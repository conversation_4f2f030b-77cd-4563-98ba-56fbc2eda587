import { use<PERSON>allback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { 
  DEFAULT_STATUS_FILTERS, 
  URL_PARAM_NAMES, 
  LEGACY_PARAM_MAPPINGS,
  splitStatusFilters 
} from '@/constants/status-filters';
import { env } from '@/config/environment';

// Shallow equality helper for arrays
const arraysEqual = (a: string[], b: string[]): boolean => {
  return a.length === b.length && a.every((val, i) => val === b[i]);
};

export interface ValidationLists {
  /** 
   * Available clients for validation. 
   * - undefined: unknown yet, accept all values provisionally
   * - empty array: known to be empty, reject all values
   * - populated array: validate against list
   */
  clients?: Array<{ clientId: string }> | null;
  /** 
   * Available entities for validation.
   * - undefined: unknown yet, accept all values provisionally  
   * - empty array: known to be empty, reject all values
   * - populated array: validate against list
   */
  entities?: Array<{ entityId: string }> | null;
  /** 
   * Available status filters for validation.
   * - undefined: unknown yet, accept all values provisionally
   * - empty array: known to be empty, reject all values
   * - populated array: validate against list
   */
  statusFilters?: string[] | null;
}

export interface URLSyncState {
  clientId: string | null;
  entityId: string | 'all' | null;
  statusFilters: string[];
}

export interface URLSyncHandlers {
  onClientChange: (clientId: string | null) => void;
  onEntityChange: (entityId: string | 'all' | null) => void;
  onStatusFiltersChange: (filters: string[]) => void;
}

export interface URLSyncOptions {
  // Current state values from Zustand store
  currentState: URLSyncState;
  
  // Change handlers to update Zustand store
  handlers: URLSyncHandlers;
  
  // Validation lists (optional - unknown values allowed until populated)
  validationLists?: ValidationLists;
  
  // Default values for resets
  defaults?: {
    entityId?: string | 'all';
    statusFilters?: string[];
  };
  
  // Configuration
  debounceMs?: number;
  enableCascadingReset?: boolean;
  enableDebug?: boolean;
  
  // Custom parameter names (optional)
  paramNames?: {
    clientId?: string;
    entityId?: string;
    status?: string;
  };
}

export interface URLSyncResult {
  isHydrated: boolean;
  announceMessage: string | null;
  clearAnnouncement: () => void;
}

/**
 * Self-contained URL synchronization hook with proper precedence and validation
 * 
 * Precedence: URL params (highest) → Zustand state → localStorage defaults (lowest)
 * 
 * Features:
 * - Canonical camelCase params (clientId, entityId, status) 
 * - Legacy snake_case support on read (client_id, status_filter)
 * - Validation with cascading resets
 * - Debounced URL updates (300ms default)
 * - isHydrated flag to prevent premature API calls
 * - Optional debug logging
 * - Accessibility announcements for filter changes
 */
export function useURLSync({
  currentState,
  handlers,
  validationLists,
  defaults = {
    entityId: 'all',
    statusFilters: [...DEFAULT_STATUS_FILTERS]
  },
  debounceMs = 300,
  enableCascadingReset = true,
  enableDebug = false,
  paramNames = {
    clientId: URL_PARAM_NAMES.CLIENT_ID,
    entityId: URL_PARAM_NAMES.ENTITY_ID,
    status: URL_PARAM_NAMES.STATUS,
  }
}: URLSyncOptions): URLSyncResult {
  const location = useLocation();
  const navigate = useNavigate();
  
  // Internal state
  const [isHydrated, setIsHydrated] = useState(false);
  const [announceMessage, setAnnounceMessage] = useState<string | null>(null);
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isInitializedRef = useRef(false);
  const lastURLRef = useRef<string>('');
  const lastStateRef = useRef<URLSyncState>({
    clientId: null,
    entityId: null,
    statusFilters: []
  });
  
  // Debug logging helper using standardized env utility
  const debug = useCallback((...args: any[]) => {
    if (enableDebug) {
      env.logVerbose('[useURLSync]', ...args);
    }
  }, [enableDebug]);
  
  // Clear announcement helper
  const clearAnnouncement = useCallback(() => {
    setAnnounceMessage(null);
  }, []);
  
  // Parse URL parameters with legacy support
  const parseURLParams = useCallback(() => {
    const searchParams = new URLSearchParams(location.search);
    const params: Record<string, string | null> = {};
    
    // Parse all parameters, handling legacy names
    for (const [key, value] of searchParams.entries()) {
      const normalizedKey = LEGACY_PARAM_MAPPINGS[key as keyof typeof LEGACY_PARAM_MAPPINGS] || key;
      params[normalizedKey] = value;
    }
    
    return {
      clientId: params[paramNames.clientId!] || null,
      entityId: params[paramNames.entityId!] || null,
      statusParam: params[paramNames.status!] || null
    };
  }, [location.search, paramNames]);
  
  // Optimized validation helpers with Set-based lookups for O(1) validation performance
  const clientIdSet = useMemo(() => {
    const clients = validationLists?.clients;
    if (!clients || clients.length === 0) return null;
    return new Set(clients.map(c => c.clientId));
  }, [validationLists?.clients]);
  
  const validateClientId = useCallback((clientId: string): boolean => {
    const clients = validationLists?.clients;
    if (clients === undefined) return true; // Unknown yet, accept provisionally
    if (clients === null || clients.length === 0) return false; // Known empty, reject all
    return clientIdSet ? clientIdSet.has(clientId) : clients.some(c => c.clientId === clientId);
  }, [validationLists?.clients, clientIdSet]);
  
  const entityIdSet = useMemo(() => {
    const entities = validationLists?.entities;
    if (!entities || entities.length === 0) return null;
    return new Set(entities.map(e => e.entityId));
  }, [validationLists?.entities]);
  
  const validateEntityId = useCallback((entityId: string): boolean => {
    if (entityId === 'all') return true;
    const entities = validationLists?.entities;
    if (entities === undefined) return true; // Unknown yet, accept provisionally
    if (entities === null || entities.length === 0) return false; // Known empty, reject all
    return entityIdSet ? entityIdSet.has(entityId) : entities.some(e => e.entityId === entityId);
  }, [validationLists?.entities, entityIdSet]);
  
  const statusFilterSet = useMemo(() => {
    const statusFilters = validationLists?.statusFilters;
    if (!statusFilters || statusFilters.length === 0) return null;
    return new Set(statusFilters);
  }, [validationLists?.statusFilters]);
  
  const validateStatusFilter = useCallback((status: string): boolean => {
    const statusFilters = validationLists?.statusFilters;
    if (statusFilters === undefined) return true; // Unknown yet, accept provisionally
    if (statusFilters === null || statusFilters.length === 0) return false; // Known empty, reject all
    return statusFilterSet ? statusFilterSet.has(status) : statusFilters.includes(status);
  }, [validationLists?.statusFilters, statusFilterSet]);
  
  // Apply validation and cascading resets
  const applyValidationAndResets = useCallback((urlParams: {
    clientId: string | null;
    entityId: string | null;
    statusFilters: string[];
  }) => {
    debug('Applying validation and resets', urlParams);
    
    let { clientId, entityId, statusFilters } = urlParams;
    let hasResets = false;
    const announcements: string[] = [];
    const lastState = lastStateRef.current;
    
    // Validate clientId
    const clientValid = !clientId || validateClientId(clientId);
    if (clientId && !clientValid) {
      debug('Invalid clientId, resetting to null:', clientId);
      clientId = null;
      hasResets = true;
      // Only announce if client actually changed
      if (lastState.clientId !== null) {
        announcements.push('Invalid client selection cleared');
      }
    }
    
    // Validate entityId (with cascading reset if client was invalid)
    const entityValid = !entityId || validateEntityId(entityId);
    if (entityId && !entityValid) {
      debug('Invalid entityId, resetting to default:', entityId);
      entityId = defaults.entityId!;
      hasResets = true;
      // Only announce if entity actually changed
      if (lastState.entityId !== defaults.entityId!) {
        announcements.push('Invalid entity selection reset to default');
      }
    } else if (enableCascadingReset && clientId && !clientValid) {
      // Cascade: invalid client resets entity
      debug('Cascading reset: client invalid, resetting entity');
      entityId = defaults.entityId!;
      hasResets = true;
      // Only announce if entity actually changed
      if (lastState.entityId !== defaults.entityId!) {
        announcements.push('Entity reset due to invalid client');
      }
    }
    
    // Validate statusFilters (with cascading reset if client/entity invalid)
    const validStatusFilters = statusFilters.filter(validateStatusFilter);
    const statusFiltersValid = validStatusFilters.length === statusFilters.length;
    
    if (!statusFiltersValid) {
      debug('Some status filters invalid, keeping valid ones:', { statusFilters, validStatusFilters });
      statusFilters = validStatusFilters.length > 0 ? validStatusFilters : defaults.statusFilters!;
      hasResets = true;
      // Only announce if filters actually changed
      if (!arraysEqual(lastState.statusFilters, statusFilters)) {
        announcements.push('Invalid status filters removed');
      }
    } else if (enableCascadingReset && (clientId && !clientValid || entityId && !entityValid)) {
      // Cascade: invalid client/entity resets status filters
      debug('Cascading reset: client/entity invalid, resetting status filters');
      statusFilters = defaults.statusFilters!;
      hasResets = true;
      // Only announce if filters actually changed
      if (!arraysEqual(lastState.statusFilters, statusFilters)) {
        announcements.push('Status filters reset due to invalid selection');
      }
    }
    
    // Update last state reference
    lastStateRef.current = { clientId, entityId, statusFilters };
    
    // Set announcement if resets occurred and state actually changed
    if (announcements.length > 0) {
      setAnnounceMessage(announcements.join('. '));
    }
    
    return { clientId, entityId, statusFilters, hasResets };
  }, [validateClientId, validateEntityId, validateStatusFilter, defaults, enableCascadingReset, debug]);
  
  // Memoized URL parameters building
  const urlParams = useMemo(() => {
    const newParams = new URLSearchParams();
    
    // Add parameters using canonical names
    if (currentState.clientId) {
      newParams.set(paramNames.clientId!, currentState.clientId);
    }
    
    if (currentState.entityId && currentState.entityId !== 'all') {
      newParams.set(paramNames.entityId!, currentState.entityId);
    }
    
    if (currentState.statusFilters.length > 0) {
      newParams.set(paramNames.status!, currentState.statusFilters.join(','));
    }
    
    return newParams;
  }, [currentState.clientId, currentState.entityId, currentState.statusFilters, paramNames]);

  // Debounced URL update
  const updateURL = useCallback(() => {
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
    }
    
    const delay = isInitializedRef.current ? debounceMs : 0;
    
    updateTimeoutRef.current = setTimeout(() => {
      const newSearch = urlParams.toString();
      const newURL = `${location.pathname}${newSearch ? `?${newSearch}` : ''}`;
      
      // Only update if URL actually changed
      if (newURL !== lastURLRef.current) {
        debug('Updating URL:', newURL);
        lastURLRef.current = newURL;
        navigate(newURL, { replace: true });
      }
    }, delay);
  }, [urlParams, location.pathname, navigate, debounceMs, debug]);
  
  // Initial hydration from URL (runs before first paint)
  useLayoutEffect(() => {
    debug('Starting hydration from URL:', location.search);
    
    const urlParams = parseURLParams();
    const statusFilters = splitStatusFilters(urlParams.statusParam);
    
    debug('Parsed URL params:', { ...urlParams, statusFilters });
    debug('Current state before hydration:', currentState);
    
    // Check if URL has explicit parameters (URL takes precedence over localStorage)
    const hasURLClient = urlParams.clientId !== null;
    const hasURLEntity = urlParams.entityId !== null;
    const hasURLStatus = urlParams.statusParam !== null;
    
    if (hasURLClient || hasURLEntity || hasURLStatus) {
      debug('URL parameters found, forcing override of localStorage values');
    }
    
    // Apply validation and get clean values
    const validated = applyValidationAndResets({
      clientId: urlParams.clientId,
      entityId: urlParams.entityId,
      statusFilters
    });
    
    debug('Validated params:', validated);
    
    // Apply changes to state - only when there are URL params or validation resets occurred
    let hasChanges = false;
    
    // Only update if URL had client parameter OR validation reset the value
    if (hasURLClient) {
      debug('Setting clientId from URL parameter:', validated.clientId, 'was:', currentState.clientId);
      handlers.onClientChange(validated.clientId);
      hasChanges = true;
    } else if (validated.hasResets && validated.clientId !== currentState.clientId) {
      debug('Setting clientId from validation reset:', validated.clientId, 'was:', currentState.clientId);
      handlers.onClientChange(validated.clientId);
      hasChanges = true;
    }
    
    // Only update if URL had entity parameter OR validation reset the value
    if (hasURLEntity) {
      debug('Setting entityId from URL parameter:', validated.entityId, 'was:', currentState.entityId);
      handlers.onEntityChange(validated.entityId);
      hasChanges = true;
    } else if (validated.hasResets && validated.entityId !== currentState.entityId) {
      debug('Setting entityId from validation reset:', validated.entityId, 'was:', currentState.entityId);
      handlers.onEntityChange(validated.entityId);
      hasChanges = true;
    }
    
    // Only update if URL had status parameter OR validation reset the value
    const currentStatusString = currentState.statusFilters.join(',');
    const validatedStatusString = validated.statusFilters.join(',');
    if (hasURLStatus) {
      debug('Setting statusFilters from URL parameter:', validated.statusFilters, 'was:', currentState.statusFilters);
      handlers.onStatusFiltersChange(validated.statusFilters);
      hasChanges = true;
    } else if (validated.hasResets && validatedStatusString !== currentStatusString) {
      debug('Setting statusFilters from validation reset:', validated.statusFilters, 'was:', currentState.statusFilters);
      handlers.onStatusFiltersChange(validated.statusFilters);
      hasChanges = true;
    }
    
    // Set hydrated state after micro-task to allow state updates to complete
    Promise.resolve().then(() => {
      debug('Hydration complete, hasChanges:', hasChanges);
      setIsHydrated(true);
      isInitializedRef.current = true;
      lastURLRef.current = `${location.pathname}${location.search}`;
    });
    
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Only run on mount
  
  // Update URL when state changes (after hydration)
  useEffect(() => {
    if (isInitializedRef.current) {
      debug('State changed, updating URL');
      updateURL();
    }
  }, [updateURL]);

  // Re-validate when validation lists update
  useEffect(() => {
    if (!isInitializedRef.current) return;
    
    debug('Validation lists updated, re-checking current state');
    
    // Apply validation and resets to current state
    const validated = applyValidationAndResets({
      clientId: currentState.clientId,
      entityId: currentState.entityId,
      statusFilters: currentState.statusFilters
    });
    
    // Apply changes if validation triggered resets
    if (validated.hasResets) {
      if (validated.clientId !== currentState.clientId) {
        handlers.onClientChange(validated.clientId);
      }
      if (validated.entityId !== currentState.entityId) {
        handlers.onEntityChange(validated.entityId);
      }
      const currentStatusString = currentState.statusFilters.join(',');
      const validatedStatusString = validated.statusFilters.join(',');
      if (validatedStatusString !== currentStatusString) {
        handlers.onStatusFiltersChange(validated.statusFilters);
      }
    }
  }, [validationLists, applyValidationAndResets, currentState, handlers, debug]);
  
  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, []);
  
  return {
    isHydrated,
    announceMessage,
    clearAnnouncement
  };
}