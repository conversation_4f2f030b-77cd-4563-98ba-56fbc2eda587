import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import type { SupplierNode, ExpandedState, HierarchicalBillsData } from '../types/hierarchical-bills.types';
import type { SelectedSummary } from './useSelection';
import type { AmortizationConfig } from './useAmortizationConfig';
import type { PaginationState } from './useBillsData';
import type { MonthlyScheduleEntry } from '../types/schedule.types';

export interface UseKeyboardNavigationOptions {
  enabled?: boolean;
  resetOnDataChange?: boolean;
}

export interface UseKeyboardNavigationReturn {
  focusedInvoiceIndex: number;
  navigationMode: 'invoice' | 'lineitem';
  focusedLineItemIndex: number;
  showKeyboardHelp: boolean;
  setShowKeyboardHelp: (show: boolean) => void;
  resetNavigation: () => void;
}

export function useKeyboardNavigation(
  filteredSuppliers: SupplierNode[],
  expandedState: ExpandedState,
  selectedSummary: SelectedSummary,
  amortizationConfig: AmortizationConfig,
  monthlySchedule: MonthlyScheduleEntry[],
  handleToggleExpand: (type: 'supplier' | 'invoice', id: string) => void,
  handleToggleSelection: (type: 'supplier' | 'invoice' | 'lineItem', id: string) => void,
  setHierarchicalData: React.Dispatch<React.SetStateAction<HierarchicalBillsData>>,
  handleLoadMore: () => Promise<void>,
  pagination: PaginationState,
  onPostReady?: () => void,
  onSaveChanges?: () => void,
  enabled = true,
  resetOnDataChange = true
): UseKeyboardNavigationReturn {

  // Navigation state
  const [focusedInvoiceIndex, setFocusedInvoiceIndex] = useState<number>(-1);
  const [navigationMode, setNavigationMode] = useState<'invoice' | 'lineitem'>('invoice');
  const [focusedLineItemIndex, setFocusedLineItemIndex] = useState<number>(-1);
  const [showKeyboardHelp, setShowKeyboardHelp] = useState(false);

  // Reset navigation state
  const resetNavigation = useCallback(() => {
    setFocusedInvoiceIndex(-1);
    setNavigationMode('invoice');
    setFocusedLineItemIndex(-1);
  }, []);

  // Get all invoices for navigation
  const allInvoices = useMemo(() => {
    return filteredSuppliers.flatMap(supplier => supplier.invoices);
  }, [filteredSuppliers]);

  // One-shot guard for initialization - survives renders to prevent re-initialization
  const hasInitialised = useRef(false);

  // Auto-expand supplier and scroll to focused invoice
  useEffect(() => {
    if (!enabled || focusedInvoiceIndex < 0) return;

    const focusedInvoice = allInvoices[focusedInvoiceIndex];
    if (!focusedInvoice) return;
    
    // Find which supplier contains this invoice
    const supplierWithInvoice = filteredSuppliers.find(supplier => 
      supplier.invoices.some(invoice => invoice.invoiceId === focusedInvoice.invoiceId)
    );
    
    if (!supplierWithInvoice) return;

    // Auto-expand the supplier if it's not already expanded
    const wasExpanded = expandedState.suppliers.has(supplierWithInvoice.supplierId);
    if (!wasExpanded) {
      console.log('Auto-expanding supplier:', supplierWithInvoice.supplierName);
      handleToggleExpand('supplier', supplierWithInvoice.supplierId);
    }
    
    // Scroll to focused invoice (delay for expansion to complete)
    setTimeout(() => {
      const invoiceElement = document.querySelector(`[data-invoice-id="${focusedInvoice.invoiceId}"]`);
      if (invoiceElement) {
        console.log('Scrolling to focused invoice:', focusedInvoice.invoiceId);
        invoiceElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'nearest'
        });
      }
    }, wasExpanded ? 0 : 300); // Delay if we just expanded
  }, [focusedInvoiceIndex, allInvoices, filteredSuppliers, expandedState.suppliers, enabled]); // eslint-disable-line react-hooks/exhaustive-deps

  // Initialize focus to first invoice and expand first supplier when data loads
  useEffect(() => {
    if (!enabled || !resetOnDataChange) return;

    if (allInvoices.length > 0 && !hasInitialised.current) {
      // One-shot initialization
      hasInitialised.current = true;
      setFocusedInvoiceIndex(0);
      console.log('Focusing first invoice for navigation');
      
      // Auto-expand the first supplier so the first invoice is visible
      if (filteredSuppliers.length > 0) {
        const firstSupplier = filteredSuppliers[0];
        if (!expandedState.suppliers.has(firstSupplier.supplierId)) {
          console.log('Auto-expanding first supplier for immediate navigation');
          handleToggleExpand('supplier', firstSupplier.supplierId);
        }
      }
    } else if (allInvoices.length === 0) {
      // Reset when no invoices available
      hasInitialised.current = false;
      setFocusedInvoiceIndex(-1);
    }
  }, [allInvoices.length, enabled, resetOnDataChange]); // Only depend on stable primitives

  // Validation helper functions
  const hasUnpostedEntries = useCallback((schedule: MonthlyScheduleEntry[]): boolean => {
    return schedule.some(entry => entry.status !== 'posted');
  }, []);

  const hasMandatoryFieldsPopulated = useCallback((config: AmortizationConfig): boolean => {
    return !!(config.prepaymentAccount && config.expenseAccount);
  }, []);

  const amortizationCoversTotal = useCallback((schedule: MonthlyScheduleEntry[], totalAmount: number): boolean => {
    if (schedule.length === 0 || totalAmount === 0) return false;
    
    const scheduleTotal = schedule.reduce((sum: number, entry: MonthlyScheduleEntry) => sum + entry.amount, 0);
    // Allow small rounding differences (within 0.01)
    return Math.abs(scheduleTotal - totalAmount) <= 0.01;
  }, []);

  // Memoized keyboard navigation handler to fix event listener churn
  const handleKeyboardNavigation = useCallback((event: KeyboardEvent) => {
    if (!enabled) return;

    // Don't interfere with form inputs
    if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
      return;
    }

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        if (navigationMode === 'invoice') {
          // Navigate between invoices or enter line item mode
          if (allInvoices.length > 0) {
            if (focusedInvoiceIndex >= 0 && focusedInvoiceIndex < allInvoices.length) {
              const focusedInvoice = allInvoices[focusedInvoiceIndex];
              const isExpanded = expandedState.invoices.has(focusedInvoice.invoiceId);
              
              if (isExpanded && focusedInvoice.lineItems.length > 0) {
                // Enter line item navigation mode
                setNavigationMode('lineitem');
                setFocusedLineItemIndex(0);
                console.log('Arrow Down: Entering line item navigation mode');
              } else {
                // Navigate to next invoice
                setFocusedInvoiceIndex(prev => {
                  const newIndex = prev >= allInvoices.length - 1 ? allInvoices.length - 1 : prev + 1;
                  console.log('Arrow Down: Focus moving to invoice index:', newIndex);
                  
                  // Auto-load more data when approaching the end
                  if (newIndex >= allInvoices.length - 3 && pagination.hasMore && !pagination.isLoadingMore) {
                    console.log('Auto-loading more data due to keyboard navigation');
                    handleLoadMore();
                  }
                  
                  return newIndex;
                });
              }
            } else {
              // No invoice focused, focus first one
              setFocusedInvoiceIndex(0);
              console.log('Arrow Down: Focus moving to invoice index: 0');
            }
          }
        } else if (navigationMode === 'lineitem') {
          // Navigate within line items
          if (focusedInvoiceIndex >= 0 && focusedInvoiceIndex < allInvoices.length) {
            const focusedInvoice = allInvoices[focusedInvoiceIndex];
            if (focusedLineItemIndex < focusedInvoice.lineItems.length - 1) {
              setFocusedLineItemIndex(prev => prev + 1);
              console.log('Arrow Down: Focus moving to line item index:', focusedLineItemIndex + 1);
            } else {
              // At last line item, move to next invoice
              setNavigationMode('invoice');
              setFocusedLineItemIndex(-1);
              setFocusedInvoiceIndex(prev => {
                const newIndex = prev >= allInvoices.length - 1 ? allInvoices.length - 1 : prev + 1;
                console.log('Arrow Down: Exiting line items, moving to invoice index:', newIndex);
                return newIndex;
              });
            }
          }
        }
        break;
        
      case 'ArrowUp':
        event.preventDefault();
        if (navigationMode === 'invoice') {
          // Navigate between invoices
          if (allInvoices.length > 0) {
            setFocusedInvoiceIndex(prev => {
              const newIndex = prev <= 0 ? 0 : prev - 1;
              console.log('Arrow Up: Focus moving to invoice index:', newIndex);
              return newIndex;
            });
          }
        } else if (navigationMode === 'lineitem') {
          // Navigate within line items or return to invoice level
          if (focusedInvoiceIndex >= 0 && focusedInvoiceIndex < allInvoices.length) {
            if (focusedLineItemIndex <= 0) {
              // Return to invoice level navigation
              setNavigationMode('invoice');
              setFocusedLineItemIndex(-1);
              console.log('Arrow Up: Returning to invoice navigation mode');
            } else {
              // Move to previous line item
              setFocusedLineItemIndex(prev => prev - 1);
              console.log('Arrow Up: Focus moving to line item index:', focusedLineItemIndex - 1);
            }
          }
        }
        break;
        
      case 'Enter':
        event.preventDefault();
        if (event.ctrlKey || event.metaKey) {
          // Ctrl+Enter for Post Ready
          const canPost = selectedSummary.hasSelection &&
            hasUnpostedEntries(monthlySchedule) && 
            hasMandatoryFieldsPopulated(amortizationConfig) && 
            amortizationCoversTotal(monthlySchedule, selectedSummary.totalAmount);
          if (canPost && onPostReady) {
            onPostReady();
          }
        }
        break;
        
      case 'ArrowLeft':
      case 'ArrowRight':
        event.preventDefault();
        // Left/Right arrows for invoice expand/collapse
        if (focusedInvoiceIndex >= 0 && focusedInvoiceIndex < allInvoices.length) {
          const focusedInvoice = allInvoices[focusedInvoiceIndex];
          handleToggleExpand('invoice', focusedInvoice.invoiceId);
        }
        break;
        
      case ' ': // Space
        event.preventDefault();
        if (focusedInvoiceIndex >= 0 && focusedInvoiceIndex < allInvoices.length) {
          const focusedInvoice = allInvoices[focusedInvoiceIndex];
          
          if (navigationMode === 'invoice') {
            // Toggle selection of all line items in focused invoice
            handleToggleSelection('invoice', focusedInvoice.invoiceId);
          } else if (navigationMode === 'lineitem' && focusedLineItemIndex >= 0) {
            // Toggle selection of specific line item
            if (focusedLineItemIndex < focusedInvoice.lineItems.length) {
              const lineItem = focusedInvoice.lineItems[focusedLineItemIndex];
              handleToggleSelection('lineItem', lineItem.lineItemId);
            }
          }
        }
        break;
        
      case 'Escape':
        event.preventDefault();
        if (navigationMode === 'lineitem') {
          // Return to invoice navigation mode
          setNavigationMode('invoice');
          setFocusedLineItemIndex(-1);
          console.log('Escape: Returning to invoice navigation mode');
        } else {
          // Clear focus and all selections
          setFocusedInvoiceIndex(-1);
          setNavigationMode('invoice');
          setFocusedLineItemIndex(-1);
          // Clear all selections
          setHierarchicalData(prev => ({
            ...prev,
            suppliers: prev.suppliers.map(supplier => ({
              ...supplier,
              isSelected: false,
              invoices: supplier.invoices.map(invoice => ({
                ...invoice,
                isSelected: false,
                lineItems: invoice.lineItems.map(lineItem => ({
                  ...lineItem,
                  isSelected: false
                }))
              }))
            }))
          }));
        }
        break;
        
      case '?':
      case '/':
        // Handle both ? and Shift+/ (which can register as either ? or /)
        if (event.key === '?' || (event.key === '/' && event.shiftKey)) {
          event.preventDefault();
          setShowKeyboardHelp(true);
        }
        break;
        
      case 's':
        if (event.ctrlKey || event.metaKey) {
          event.preventDefault();
          if (selectedSummary.hasSelection && amortizationConfig.prepaymentAccount && amortizationConfig.expenseAccount && onSaveChanges) {
            onSaveChanges();
          }
        }
        break;
        
      case 'a':
        if (event.ctrlKey || event.metaKey) {
          event.preventDefault();
          if (focusedInvoiceIndex >= 0 && focusedInvoiceIndex < allInvoices.length) {
            const focusedInvoice = allInvoices[focusedInvoiceIndex];
            // Select all line items from the focused invoice only
            focusedInvoice.lineItems.forEach(lineItem => {
              if (!lineItem.isSelected) {
                handleToggleSelection('lineItem', lineItem.lineItemId);
              }
            });
          }
        }
        break;
        
      case 'd':
        if (event.ctrlKey || event.metaKey) {
          event.preventDefault();
          // Deselect all line items
          setHierarchicalData(prev => ({
            ...prev,
            suppliers: prev.suppliers.map(supplier => ({
              ...supplier,
              invoices: supplier.invoices.map(invoice => ({
                ...invoice,
                lineItems: invoice.lineItems.map(lineItem => ({
                  ...lineItem,
                  isSelected: false
                }))
              }))
            }))
          }));
        }
        break;
    }
  }, [
    // Memoized dependencies to prevent excessive re-renders and fix event listener churn
    enabled,
    navigationMode,
    focusedInvoiceIndex,
    focusedLineItemIndex,
    allInvoices,
    expandedState.invoices,
    selectedSummary,
    amortizationConfig,
    monthlySchedule,
    pagination.hasMore,
    pagination.isLoadingMore,
    handleToggleExpand,
    handleToggleSelection,
    setHierarchicalData,
    handleLoadMore,
    onPostReady,
    onSaveChanges,
    hasUnpostedEntries,
    hasMandatoryFieldsPopulated,
    amortizationCoversTotal,
  ]);

  // Ref to always call the latest navigation handler without re-adding the listener
  const handlerRef = useRef<(e: KeyboardEvent) => void>();

  // Keep ref updated with the latest callback
  useEffect(() => {
    handlerRef.current = handleKeyboardNavigation;
  }, [handleKeyboardNavigation]);

  // Attach a single keydown listener that delegates to the ref
  useEffect(() => {
    if (!enabled) return;

    // Reset navigation once when (re)-enabling keyboard nav
    resetNavigation();

    const listener = (e: KeyboardEvent) => {
      handlerRef.current?.(e);
    };

    document.addEventListener('keydown', listener);
    return () => {
      document.removeEventListener('keydown', listener);
    };
  }, [enabled, resetNavigation]);

  return {
    focusedInvoiceIndex,
    navigationMode,
    focusedLineItemIndex,
    showKeyboardHelp,
    setShowKeyboardHelp,
    resetNavigation,
  };
}