import React, { useMemo } from 'react';
import { AutocompletePopover, type AutocompleteItem } from './autocomplete-popover';

interface Client {
  clientId: string;
  clientName: string;
}

interface ClientSelectorProps {
  clients: Client[];
  selectedClientId: string | null;
  onClientSelect: (clientId: string) => void;
  isLoading?: boolean;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  isHydrated?: boolean;
}

export function ClientSelector({
  clients,
  selectedClientId,
  onClientSelect,
  isLoading = false,
  disabled = false,
  placeholder = 'Select client...',
  className,
  isHydrated = true,
}: ClientSelectorProps) {
  
  // Transform clients into AutocompleteItem format
  const items = useMemo(() => {
    // Sort clients alphabetically
    const sortedClients = [...clients].sort((a, b) => a.clientName.localeCompare(b.clientName));
    
    return sortedClients.map(client => ({
      value: client.clientId,
      label: client.clientName,
    }));
  }, [clients]);

  return (
    <AutocompletePopover
      items={items}
      selectedValue={isHydrated ? selectedClientId : null}
      onSelect={onClientSelect}
      placeholder={isHydrated ? placeholder : 'Loading...'}
      searchPlaceholder="Search clients..."
      emptyMessage="No clients found."
      groupHeading="Clients"
      isLoading={isLoading || !isHydrated}
      disabled={disabled || !isHydrated}
      className={className}
      aria-label="Select client"
      aria-busy={isLoading || !isHydrated}
      screenReaderLabel="Search for client"
      buttonClassName="min-w-[150px]"
      showCheckmarks={true}
    />
  );
}