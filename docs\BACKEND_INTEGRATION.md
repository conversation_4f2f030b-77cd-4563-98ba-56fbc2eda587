# Frontend-Backend Integration Reference

**Quick reference for frontend developers working with the DRCR backend**
*Part of the DRCR full-stack documentation system*

## Backend System Overview

**Location**: `/mnt/d/Projects/drcr_back/`
**Technology**: Python FastAPI
**Local URL**: `http://localhost:8081`
**Start Command**: `cd /mnt/d/Projects/drcr_back/rest_api && python run_server.py`

## Critical API Information

### Base Configuration
```typescript
// Correct configuration for development
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081';
```

### Authentication Headers
```typescript
headers: {
  'Authorization': `Bearer ${firebaseIdToken}`,
  'Content-Type': 'application/json'
}
```

## Essential Backend Endpoints for Frontend

### Dashboard & Core Data
```typescript
// Main dashboard data
GET /transactions/dashboard
// Returns: PaginatedDashboardResponse with schedule status counts

// Client summary (most used)
GET /clients/summary  
// Returns: ClientListResponse with entities and status

// Entity details
GET /entities/{entityId}
// Returns: Entity with connection_details object
```

### Prepayments (Primary Feature)
```typescript
// Calculate amortization preview
POST /schedules/calculate-preview
// Body: { transaction_id, start_date, end_date, expense_account_code }

// Confirm schedule (main user action)
POST /schedules/{scheduleId}/confirm
// Body: { confirmed_by: userId }

// Post entries to Xero
POST /schedules/{scheduleId}/entries/bulk-post
// Body: { entry_indices: number[] }
```

### Xero Integration
```typescript
// Start OAuth flow
GET /xero/connect/initiate/{clientId}
// Returns: { auth_url: string }

// Connect organization
POST /xero/clients/{clientId}/xero/connect-organization
// Body: { xero_tenant_id: string }

// Get chart of accounts
GET /entities/{entityId}/accounts
// Returns: Account[] with code, name, type
```

## Backend Data Structures

### Entity Response Format
```typescript
// Backend returns (snake_case)
{
  entity_id: string;
  entity_name: string;  // Note: NOT "name"
  type: "xero" | "qbo" | "manual";
  status: "active" | "inactive" | "error";
  connection_details: {
    status: "active" | "error" | "disconnected";
    xero_tenant_id?: string;
    last_sync?: string;
    error_message?: string;
  }
}

// Frontend transforms to (camelCase)
{
  entityId: string;
  entityName: string;
  type: "xero" | "qbo" | "manual";
  status: "active" | "inactive" | "error";
  connection_status: "connected" | "error" | "disconnected"; // Transformed!
  xeroTenantId?: string;
  lastSync?: string;
  errorMessage?: string;
}
```

### Schedule Status System
```typescript
// Backend schedule statuses
type ScheduleStatus = 
  | "pending_configuration"  // User can configure
  | "pending_confirmation"   // User can confirm  
  | "confirmed"             // Ready for posting
  | "posted"                // Posted to Xero
  | "cancelled"             // Cancelled
  | "error";                // Error state

// Frontend action visibility
const showActionButton = [
  "pending_configuration",
  "pending_confirmation",
  "proposed"  // Legacy status
].includes(schedule.status);
```

## Backend Field Access Patterns

### Critical Rule: Use Canonical Fields
Backend uses field access helpers to handle variations:

```python
# Backend field access (DO NOT replicate in frontend)
from rest_api.utils.field_access import get_field, get_account_code

# These variations are handled automatically:
"AccountCode" → "account_code"
"Date" → "date_issued"  
"JournalLines" → "lines"
"LineAmount" → "line_amount"
```

**Frontend Impact**: Always expect snake_case from backend APIs and transform to camelCase in services.

## Error Handling

### Backend Error Format
```typescript
interface BackendError {
  detail: string;           // Human-readable message
  status_code: number;      // HTTP status code
  timestamp: number;        // Unix timestamp
  request_id?: string;      // For tracing
}
```

### Frontend Error Handling
```typescript
try {
  const result = await api.post('/endpoint', data);
  return result.data;
} catch (error) {
  // Backend errors are in error.response.data.detail
  const message = error.response?.data?.detail || 'Request failed';
  throw new Error(message);
}
```

## Common Backend Operations

### Trigger Sync
```typescript
// Trigger entity sync
POST /entities/{entityId}/sync/trigger
// Body: { sync_type: "full" | "incremental" }

// Check sync status
GET /sync/status/{syncJobId}
// Returns: { status, progress_percent, stage }
```

### User Management
```typescript
// Get current user
GET /auth/me
// Returns: { user_id, email, firm_id, role }

// List firm users
GET /auth/users?limit=25&offset=0
// Returns: PaginatedUsersResponse
```

## Backend Development Context

### Key Backend Files
- **Main API**: `/mnt/d/Projects/drcr_back/rest_api/main.py`
- **Routes**: `/mnt/d/Projects/drcr_back/rest_api/routes/`
- **Models**: `/mnt/d/Projects/drcr_back/rest_api/models/`
- **Services**: `/mnt/d/Projects/drcr_back/rest_api/services/`

### Backend Documentation
- **Complete API Reference**: `/mnt/d/Projects/drcr_back/docs/BACKEND_REFERENCE.md`
- **Database Schema**: `/mnt/d/Projects/drcr_back/docs/DATABASE_SCHEMA.md`
- **Backend AI Context**: `/mnt/d/Projects/drcr_back/CLAUDE.md`

## Database Context (Firestore)

### Collections Frontend Uses
- **TRANSACTIONS**: Bills and invoices
- **AMORTIZATION_SCHEDULES**: Prepayment schedules
- **ENTITIES**: Business entities and connections
- **CLIENTS**: Client information
- **CHART_OF_ACCOUNTS**: Account codes and names

### Field Access
Backend handles field variations automatically, but frontend should expect:
- **snake_case** field names from APIs
- **Consistent data types** (numbers as numbers, not strings)
- **Proper null handling** (empty arrays vs null)

## Performance Considerations

### API Response Times
- **Health endpoint**: ~43ms average
- **Dashboard data**: ~200ms average
- **Sync operations**: Long-running (use progress tracking)

### Optimization Tips
- **Cache GET requests** using browser cache headers
- **Use pagination** for list endpoints
- **Batch operations** where possible
- **Show loading states** for all async operations

## Testing Backend Integration

### Local Testing
```bash
# Start backend
cd /mnt/d/Projects/drcr_back/rest_api
python run_server.py

# Test endpoints
curl -H "Authorization: Bearer $FIREBASE_TOKEN" \
     http://localhost:8081/auth/me
```

### Integration Testing
- **API Contract Tests**: Verify request/response formats
- **Authentication Tests**: Test token validation
- **Error Handling Tests**: Verify error responses
- **Data Transformation Tests**: Ensure field mappings work

## Common Integration Issues

### URL Problems
❌ **Wrong**: `https://localhost:8081` (HTTPS on localhost)
✅ **Right**: `http://localhost:8081` (HTTP on localhost)

### Field Access Issues
❌ **Wrong**: Expecting `name` field for entities
✅ **Right**: Backend returns `entity_name`

### Status Mapping Issues
❌ **Wrong**: Using backend status values directly in UI
✅ **Right**: Transform `connection_details.status` to `connection_status`

### Auth Token Issues
❌ **Wrong**: Missing or expired Firebase tokens
✅ **Right**: Always refresh tokens and handle auth errors

## Quick Development Commands

### Backend Operations
```bash
# Start backend server
cd /mnt/d/Projects/drcr_back/rest_api && python run_server.py

# Run backend tests
cd /mnt/d/Projects/drcr_back && python -m pytest tests/ -v

# Check backend logs
# Look at terminal output where run_server.py is running
```

### Full-Stack Development
```bash
# Start both systems
cd /mnt/d/Projects/drcr_back/rest_api && python run_server.py &
cd /mnt/d/Projects/drcr_front && npm run dev &
```

This reference enables efficient frontend development with full backend integration awareness.