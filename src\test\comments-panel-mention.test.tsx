import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { CommentsPanel } from '@/components/common/CommentsPanel';
import { CommentsService } from '@/services/comments.service';
import { useAuthStore } from '@/store/auth.store';

// Mock the services and stores
vi.mock('@/services/comments.service');
vi.mock('@/store/auth.store');
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

const mockCommentsService = vi.mocked(CommentsService);
const mockUseAuthStore = vi.mocked(useAuthStore);

describe('CommentsPanel - Mention Bug Fix', () => {
  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();
    
    // Mock auth store
    mockUseAuthStore.mockReturnValue({
      user: { uid: 'test-user', email: '<EMAIL>' },
      isAuthenticated: true,
    } as any);
    
    // Mock comments service
    mockCommentsService.getCommentsForRecord.mockResolvedValue({
      comments: [],
      pages: 1,
      total: 0,
    });
    
    mockCommentsService.searchUsersForMention.mockResolvedValue([
      {
        uid: 'user1',
        display_name: 'Art U',
        email: '<EMAIL>',
      },
      {
        uid: 'user2', 
        display_name: 'Arthur User',
        email: '<EMAIL>',
      },
    ]);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should not trigger re-detection after mention selection', async () => {
    const { container } = render(
      <CommentsPanel
        parentType="INVOICE"
        parentId="test-invoice"
        enableMentions={true}
      />
    );

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByPlaceholderText(/write a comment/i)).toBeInTheDocument();
    });

    const textarea = screen.getByPlaceholderText(/write a comment/i) as HTMLTextAreaElement;
    
    // Simulate typing "@Art"
    fireEvent.change(textarea, { target: { value: '@Art' } });
    
    // Set cursor position after "@Art"
    Object.defineProperty(textarea, 'selectionStart', {
      value: 4,
      writable: true,
    });
    
    // Wait for mention search to trigger
    await waitFor(() => {
      expect(mockCommentsService.searchUsersForMention).toHaveBeenCalledWith('Art');
    });

    // Wait for mention dropdown to appear
    await waitFor(() => {
      expect(screen.getByText('Art U')).toBeInTheDocument();
    });

    // Clear the search mock call count
    mockCommentsService.searchUsersForMention.mockClear();

    // Click on "Art U" mention
    fireEvent.click(screen.getByText('Art U'));

    // Wait a bit for all async operations to complete
    await waitFor(() => {
      expect(textarea.value).toBe('@Art U ');
    }, { timeout: 100 });

    // Verify that searchUsersForMention was NOT called again after selection
    expect(mockCommentsService.searchUsersForMention).not.toHaveBeenCalled();
    
    // Verify the mention dropdown is closed
    expect(screen.queryByText('Art U')).not.toBeInTheDocument();
  });

  it('should still detect new mentions when user types normally', async () => {
    const { container } = render(
      <CommentsPanel
        parentType="INVOICE"
        parentId="test-invoice"
        enableMentions={true}
      />
    );

    const textarea = screen.getByPlaceholderText(/write a comment/i) as HTMLTextAreaElement;
    
    // Simulate typing a new mention after some text
    fireEvent.change(textarea, { target: { value: 'Hello @Art' } });
    
    // Set cursor position after "@Art"
    Object.defineProperty(textarea, 'selectionStart', {
      value: 10,
      writable: true,
    });
    
    // Wait for mention search to trigger
    await waitFor(() => {
      expect(mockCommentsService.searchUsersForMention).toHaveBeenCalledWith('Art');
    });

    // Verify mention dropdown appears
    await waitFor(() => {
      expect(screen.getByText('Art U')).toBeInTheDocument();
    });
  });
});
