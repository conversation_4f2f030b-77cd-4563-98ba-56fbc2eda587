# Service Layer Guide

*Last verified: 2025-01-15 against actual service implementations*

## Overview

The DRCR frontend uses a service layer pattern with 9 dedicated services for different domains. All services follow consistent patterns for error handling, loading states, and type safety.

## Service Architecture

### Design Principles
- **Domain Separation**: Each service handles a specific business domain
- **Consistent Error Handling**: All services throw descriptive Error objects
- **Type Safety**: Full TypeScript integration with proper interfaces
- **Centralized API**: All services use the shared `api` client from `@/lib/api`
- **Authentication**: Automatic token handling via API client

### Common Patterns

#### Standard Service Method
```typescript
static async methodName(params: ParamsType): Promise<ReturnType> {
  try {
    const response = await api.someMethod(params);
    return response;
  } catch (error) {
    console.error('Error in methodName:', error);
    throw new Error('User-friendly error message');
  }
}
```

#### Usage with Loading States
```typescript
const [loading, setLoading] = useState(false);
const [data, setData] = useState(null);

const fetchData = async () => {
  setLoading(true);
  try {
    const result = await ServiceName.methodName(params);
    setData(result);
  } catch (error) {
    toast.error(error.message);
  } finally {
    setLoading(false);
  }
};
```

## Services Reference

### 1. AuthService (`src/services/auth.service.ts`)

**Purpose:** Firebase authentication wrapper

**Methods:**
```typescript
// Authentication operations
static async signIn(email: string, password: string): Promise<UserCredential>
static async signUp(email: string, password: string): Promise<UserCredential>
static async signOut(): Promise<void>

// Error handling
static handleFirebaseError(error: any): string
```

**Usage Example:**
```typescript
import { AuthService } from '@/services/auth.service';

try {
  const userCredential = await AuthService.signIn(email, password);
  // Handle successful login
} catch (error) {
  const errorMessage = AuthService.handleFirebaseError(error);
  toast.error(errorMessage);
}
```

### 2. ClientsService (`src/services/clients.service.ts`)

**Purpose:** Client management operations

**Key Methods:**
```typescript
// Client listing
static async getClients(): Promise<ClientSummary[]>
static async getClientsEnhanced(filters?: ClientFilters): Promise<ClientListResponse>

// Client creation
static async createClient(data: CreateClientData): Promise<{ message: string; client_id: string }>
static async createClientEnhanced(data: ClientCreate): Promise<CreateClientResponse>
static async createClientFromWizard(step1, step2, step3): Promise<CreateClientResponse>

// Client details
static async getClient(clientId: string): Promise<ClientSummary>
static async getClientDetails(clientId: string): Promise<ClientResponse>

// Client updates
static async updateClient(clientId: string, data: UpdateClientData): Promise<void>
static async updateClientEnhanced(clientId: string, data: ClientUpdate): Promise<UpdateClientResponse>

// Client deletion
static async deleteClient(clientId: string): Promise<DeleteClientResponse>

// Form helpers
static async getClientEnums(): Promise<ClientEnums>
```

**Usage Example:**
```typescript
import { ClientsService } from '@/services/clients.service';

// Get clients with pagination
const response = await ClientsService.getClientsEnhanced({
  page: 1,
  limit: 10,
  clientFilter: 'search term'
});

// Create new client
const newClient = await ClientsService.createClient({
  name: 'Client Name',
  contact_email: '<EMAIL>'
});
```

### 3. DashboardService (`src/services/dashboard.service.ts`)

**Purpose:** Dashboard data aggregation

**Methods:**
```typescript
// Firm-level dashboard
static async getFirmDashboard(filters?: FirmDashboardFilters): Promise<FirmDashboardResponse>

// Client-specific dashboard
static async getClientDashboard(clientId: string, entityId?: string): Promise<DashboardData>
```

**Usage Example:**
```typescript
import { DashboardService } from '@/services/dashboard.service';

// Get firm dashboard with filters
const dashboardData = await DashboardService.getFirmDashboard({
  page: 1,
  limit: 20,
  clientFilter: 'active clients'
});
```

### 4. EntitiesService (`src/services/entities.service.ts`)

**Purpose:** Entity (organization) management

**Methods:**
```typescript
// Entity listing
static async getEntitiesForClient(clientId: string): Promise<EntityResponse[]>

// Entity details
static async getEntity(entityId: string, disableCache?: boolean): Promise<EntityResponse>

// Entity creation
static async createEntityFromWizard(step1: EntityWizardStep1, step2: EntityWizardStep2): Promise<any>

// Settings management
static async updateEntitySettings(entityId: string, settings: any): Promise<any>

// Connection management
static async checkEntityConnectionStatus(entityId: string): Promise<any>
static async disconnectEntity(entityId: string): Promise<any>
```

**Usage Example:**
```typescript
import { EntitiesService } from '@/services/entities.service';

// Get entities for a client
const entities = await EntitiesService.getEntitiesForClient(clientId);

// Update entity settings
await EntitiesService.updateEntitySettings(entityId, {
  prepayment_asset_account_codes: ['1200', '1210'],
  auto_sync_enabled: true
});
```

### 5. PrepaymentsService (`src/services/prepayments.service.ts`)

**Purpose:** Prepayment and amortization schedule management (Primary service)

**Data Fetching:**
```typescript
static async getPrepaymentsData(filters: PrepaymentsFilters, disableCache?: boolean): Promise<PrepaymentsResponse>
```

**Schedule Operations:**
```typescript
static async getSchedule(scheduleId: string): Promise<any>
static async updateSchedule(scheduleId: string, data: Partial<ScheduleData>): Promise<any>
static async confirmSchedule(scheduleId: string): Promise<void>
static async skipSchedule(scheduleId: string, reason: string): Promise<void>
```

**Schedule Calculations:**
```typescript
static async calculatePreview(data: {
  amount: number;
  start_date: string;
  end_date: string;
  calculation_method: 'auto' | 'day_based' | 'equal_monthly';
  entity_id: string;
}, options?: { signal?: AbortSignal }): Promise<PreviewResponse>

static async recalculateSchedule(scheduleId: string, data: any): Promise<any>
static async previewChanges(scheduleId: string, data: any): Promise<any>
```

**Monthly Entry Operations:**
```typescript
static async updateMonthlyEntry(scheduleId: string, entryIndex: number, amount: number): Promise<any>
```

**Xero Posting Operations:**
```typescript
static async postReadyEntries(scheduleId: string, entryIndices?: number[]): Promise<any>
static async postSingleEntry(scheduleId: string, entryIndex: number): Promise<any>
static async markScheduleReady(scheduleId: string, months?: string[]): Promise<{ message: string }>
```

**Attachment Handling:**
```typescript
static async getAttachmentUrl(attachmentId: string): Promise<string>
```

**Usage Example:**
```typescript
import { PrepaymentsService } from '@/services/prepayments.service';

// Get prepayments data with filtering
const prepaymentsData = await PrepaymentsService.getPrepaymentsData({
  client_id: 'client123',
  entity_id: 'entity456',
  status_filters: ['pending_configuration', 'proposed'],
  page: 1,
  limit: 20
});

// Calculate amortization preview
const preview = await PrepaymentsService.calculatePreview({
  amount: 12000,
  start_date: '2024-01-01',
  end_date: '2024-12-31',
  calculation_method: 'equal_monthly',
  entity_id: 'entity456'
});

// Update individual monthly entry
await PrepaymentsService.updateMonthlyEntry(scheduleId, 0, 1250.50);

// Post entries to Xero
await PrepaymentsService.postReadyEntries(scheduleId, [0, 1, 2]);
```

### 6. UsersService (`src/services/users.service.ts`)

**Purpose:** User management operations

**Methods:**
```typescript
// User profile operations
static async getUserProfile(userId: string): Promise<UserProfile>
static async updateUserProfile(userId: string, data: any): Promise<any>
```

**Usage Example:**
```typescript
import { UsersService } from '@/services/users.service';

// Get user profile
const profile = await UsersService.getUserProfile(userId);

// Update user profile
await UsersService.updateUserProfile(userId, {
  display_name: 'New Name',
  preferences: { theme: 'dark' }
});
```

### 7. FirmService (`src/services/firm.service.ts`)

**Purpose:** Firm-level operations

**Methods:**
```typescript
// Firm management
static async getFirmDetails(): Promise<FirmDetails>
static async updateFirmSettings(settings: any): Promise<any>
```

**Usage Example:**
```typescript
import { FirmService } from '@/services/firm.service';

// Get firm details
const firmDetails = await FirmService.getFirmDetails();

// Update firm settings
await FirmService.updateFirmSettings({
  default_currency: 'USD',
  timezone: 'America/New_York'
});
```

### 8. PostProcessingService (`src/services/post-processing.service.ts`)

**Purpose:** Post-processing operations for schedules

**Methods:**
```typescript
// Post-processing workflows
static async processSchedule(scheduleId: string): Promise<any>
static async validateSchedule(scheduleId: string): Promise<any>
```

**Usage Example:**
```typescript
import { PostProcessingService } from '@/services/post-processing.service';

// Process schedule after creation
await PostProcessingService.processSchedule(scheduleId);

// Validate schedule before posting
const validationResult = await PostProcessingService.validateSchedule(scheduleId);
```

### 9. TokenUsageService (`src/services/tokenUsage.service.ts`)

**Purpose:** Token usage tracking for LLM operations

**Methods:**
```typescript
// Token usage tracking
static async trackTokenUsage(data: TokenUsageData): Promise<any>
static async getTokenUsageSummary(): Promise<TokenUsageSummary>
```

**Usage Example:**
```typescript
import { TokenUsageService } from '@/services/tokenUsage.service';

// Track LLM token usage
await TokenUsageService.trackTokenUsage({
  operation: 'schedule_detection',
  tokens_used: 1250,
  entity_id: 'entity456'
});

// Get usage summary
const summary = await TokenUsageService.getTokenUsageSummary();
```

## Service Dependencies

### Import Structure
```typescript
// All services follow this import pattern
import { api } from '@/lib/api';
import { auth } from '@/lib/firebase';
import type { /* relevant types */ } from '@/types/';

// Service class
export class ServiceName {
  static async methodName(params: ParamsType): Promise<ReturnType> {
    // Implementation
  }
}
```

### Error Handling Strategy
All services implement consistent error handling:

```typescript
try {
  const response = await api.someMethod(params);
  return response;
} catch (error) {
  console.error('Error in service method:', error);
  throw new Error('User-friendly error message');
}
```

### Type Safety
- All service methods have proper TypeScript return types
- Parameters use defined interfaces from `@/types/`
- API responses are typed using interfaces from `@/lib/api`

## Best Practices

### 1. Always Use Services
```typescript
// ✅ Good - Use service layer
import { ClientsService } from '@/services/clients.service';
const clients = await ClientsService.getClients();

// ❌ Bad - Direct API calls
import { api } from '@/lib/api';
const clients = await api.getClients();
```

### 2. Handle Loading States
```typescript
// ✅ Good - Proper loading handling
const [loading, setLoading] = useState(false);

const fetchData = async () => {
  setLoading(true);
  try {
    const result = await ServiceName.methodName(params);
    setData(result);
  } catch (error) {
    toast.error(error.message);
  } finally {
    setLoading(false);
  }
};
```

### 3. Use TypeScript Types
```typescript
// ✅ Good - Proper typing
const createClient = async (data: CreateClientData): Promise<void> => {
  const response = await ClientsService.createClient(data);
  // response is properly typed
};

// ❌ Bad - No typing
const createClient = async (data) => {
  const response = await ClientsService.createClient(data);
};
```

### 4. Error Boundaries
```typescript
// ✅ Good - Descriptive error handling
try {
  await PrepaymentsService.confirmSchedule(scheduleId);
  toast.success('Schedule confirmed successfully');
} catch (error) {
  if (error.message.includes('already confirmed')) {
    toast.warning('Schedule is already confirmed');
  } else {
    toast.error('Failed to confirm schedule');
  }
}
```

## Service Testing

### Unit Testing Pattern
```typescript
// Example test structure
describe('ClientsService', () => {
  beforeEach(() => {
    // Mock API client
    vi.mock('@/lib/api');
  });

  it('should fetch clients successfully', async () => {
    // Mock API response
    const mockClients = [{ id: '1', name: 'Test Client' }];
    api.getClients.mockResolvedValue({ clients: mockClients });

    // Call service method
    const result = await ClientsService.getClients();

    // Assert result
    expect(result).toEqual(mockClients);
  });

  it('should handle errors gracefully', async () => {
    // Mock API error
    api.getClients.mockRejectedValue(new Error('API Error'));

    // Expect service to throw user-friendly error
    await expect(ClientsService.getClients()).rejects.toThrow('Failed to fetch clients');
  });
});
```

## Performance Considerations

### Caching Strategy
- Services leverage API client caching automatically
- Use `disableCache: true` parameter when fresh data is required
- Cache invalidation happens automatically on mutations

### Request Deduplication
- API client prevents duplicate requests within short time windows
- Services benefit from this automatically

### Error Recovery
- Services implement proper error recovery patterns
- Failed requests don't break application state
- User-friendly error messages guide user actions

## Migration Notes

### From Direct API Usage
If you have direct API calls, migrate to services:

```typescript
// Before
import { api } from '@/lib/api';
const response = await api.getDashboardTransactions(filters);

// After
import { PrepaymentsService } from '@/services/prepayments.service';
const response = await PrepaymentsService.getPrepaymentsData(filters);
```

### Adding New Methods
When adding new service methods:

1. Add method to appropriate service class
2. Use consistent error handling pattern
3. Add proper TypeScript types
4. Update this documentation
5. Add unit tests

## Related Documentation

- **API Reference:** `docs/API_REFERENCE.md` - Complete API endpoint documentation
- **Component Architecture:** `docs/COMPONENT_ARCHITECTURE.md` - How services integrate with components
- **Troubleshooting:** `docs/TROUBLESHOOTING.md` - Common service-related issues