# CLAUDE.md - DRCR Backend Essential Context

## Project Overview
Python FastAPI backend for automated prepayment processing with Xero integration, AI document processing, and amortization schedule generation.

**Tech Stack:** FastAPI, Python 3.9+, Firestore, Firebase Auth, OpenAI/Mistral, Xero API, GCP Cloud Functions/Run
**Architecture:** REST API + Cloud Functions, two-stage sync, service layer pattern

## Critical Development Commands

### Development Server
```bash
# Start REST API server (runs on http://localhost:8081)
cd /mnt/d/Projects/drcr_back/rest_api
python run_server.py
```

### Testing & Deployment
```bash
# Run all tests
python -m pytest tests/ -v

# Performance testing
python scripts/utilities/performance_test.py --url http://localhost:8081 --token-file firebase_id_token.txt

# Deploy to GCP
./gcloud_manual_deploy.sh
python scripts/deploy_scheduled_sync.py
```

### Database & Debug
```bash
# Firestore management
python scripts/utilities/create_firestore_indexes.py
python tests/token_management/check_all_tokens.py

# Fix common issues
python scripts/utilities/fix_xero_config.py
python scripts/utilities/generate_encryption_key.py
```

## AI Code Templates (Copy-Paste Ready)

### New FastAPI Endpoint
```python
@router.post("/endpoint", response_model=ResponseModel)
async def endpoint_name(
    request: RequestModel,
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Endpoint description
    """
    try:
        # Input validation
        if not request.required_field:
            raise HTTPException(status_code=400, detail="Required field missing")
        
        # Business logic
        result = await service.process_request(request, current_user, db)
        
        # Return response
        return ResponseModel(
            success=True,
            data=result,
            message="Operation completed successfully"
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error in endpoint_name: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
```

### New Service Method
```python
class ServiceName:
    def __init__(self, db):
        self.db = db
    
    async def process_data(self, data: dict, user: AuthUser) -> dict:
        """
        Process data with proper error handling and field access
        """
        try:
            # Use canonical field access
            entity_id = get_field(data, "entity_id")
            account_code = get_account_code(data)
            
            # Validate access permissions
            if not await self._validate_access(entity_id, user):
                raise ValueError("Access denied")
            
            # Process data
            processed_data = await self._process_logic(data)
            
            # Store in Firestore
            doc_ref = self.db.collection('COLLECTION_NAME').document()
            await doc_ref.set(processed_data)
            
            return {
                "id": doc_ref.id,
                "status": "success",
                "data": processed_data
            }
            
        except Exception as e:
            logger.error(f"Error in process_data: {str(e)}")
            raise
    
    async def _validate_access(self, entity_id: str, user: AuthUser) -> bool:
        """Validate user has access to entity"""
        # Implementation here
        return True
    
    async def _process_logic(self, data: dict) -> dict:
        """Core processing logic"""
        # Implementation here
        return data
```

### Firestore Query Template
```python
async def get_paginated_data(
    collection_name: str,
    filters: dict,
    limit: int = 25,
    offset: int = 0,
    db = None
) -> dict:
    """
    Standard paginated query with filtering
    """
    try:
        # Build query
        query = db.collection(collection_name)
        
        # Apply filters
        if filters.get("entity_id"):
            query = query.where("entity_id", "==", filters["entity_id"])
        
        if filters.get("status"):
            query = query.where("status", "==", filters["status"])
        
        if filters.get("date_from"):
            query = query.where("created_at", ">=", filters["date_from"])
        
        # Get total count
        total_docs = await query.count().get()
        total_count = total_docs[0][0].value
        
        # Apply pagination
        paginated_query = query.order_by("created_at", direction="DESCENDING")
        paginated_query = paginated_query.limit(limit).offset(offset)
        
        # Execute query
        docs = await paginated_query.get()
        
        # Transform results
        data = []
        for doc in docs:
            item = doc.to_dict()
            item["id"] = doc.id
            data.append(item)
        
        return {
            "data": data,
            "pagination": {
                "total": total_count,
                "page": (offset // limit) + 1,
                "per_page": limit,
                "pages": (total_count + limit - 1) // limit
            }
        }
        
    except Exception as e:
        logger.error(f"Error in get_paginated_data: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to fetch data")
```

### Pydantic Model Template
```python
from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime

class RequestModel(BaseModel):
    """Request model with validation"""
    entity_id: str = Field(..., description="Entity identifier")
    name: str = Field(..., min_length=1, max_length=100, description="Name field")
    amount: float = Field(..., gt=0, description="Amount must be positive")
    status: str = Field(..., regex="^(active|inactive|pending)$", description="Status")
    optional_field: Optional[str] = Field(None, description="Optional field")
    
    @validator('entity_id')
    def validate_entity_id(cls, v):
        if not v or len(v) < 10:
            raise ValueError('Invalid entity_id format')
        return v
    
    @validator('amount')
    def validate_amount(cls, v):
        if v <= 0:
            raise ValueError('Amount must be positive')
        return round(v, 2)

class ResponseModel(BaseModel):
    """Standard response model"""
    success: bool = Field(True, description="Operation success status")
    data: dict = Field(..., description="Response data")
    message: Optional[str] = Field(None, description="Success message")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
```

### Error Handling Template
```python
from fastapi import HTTPException
import logging

logger = logging.getLogger(__name__)

class ErrorHandler:
    """Standard error handling patterns"""
    
    @staticmethod
    def handle_validation_error(error: Exception) -> HTTPException:
        """Handle validation errors"""
        logger.warning(f"Validation error: {str(error)}")
        return HTTPException(
            status_code=422,
            detail={
                "error": "Validation failed",
                "message": str(error)
            }
        )
    
    @staticmethod
    def handle_auth_error(error: Exception) -> HTTPException:
        """Handle authentication errors"""
        logger.warning(f"Auth error: {str(error)}")
        return HTTPException(
            status_code=401,
            detail="Authentication required"
        )
    
    @staticmethod
    def handle_permission_error(error: Exception) -> HTTPException:
        """Handle permission errors"""
        logger.warning(f"Permission error: {str(error)}")
        return HTTPException(
            status_code=403,
            detail="Access denied"
        )
    
    @staticmethod
    def handle_not_found_error(resource: str) -> HTTPException:
        """Handle not found errors"""
        logger.info(f"Resource not found: {resource}")
        return HTTPException(
            status_code=404,
            detail=f"{resource} not found"
        )
    
    @staticmethod
    def handle_internal_error(error: Exception) -> HTTPException:
        """Handle internal server errors"""
        logger.error(f"Internal error: {str(error)}", exc_info=True)
        return HTTPException(
            status_code=500,
            detail="Internal server error"
        )
```

### Cloud Function Template
```python
import os
import sys
import logging
from datetime import datetime
from typing import Dict, List
import functions_framework
from google.cloud import firestore
from tenacity import retry, stop_after_attempt, wait_exponential

# Add paths for imports (DRCR pattern)
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

try:
    from dotenv import load_dotenv
    load_dotenv()  # Local dev only
except ImportError:
    pass

logger = logging.getLogger(__name__)

@functions_framework.http
def function_name(request):
    """
    Cloud Function entry point following DRCR patterns
    
    Returns:
        JSON response with processing results
    """
    start_time = datetime.utcnow()
    error_handler = ErrorHandler()  # Outside try block
    
    try:
        logger.info("Starting function execution")
        
        # Process request
        request_data = request.get_json(silent=True) or {}
        result = process_data(request_data)
        
        duration = (datetime.utcnow() - start_time).total_seconds()
        
        return {
            "status": "success", 
            "data": result,
            "duration_seconds": duration,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        duration = (datetime.utcnow() - start_time).total_seconds()
        error_message = str(e)
        
        logger.error(f"Function failed: {error_message}")
        
        # Log to error handler for monitoring
        try:
            error_handler.log_error("function_failure", error_message, {
                "duration_seconds": duration
            })
        except Exception:
            pass
        
        return {
            "status": "error",
            "error": error_message,
            "duration_seconds": duration,
            "timestamp": datetime.utcnow().isoformat()
        }, 500

@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10)
)
def api_call_with_retry():
    """Example API call with retry logic"""
    # API implementation here
    pass

class ServiceClass:
    def __init__(self):
        self.db = firestore.Client(project=os.getenv("GCP_PROJECT_ID"))
    
    def batch_firestore_operations(self, items: List[Dict]) -> Dict:
        """Batch Firestore operations for performance"""
        try:
            # Batch reads
            doc_refs = [self.db.collection('COLLECTION').document(item['id']) for item in items]
            docs = self.db.get_all(doc_refs)
            
            # Process results
            results = []
            for doc in docs:
                if doc.exists:
                    results.append(doc.to_dict())
            
            return {"processed": len(results), "total": len(items)}
            
        except Exception as e:
            logger.error(f"Batch operation failed: {str(e)}")
            raise
```

## Critical Field Access Patterns

### ✅ CORRECT Field Access
```python
from rest_api.utils.field_access import get_field, get_account_code, get_journal_lines

# General field access with alias resolution
entity_name = get_field(data, "entity_name")  # Handles "entity_name", "entityName", "name"
date_issued = get_field(data, "date_issued")  # Handles "Date", "DateString", "date_issued"

# Specialized helpers
account_code = get_account_code(line_data)  # Handles "AccountCode", "account_code"
journal_lines = get_journal_lines(journal_data)  # Handles "JournalLines", "lines"
```

### ❌ NEVER Use Direct Access
```python
# WRONG - Don't use direct field access
account_code = data.get("AccountCode") or data.get("account_code")  # DON'T DO THIS
date_value = data.get("Date") or data.get("date_issued")  # DON'T DO THIS

# WRONG - Inconsistent field naming
entity_name = data.get("name")  # Should be "entity_name"
```

### Field Alias Reference
```python
# Common aliases handled by get_field()
FIELD_ALIASES = {
    "AccountCode": "account_code",
    "Date": "date_issued",
    "JournalLines": "lines",
    "LineAmount": "line_amount",
    "Total": "total_amount",
    "journalDate": "journal_date",
    "DueDate": "date_due"
}
```

## Quick Debug Commands

### Server Debug
```bash
# Check server is running
curl -X GET http://localhost:8081/health

# Test authentication
curl -H "Authorization: Bearer $FIREBASE_TOKEN" \
     -X GET http://localhost:8081/auth/me

# Check specific endpoint
curl -H "Authorization: Bearer $FIREBASE_TOKEN" \
     -H "Content-Type: application/json" \
     -X GET http://localhost:8081/clients/summary
```

### Database Debug
```python
# Check Firestore connection
from rest_api.dependencies import get_global_db_client
db = get_global_db_client()
print("Database connected:", db is not None)

# Query collection
collection_ref = db.collection('ENTITIES')
docs = collection_ref.limit(5).get()
print(f"Found {len(docs)} documents")

# Check field access
from rest_api.utils.field_access import get_field
test_data = {"AccountCode": "6200", "Date": "2025-01-15"}
print("Account code:", get_field(test_data, "account_code"))
print("Date issued:", get_field(test_data, "date_issued"))
```

### Common Debug Issues
- **Import errors**: Check `sys.path` includes project root
- **Firestore errors**: Verify credentials path and project ID
- **Auth errors**: Check Firebase token expiry and format
- **Field access errors**: Use canonical helpers, not direct access
- **CORS errors**: Check allowed origins in main.py

## Key Architecture Components

### REST API (`rest_api/`) - 91 endpoints across 16 route files
- **Auth & Users:** `/auth/*` - Authentication, user management, password reset
- **Core Resources:** `/clients/*`, `/entities/*`, `/firms/*` - Business entities
- **Financial Data:** `/transactions/*`, `/schedules/*`, `/manual-journals/*` - Prepayment processing
- **Integration:** `/xero/*`, `/sync/*` - External platform integration
- **Support:** `/attachments/*`, `/audit/*`, `/reports/*` - Supporting features

### Cloud Functions (`cloud_functions/`)
- **xero_sync_consumer**: Main sync processor for Xero data
- **scheduled_sync_processor**: Automated scheduled synchronization
- **nylas_email_poller**: Production email threading + reply system with Cloud Tasks async processing

### Shared Logic (`drcr_shared_logic/`)
- **Clients**: Base client and Xero client implementations  
- **Document Processor**: AI-powered invoice document processing

## Core Features
1. **Two-Stage Sync Architecture**: Fast metadata loading (Cloud Functions) + heavy processing (Cloud Run)
2. **AI Document Processing**: PDF/image extraction with OpenAI and Mistral OCR
3. **Hybrid Amortization**: Smart selection between 12-month equal vs day-based distribution
4. **Real-time Progress Tracking**: Firestore-based sync job monitoring
5. **Cost-Effective Token Storage**: Encrypted Firestore storage vs expensive Secret Manager

## Critical Project Rules

### 1. Full-Stack Development
- **Frontend**: `/mnt/d/Projects/drcr_front/` - React app on `http://localhost:5173`
- **Backend**: `/mnt/d/Projects/drcr_back/` - FastAPI app on `http://localhost:8081`
- **API Changes**: Update both backend docs AND frontend CLAUDE.md immediately

### 2. Data Consistency (CRITICAL FOR PRODUCTION)
- **Use canonical field access**: Always use `get_field()` helpers, never direct access
- **Field compatibility**: Backend snake_case ↔ Frontend camelCase transformations
- **Test end-to-end**: Local success ≠ cloud success, always test full flow

### 3. Environment Configuration
- **Development**: Use `.env` file with `API_PORT=8081`, `XERO_REDIRECT_URI=http://localhost:8081/xero/callback`
- **Production**: Load secrets from Secret Manager with `LOAD_SECRETS_FROM_SECRET_MANAGER=true`

### 4. Token Usage Tracking (CRITICAL FOR BILLING)
- **LLM Processing**: Token usage data MUST flow through all processing paths
- **Lightweight Summaries**: Always include `_token_usage` in returned summaries
- **Sync Process**: Token usage aggregated in `SYNC_JOBS.token_usage` for billing
- **Cost Tracking**: Use `_update_usage_counters` to track OpenAI/Mistral costs

## Documentation References

**For detailed information, see:**
- **Complete API Reference:** `docs/BACKEND_REFERENCE.md` (91 endpoints)
- **Database Schema:** `docs/DATABASE_SCHEMA.md` (20 collections)
- **Full-Stack Integration:** `/mnt/d/Projects/FULLSTACK_INTEGRATION.md`
- **Performance Guide:** `docs/PERFORMANCE_OPTIMIZATION_SUMMARY.md`

## Development Workflow
1. **Setup**: `pip install -r requirements.txt`, configure `.env`, `gcloud auth application-default login`
2. **Development**: Start server, run tests, use performance monitoring
3. **Integration**: Test frontend-backend together, validate data contracts
4. **Deployment**: Use deployment scripts, validate in production

## 📝 Documentation Update Rules
**After ANY code change, you MUST update documentation:**
1. **New API endpoint** → Update `docs/BACKEND_REFERENCE.md` + add to CLAUDE.md if commonly used
2. **Database schema change** → Update `docs/DATABASE_SCHEMA.md` + field access patterns in CLAUDE.md
3. **New service method** → Update service docs + add template to CLAUDE.md if common
4. **Field access change** → Update field access patterns in CLAUDE.md + `field_access.py`
5. **Integration change** → Update `docs/FRONTEND_INTEGRATION.md` + cross-project docs

**Validation**: Check `/mnt/d/Projects/DOCUMENTATION_UPDATE_PROCESS.md` for full process

---
**Last Updated:** 2025-07-22 | **Lines:** ~450 (AI-optimized with templates) | **Frontend:** `/mnt/d/Projects/drcr_front/CLAUDE.md`

## Recent Critical Fixes

### Custom Narration for Xero Journal Entries (2025-07-22) ✅ COMPLETED
**Feature**: Added ability to enter/edit custom narration while posting journals to Xero
**Implementation**: 
- **Database**: Added `custom_narration` field to `AMORTIZATION_SCHEDULES` collection (max 500 chars)
- **Helper**: Created `narration_helper.py` with precedence logic: custom → auto-generated
- **API Integration**: Updated `ScheduleUpdateRequest` schema and 3 journal posting functions
- **Frontend**: Added narration textarea to `AmortizationConfiguration` and `EditScheduleModal` components
- **Bug Fix**: Frontend now correctly sends custom_narration to `/schedules/{id}` PUT endpoint
```python
# Backend usage in journal posting
from rest_api.utils.narration_helper import build_narration

entry_context = {
    "transaction_details": transaction_details,
    "entry_index": i,
    "total_entries": len(monthly_entries)
}
narration = build_narration(schedule_data, entry_context)  # Custom or auto-generated

# ScheduleUpdateRequest schema includes
custom_narration: Optional[str] = Field(None, max_length=500, description="Custom narration for journal entries (max 500 chars)")
```
**User Experience**: Users can customize journal narration in Bills for Amortization page, with auto-generated fallback
**Endpoints Affected**: `/schedules/{id}` PUT (update), `/schedules/{id}/confirm`, `/schedules/{id}/entries/{index}/post`, `/schedules/{id}/entries/bulk-post`
**Status**: ✅ **FULLY FUNCTIONAL** - Custom narration flows from frontend → API → journal entries → Xero

### Day-Based Amortization 50% Rule Fix (2025-07-22)  
**Issue**: Day-based amortization 50% rule was not properly respected for date display in calculate-preview endpoint
**Root Cause**: `month_date` calculation always used end-of-month dates regardless of 50% rule setting
**Fix**: Modified `AmortizationService._calculate_day_based()` to respect setting for both amount calculation AND date display
```python
# Fixed month_date calculation in amortization_service.py:196-209
if apply_50_percent_rule:
    # Use last day of month when 50% rule is applied
    last_day = monthrange(month_info['year'], month_info['month'])[1]
    month_date = date(month_info['year'], month_info['month'], last_day)
else:
    # When 50% rule is disabled, use actual start date for first month, end date for last month
    if i == 0:  # First month
        month_date = start_date
    elif i == actual_periods - 1:  # Last month  
        month_date = end_date
    else:  # Middle months
        last_day = monthrange(month_info['year'], month_info['month'])[1]
        month_date = date(month_info['year'], month_info['month'], last_day)
```
**Impact**: All calculation methods now respect the `amortization_apply_50_percent_rule` entity setting:
- **Disabled**: Uses exact user-selected dates (April 7th shows as 2025-04-07, not 2025-04-30)
- **Enabled**: Applies 50% rule with end-of-month dates (existing behavior)
**Endpoints Affected**: `/schedules/calculate-preview`, all amortization calculations
**Testing**: Verified with entities having rule enabled/disabled - both scenarios work correctly

### Email Reply System Implementation (2025-07-20) ⚠️ BLOCKED
**Feature**: Complete email reply system with Cloud Tasks async processing - **infrastructure ready**
**Components**:
- `NylasService.send_reply()` - Thread-safe reply sending with idempotency
- `ReplyProcessor` - Template management and Cloud Tasks integration
- `REPLIES` Firestore subcollection - Full audit trail and status tracking
- Cloud Tasks queue with 1 msg/sec rate limiting for Gmail compliance
**Status**: ⚠️ **BLOCKED by Nylas API issue** - Grant shows empty scopes `[]` despite proper configuration
**Support**: Reported to Nylas (Grant ID: 8ede108b-5335-47bf-9a4a-325e60f2b2b8) - awaiting platform fix
**Impact**: Ready to deploy immediately once Nylas resolves API permissions bug

### Token Usage Tracking Fix (2025-07-17)
**Issue**: Token usage showed as 0 despite LLM processing occurring (amortization schedules created)
**Root Cause**: `_token_usage` data was being stripped from lightweight summaries in attachment processing
**Fix**: Added `_token_usage` field to all processing paths in `llm_processing_service.py`:
- `_process_single_attachment_sequential()` - Line 759
- `_process_single_attachment_with_semaphore()` - Line 650  
- `_create_lightweight_extraction_summary()` - Line 90
**Impact**: Token usage now flows correctly from LLM processing → sync tracking → billing