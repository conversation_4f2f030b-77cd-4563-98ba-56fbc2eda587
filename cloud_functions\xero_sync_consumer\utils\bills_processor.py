"""
Bills Processing Module

Handles Bills (ACCPAY) sync from Xero with prepayment detection and amortization schedule generation.
This includes GL-based and LLM-based prepayment analysis.
"""

import logging
import re
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List, Tuple
from google.cloud import firestore

# Import shared utilities and clients - consistent absolute imports
from .llm_utils import _perform_combined_prepayment_analysis, _fetch_and_process_attachments
from .sync_helpers import _update_firestore_sync_timestamp, _create_audit_log_entry
from .financial_doc_adapter import FinancialDocAdapter
# Canonical field access helper (MVP alias resolver)
from rest_api.utils.field_access import get_account_code

logger = logging.getLogger(__name__)

# NEW: Helper to determine the most reliable FX rate attached to the bill
# ----------------------------------------------------------------------
#  Xero can store a CurrencyRate at both the invoice level and inside each
#  Payment record.  Finance generally wants the *payment* rate when it
#  exists because that is the realised FX that hit the bank.  We therefore
#  look at the Payments list first and fall back to the invoice-level rate.


def _get_effective_currency_rate(bill: Dict[str, Any]) -> Tuple[Optional[float], str]:
    """Return a tuple (rate, source_tag).

    source_tag is one of:
        - "payment"  → pulled from the first Payment.CurrencyRate present
        - "invoice"  → pulled from Invoice.CurrencyRate when no payment-level rate exists
        - "missing"  → no rate information found (returns None)
    
    Note: Rejects zero or negative rates as invalid.
    """
    try:
        payments = bill.get("Payments", []) or []
        for payment in payments:
            rate = payment.get("CurrencyRate")
            if rate is not None and rate > 0:
                return rate, "payment"
    except Exception:
        # Defensive: Payments may be a non-iterable; ignore and fall back
        pass

    invoice_level_rate = bill.get("CurrencyRate")
    if invoice_level_rate is not None and invoice_level_rate > 0:
        return invoice_level_rate, "invoice"

    return None, "missing"


def _parse_xero_date(xero_date_str):
    """Parse Xero date format /Date(timestamp)/ **or ISO strings** to datetime object."""
    if not xero_date_str:
        return None
    # Legacy /Date(12345+0000)/ format
    if isinstance(xero_date_str, str):
        if xero_date_str.startswith("/Date(") and xero_date_str.endswith(")/"):
            ts_match = re.search(r'(\d+)', xero_date_str)
            if ts_match:
                timestamp_ms = int(ts_match.group(1))
                return datetime.fromtimestamp(timestamp_ms / 1000, tz=timezone.utc)
        # Newer ISO 8601 strings coming from Xero (e.g. 2025-07-07T00:00:00+00:00)
        try:
            # Handle trailing Z by converting to explicit UTC offset
            iso_candidate = xero_date_str.replace('Z', '+00:00')
            return datetime.fromisoformat(iso_candidate)
        except ValueError:
            pass  # Fall through to next handlers
    # Raw timestamp (ms or s)
    if isinstance(xero_date_str, (int, float)):
        ts = int(xero_date_str)
        if ts > 1_000_000_000_000:  # likely milliseconds
            ts /= 1000
        return datetime.fromtimestamp(ts, tz=timezone.utc)
    # Unknown format – return None
    return None


def _normalize_xero_date(xero_date_str: Optional[str]) -> Optional[str]:
    """
    Normalize Xero date formats for comparison.
    
    Xero dates can come in multiple formats:
    - "/Date(1749651754140+0000)/" (Xero's legacy format)
    - "2025-06-15T19:07:03.007341+00:00" (ISO format)
    - "2025-06-15T19:07:03" (ISO without timezone)
    
    Returns a normalized string for comparison or None if invalid.
    """
    if not xero_date_str:
        return None
    
    try:
        # Handle Xero's legacy /Date()/ format
        if xero_date_str.startswith("/Date(") and xero_date_str.endswith(")/"):
            # Extract timestamp from "/Date(1749651754140+0000)/"
            timestamp_part = xero_date_str[6:-2]  # Remove "/Date(" and ")/"
            
            # Handle timezone offset in timestamp
            try:
                if "+" in timestamp_part:
                    timestamp_ms = int(timestamp_part.split("+")[0])
                elif "-" in timestamp_part:
                    timestamp_ms = int(timestamp_part.split("-")[0])
                else:
                    timestamp_ms = int(timestamp_part)
            except (ValueError, IndexError) as e:
                logger.warning(f"Failed to parse timestamp from Xero date '{xero_date_str}': {e}")
                return xero_date_str
            
            # Convert to ISO format for consistent comparison
            dt = datetime.fromtimestamp(timestamp_ms / 1000, tz=timezone.utc)
            return dt.isoformat()
        
        # Handle ISO format dates - normalize to consistent format
        if "T" in xero_date_str:
            try:
                # Parse ISO format and convert to UTC
                if xero_date_str.endswith("Z"):
                    dt = datetime.fromisoformat(xero_date_str.replace("Z", "+00:00"))
                elif any(tz in xero_date_str for tz in ["+", "Z"]) or (xero_date_str.count("-") >= 3 and ("+" in xero_date_str[-6:] or "-" in xero_date_str[-6:])):
                    dt = datetime.fromisoformat(xero_date_str)
                else:
                    # No timezone info, assume UTC
                    dt = datetime.fromisoformat(xero_date_str).replace(tzinfo=timezone.utc)
                
                return dt.isoformat()
            except ValueError:
                logger.warning(f"Failed to parse ISO date format: {xero_date_str}")
                return xero_date_str  # Return as-is for comparison
        
        # Return as-is if we can't parse it
        return xero_date_str
        
    except Exception as e:
        logger.warning(f"Error normalizing Xero date '{xero_date_str}': {e}")
        return xero_date_str  # Return as-is for comparison


# ---------------------------------------------------------------------------
# Date helpers
# ---------------------------------------------------------------------------


def _to_iso_string(dt: Optional[datetime]) -> Optional[str]:
    """Return ISO-8601 string with timezone for storage; returns None if input is None."""
    if not dt:
        return None
    if isinstance(dt, datetime):
        # Ensure tz-aware; if naive assume UTC
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
        return dt.isoformat().replace("Z", "+00:00")
    # If already a string just pass it through (assume upstream produced ISO)
    return str(dt)


async def _check_if_current_version_exists(
    db: firestore.AsyncClient,
    record_id: str,
    new_updated_at: Optional[str],
    entity_id: str,
    collection_name: str = "TRANSACTIONS",
    record_type: str = "record"
) -> bool:
    """
    Check if we already have the current version of this record.
    
    Args:
        db: Firestore client
        record_id: The document ID to check
        new_updated_at: The new UpdatedDateUTC from Xero
        entity_id: Entity ID for logging
        collection_name: Firestore collection to check (default: TRANSACTIONS)
        record_type: Type of record for logging (default: record)
    
    Returns:
        True if we already have the current version (skip processing)
        False if we need to process (new or changed)
    """
    try:
        existing_doc = await db.collection(collection_name).document(record_id).get()
        
        if not existing_doc.exists:
            logger.debug(f"{record_type.title()} {record_id} not found in {collection_name} - will process")
            return False
        
        existing_data = existing_doc.to_dict()
        existing_updated_at = existing_data.get("source_updated_at_utc")
        
        # Normalize both dates for comparison
        normalized_existing = _normalize_xero_date(existing_updated_at)
        normalized_new = _normalize_xero_date(new_updated_at)
        
        if normalized_existing == normalized_new:
            logger.info(f"Skipping {record_type} {record_id} - already have current version (UpdatedDateUTC: {new_updated_at})")
            return True
        else:
            logger.info(f"{record_type.title()} {record_id} has changed - will process (Existing: {existing_updated_at} -> New: {new_updated_at})")
            return False
            
    except Exception as e:
        logger.error(f"Error checking version for {record_type} {record_id}: {e}")
        # On error, process the record to be safe
        return False


async def process_bills_sync(
    db: firestore.AsyncClient,
    xero_client,
    entity_id: str,
    client_id: str,
    entity_settings: Dict[str, Any],
    message_data: Dict[str, Any],
    force_full_sync_endpoints: List[str],
    sync_job_id: str,
    processed_prepayments_count: int,
    metadata_only: bool = False
) -> tuple[int, int]:
    """
    Process Bills sync from Xero with optional heavy processing.
    
    Args:
        metadata_only: If True, only fetch and save bill metadata without attachments/LLM analysis
    
    Returns:
        tuple: (saved_bills_count, processed_prepayments_count)
    """
    logger.info(f"Starting Bills sync for entity_id: {entity_id}, client_id: {client_id}")
    
    try:
        # Sync configuration
        full_sync = "Bills" in force_full_sync_endpoints
        transaction_sync_start_date = message_data.get("transactionSyncStartDate")
        
        # Get last sync timestamp for incremental sync
        sync_ts_field = "_system_lastSyncTimestampUtc_Bills"
        last_sync_timestamp_utc_str = entity_settings.get(sync_ts_field) if not full_sync else None
        
        where_filter = 'Type=="ACCPAY" AND Status!="VOIDED" AND Status!="DELETED"'
        
        # Use transaction_sync_start_date only for FIRST sync (when no previous sync timestamp exists)
        use_date_filter = transaction_sync_start_date and not last_sync_timestamp_utc_str
        
        if use_date_filter:
            # Parse the date and format for Xero DateTime constructor (FIRST SYNC ONLY)
            try:
                date_obj = datetime.strptime(transaction_sync_start_date, '%Y-%m-%d').date()
                where_filter += f' AND Date>=DateTime({date_obj.year},{date_obj.month:02d},{date_obj.day:02d},00,00,00)'
                logger.info(f"First sync - using date filter from {transaction_sync_start_date}")
            except ValueError:
                logger.warning(f"Invalid transactionSyncStartDate format: {transaction_sync_start_date}, skipping date filter")
                use_date_filter = False
        
        # Fetch Bills from Xero
        bills_data = await xero_client.get_records("Invoices", 
                                                  where_filter=where_filter, 
                                                  if_modified_since=last_sync_timestamp_utc_str)
        
        logger.info(f"Fetched {len(bills_data)} Bills from Xero for entity {entity_id}")
        
        # Process bills with batch writing for efficiency
        saved_bills_count = 0
        skipped_bills_count = 0
        batch = db.batch()
        batch_count = 0
        doc_adapter = FinancialDocAdapter(entity_id=entity_id)
        
        for bill in bills_data:
            bill_id = bill.get("InvoiceID")
            if not bill_id:
                logger.warning(f"Bill data missing InvoiceID for entity {entity_id}. Skipping: {bill}")
                continue
            
            # CHECK IF WE ALREADY HAVE THE CURRENT VERSION - SKIP PROCESSING IF SO
            new_updated_at = bill.get("UpdatedDateUTC")
            if await _check_if_current_version_exists(db, bill_id, new_updated_at, entity_id, "TRANSACTIONS", "bill"):
                skipped_bills_count += 1
                continue  # Skip all processing for this bill - we already have current version
            
            # Skip expensive transformation in metadata-only mode
            if metadata_only:
                standardized_bill = {}  # Empty - not needed for minimal processing
            else:
                try:
                    standardized_bill = await doc_adapter._transform_invoice(bill)
                except Exception as e_transform:
                    logger.error(f"Error transforming bill {bill_id}: {e_transform}")
                    continue
            
            # Apply filtering logic
            excluded_account_codes = set(entity_settings.get("excluded_pnl_account_codes", []))
            
            # Filter out bills where ALL line items use excluded account codes
            if excluded_account_codes:
                bill_line_items = bill.get("LineItems", [])
                if bill_line_items:  # Only check if there are line items
                    non_excluded_line_items = [li for li in bill_line_items if get_account_code(li) not in excluded_account_codes]
                    if not non_excluded_line_items:
                        # All line items use excluded account codes
                        excluded_codes_used = [get_account_code(li) for li in bill_line_items]
                        logger.info(f"Skipping bill {bill_id} - all line items use excluded account codes: {excluded_codes_used}")
                        skipped_bills_count += 1
                        continue
            
            # NOTE: Amount filtering now done at Xero API level for performance
            # All bills fetched are already above scanning_amount_threshold
            
            # Check if this transaction has potential prepayments before processing
            prepayment_asset_codes = entity_settings.get("prepayment_asset_account_codes", [])
            gl_prepayment_lines = [li for li in bill.get("LineItems", []) if get_account_code(li) in prepayment_asset_codes]
            has_potential_prepayments = (len(gl_prepayment_lines) > 0 or 
                                       entity_settings.get("enable_llm_prepayment_detection", True))
            
            # Build complete transaction document
            transaction_doc = await _build_transaction_document(
                bill=bill,
                standardized_bill=standardized_bill,
                bill_id=bill_id,
                entity_id=entity_id,
                client_id=client_id,
                has_potential_prepayments=has_potential_prepayments,
                metadata_only=metadata_only
            )
            
            transaction_ref = db.collection("TRANSACTIONS").document(bill_id)
            
            if metadata_only:
                # Metadata-only mode: Save all bills to batch without heavy processing
                batch.set(transaction_ref, transaction_doc, merge=True)
                saved_bills_count += 1
                batch_count += 1
                
                # Commit batch every 100 documents
                if batch_count >= 100:
                    await batch.commit()
                    batch = db.batch()
                    batch_count = 0
            else:
                # Original mode: Process with heavy analysis if needed
                if has_potential_prepayments:
                    # Process prepayment transaction individually (no batch)
                    saved_count, prep_count = await _process_prepayment_transaction(
                        db=db,
                        xero_client=xero_client,
                        transaction_ref=transaction_ref,
                        transaction_doc=transaction_doc,
                        bill=bill,
                        bill_id=bill_id,
                        entity_settings=entity_settings,
                        entity_id=entity_id,
                        client_id=client_id,
                        gl_prepayment_lines=gl_prepayment_lines
                    )
                    saved_bills_count += saved_count
                    processed_prepayments_count += prep_count
                else:
                    # Add non-prepayment transaction to batch for efficiency
                    batch.set(transaction_ref, transaction_doc, merge=True)
                    saved_bills_count += 1
                    batch_count += 1
                    
                    # Commit batch every 100 documents
                    if batch_count >= 100:
                        await batch.commit()
                        batch = db.batch()
                        batch_count = 0
        
        # Commit remaining documents
        if batch_count > 0:
            try:
                await batch.commit()
                logger.info(f"Committed final batch of {batch_count} Bills")
            except Exception as e_final_batch:
                logger.error(f"Error committing final batch in Bills sync: {e_final_batch}")
            finally:
                batch = db.batch()
                batch_count = 0
        
        logger.info(f"Bills sync completed for entity_id: {entity_id} - Processed: {saved_bills_count}, Skipped: {skipped_bills_count}, Prepayments: {processed_prepayments_count}")
        await _update_firestore_sync_timestamp(db, entity_id, "Bills", datetime.now(timezone.utc).isoformat(), "Bills sync successful")
        await _create_audit_log_entry(db, "SYNC", "BILLS_SYNC_SUCCESS", client_id, entity_id, "SUCCESS", 
                                     {"bills_processed": saved_bills_count, "bills_skipped": skipped_bills_count, "prepayments_count": processed_prepayments_count, "syncJobId": sync_job_id})
        
        return saved_bills_count, processed_prepayments_count
        
    except Exception as e_bills:
        logger.error(f"Error syncing Bills for entity_id {entity_id}: {e_bills}", exc_info=True)
        await _create_audit_log_entry(db, "SYNC", "BILLS_SYNC_FAILURE", client_id, entity_id, "FAILURE", 
                                     {"error": str(e_bills), "syncJobId": sync_job_id})
        raise


async def _build_transaction_document(
    bill: Dict[str, Any],
    standardized_bill: Dict[str, Any], 
    bill_id: str,
    entity_id: str,
    client_id: str,
    has_potential_prepayments: bool,
    metadata_only: bool = False
) -> Dict[str, Any]:
    """Build the base transaction document structure."""
    
    if metadata_only:
        # Enhanced metadata for Stage 1 - includes fields needed by PrepaymentReleaseDetector
        journal_date = _parse_xero_date(bill.get("Date"))
        currency_rate_val, currency_rate_source = _get_effective_currency_rate(bill)
        
        return {
            # Core identifiers
            "transaction_id": bill_id,
            "entity_id": entity_id,
            "client_id": client_id,
            "source_system": "XERO",
            "source_system_id": bill_id,  # Match Stage 2 for consistency
            
            # Business data for filtering/prioritization
            "total_amount": bill.get("Total"),
            "document_number": bill.get("InvoiceNumber"),
            "status": bill.get("Status"),
            
            # NEW - minimum detector fields extracted from raw_xero_data
            "document_type": "BILL",
            "transaction_type": "ACCPAY",
            "date_issued": _to_iso_string(journal_date),
            "contact_name": bill.get("Contact", {}).get("Name") if bill.get("Contact") else "",
            "reference": bill.get("Reference") or bill.get("InvoiceNumber", ""),  # For ACCPAY, InvoiceNumber IS the reference
            "line_items": bill.get("LineItems", []),
            
            # Financial amounts - essential for detector scoring
            "subtotal": bill.get("SubTotal"),
            "tax_total": bill.get("TotalTax"),
            "amount_due": bill.get("AmountDue"),
            "amount_paid": bill.get("AmountPaid"),
            "currency_code": bill.get("CurrencyCode"),
            "currency_rate": currency_rate_val,
            "currency_rate_source": currency_rate_source,
            
            # Contact information - for matching
            "contact_id": bill.get("Contact", {}).get("ContactID") if bill.get("Contact") else None,
            
            # Processing flags for Stage 2
            "processing_status": "metadata_loaded",
            "has_potential_prepayments": has_potential_prepayments,
            "needs_heavy_processing": has_potential_prepayments,
            
            # Raw data for Stage 2 processing - store complete bill for later
            "raw_xero_data": bill,
            
            # Timestamps
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        }
    
    # Full document for non-metadata mode (legacy/direct processing)
    transaction_doc = {
        # Core identifiers
        "transaction_id": bill_id,
        "entity_id": entity_id,
        "client_id": client_id,
        "source_system": "XERO",
        "source_system_id": bill_id,

        # Document classification
        "document_type": "BILL",
        "transaction_type": "ACCPAY",
        "document_number": bill.get("InvoiceNumber"),
        "reference": bill.get("Reference") or bill.get("InvoiceNumber", ""),  # For ACCPAY, InvoiceNumber IS the reference
        "status": bill.get("Status"),

        # Dates (use standardized format)
        # Always store Firestore Timestamp objects for consistency
        "date_issued": _to_iso_string(_parse_xero_date(standardized_bill.get("date_issued"))) or standardized_bill.get("date_issued"),
        "date_due": _to_iso_string(_parse_xero_date(standardized_bill.get("date_due"))) or standardized_bill.get("date_due"),
        "source_updated_at_utc": bill.get("UpdatedDateUTC"),

        # Financial amounts
        "subtotal": bill.get("SubTotal"),
        "tax_total": bill.get("TotalTax"),
        "total_amount": bill.get("Total"),
        "amount_due": bill.get("AmountDue"),
        "amount_paid": bill.get("AmountPaid"),
        "currency_code": bill.get("CurrencyCode"),

        # Contact information
        "contact_id": bill.get("Contact", {}).get("ContactID") if bill.get("Contact") else None,
        "contact_name": bill.get("Contact", {}).get("Name") if bill.get("Contact") else None,

        # Line items (essential for prepayment analysis)
        "line_items": bill.get("LineItems", []),

        # System fields
        "has_amortization_schedules": False,
        "prepayment_analysis": {
            "gl_based_analysis_completed": False,
            "llm_based_analysis_completed": False,
            "has_prepayment_line_items": False,
            "prepayment_line_items": [],
            "recommended_action": "pending_analysis"
        },

        # Processing status for two-stage sync
        "processing_status": "completed",
        "has_potential_prepayments": has_potential_prepayments,

        # Metadata
        "xero_url": standardized_bill.get("metadata", {}).get("xero_url"),

        # Timestamps
        "created_at": firestore.SERVER_TIMESTAMP,
        "updated_at": firestore.SERVER_TIMESTAMP
    }
    
    # Add currency rate fields (get once to avoid double call)
    currency_rate_val, currency_rate_source = _get_effective_currency_rate(bill)
    transaction_doc["currency_rate"] = currency_rate_val
    transaction_doc["currency_rate_source"] = currency_rate_source
    
    # Clean None values
    return {k: v for k, v in transaction_doc.items() if v is not None}


async def _process_prepayment_transaction(
    db: firestore.AsyncClient,
    xero_client,
    transaction_ref,
    transaction_doc: Dict[str, Any],
    bill: Dict[str, Any],
    bill_id: str,
    entity_settings: Dict[str, Any],
    entity_id: str,
    client_id: str,
    gl_prepayment_lines: List[Dict[str, Any]]
) -> tuple[int, int]:
    """
    Process a single transaction with prepayment analysis and schedule generation.
    Uses individual write (not batch) to avoid race conditions.
    
    Returns:
        tuple: (saved_count, processed_prepayments_count)
    """
    processed_prepayments_count = 0
    
    try:
        # Phase 1: Analysis
        processed_attachments = []
        if entity_settings.get("enable_llm_prepayment_detection", True):
            processed_attachments = await _fetch_and_process_attachments(
                xero_client, bill_id, bill, entity_settings, entity_id, db, f"bill_{bill_id}"
            )
        
        combined_analysis = await _perform_combined_prepayment_analysis(
            bill, gl_prepayment_lines, processed_attachments, entity_settings
        )
        logger.info(f"Completed prepayment analysis for bill {bill_id}: {combined_analysis['recommended_action']}")
        
        # Phase 2: Schedule creation (only if analysis succeeded)
        if combined_analysis["recommended_action"] == "create_amortization_schedule":
            processed_prepayments_count = await _create_amortization_schedules(
                db=db,
                bill=bill,
                bill_id=bill_id,
                combined_analysis=combined_analysis,
                entity_settings=entity_settings,
                entity_id=entity_id,
                client_id=client_id,
                gl_prepayment_lines=gl_prepayment_lines
            )
        
        # Update transaction_doc with complete analysis results
        transaction_doc["prepayment_analysis"] = combined_analysis
        transaction_doc["has_amortization_schedules"] = combined_analysis["recommended_action"] == "create_amortization_schedule"
        transaction_doc["attachments_processed"] = len(processed_attachments)
        transaction_doc["llm_processing_completed"] = combined_analysis["llm_based_analysis_completed"]
        
    except Exception as e_analysis:
        logger.error(f"Error in prepayment analysis for bill {bill_id}: {e_analysis}", exc_info=True)
        # Create fallback analysis
        fallback_analysis = {
            "gl_based_analysis_completed": True,
            "llm_based_analysis_completed": False,
            "has_prepayment_line_items": False,
            "detection_methods": [],
            "prepayment_line_items": [],
            "llm_detections": [],
            "confidence_score": 0.0,
            "recommended_action": "analysis_failed",
            "error": str(e_analysis)
        }
        transaction_doc["prepayment_analysis"] = fallback_analysis
        transaction_doc["has_amortization_schedules"] = False
        transaction_doc["attachments_processed"] = 0
        transaction_doc["llm_processing_completed"] = False
    
    # Single atomic write for prepayment transaction
    try:
        await transaction_ref.set(transaction_doc, merge=True)
        logger.info(f"Successfully wrote prepayment transaction {bill_id} with complete data")
        return 1, processed_prepayments_count
    except Exception as e_write:
        logger.error(f"Failed to write prepayment transaction {bill_id}: {e_write}", exc_info=True)
        return 0, 0


async def _create_amortization_schedules(
    db: firestore.AsyncClient,
    bill: Dict[str, Any],
    bill_id: str,
    combined_analysis: Dict[str, Any],
    entity_settings: Dict[str, Any],
    entity_id: str,
    client_id: str,
    gl_prepayment_lines: List[Dict[str, Any]]
) -> int:
    """Create amortization schedules based on analysis results."""
    # Import here to avoid circular imports - using absolute import
    from cloud_functions.xero_sync_consumer.main import _generate_and_save_amortization_schedule
    
    processed_count = 0
    
    try:
        # Determine if this is GL-based or LLM-only detection
        has_gl_detection = len(combined_analysis.get("prepayment_line_items", [])) > 0
        has_llm_detection = len(combined_analysis.get("llm_detections", [])) > 0
        
        if has_gl_detection:
            # Traditional GL-based processing
            for prepayment_line in gl_prepayment_lines:
                try:
                    best_period = combined_analysis.get("best_service_period")
                    service_start_date, service_end_date = (
                        best_period.get("start_date"), 
                        best_period.get("end_date")
                    ) if best_period and best_period.get("confidence") in ["high", "medium"] else (None, None)
                    
                    schedule_id = await _generate_and_save_amortization_schedule(
                        db, bill_id, prepayment_line, 
                        entity_settings.get("_system_masterPeriodStartDate"), 
                        entity_settings.get("_system_masterPeriodEndDate"), 
                        entity_settings, client_id, entity_id, bill, 
                        service_start_date, service_end_date
                    )
                    if schedule_id:
                        processed_count += 1
                        logger.info(f"Generated amortization schedule {schedule_id} for bill {bill_id}")
                except Exception as e_schedule:
                    logger.error(f"Error generating amortization schedule for bill {bill_id}: {e_schedule}")
                    
        elif has_llm_detection:
            # LLM-only detection: use original line items as expense accounts
            logger.info(f"Processing LLM-only prepayment detection for bill {bill_id}")
            for line_item in bill.get("LineItems", []):
                try:
                    best_period = combined_analysis.get("best_service_period")
                    service_start_date, service_end_date = (
                        best_period.get("start_date"), 
                        best_period.get("end_date")
                    ) if best_period and best_period.get("confidence") in ["high", "medium"] else (None, None)
                    
                    schedule_id = await _generate_and_save_amortization_schedule(
                        db, bill_id, line_item, 
                        entity_settings.get("_system_masterPeriodStartDate"), 
                        entity_settings.get("_system_masterPeriodEndDate"), 
                        entity_settings, client_id, entity_id, bill, 
                        service_start_date, service_end_date, 
                        is_llm_detected=True
                    )
                    if schedule_id:
                        processed_count += 1
                        logger.info(f"Generated LLM-detected amortization schedule {schedule_id} for bill {bill_id}")
                except Exception as e_schedule:
                    logger.error(f"Error generating LLM-detected amortization schedule for bill {bill_id}: {e_schedule}")
                    
    except Exception as e_schedule_creation:
        logger.error(f"Error in schedule creation phase for bill {bill_id}: {e_schedule_creation}", exc_info=True)
    
    return processed_count