<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emergency Cache & Cookie Clear - DRCR</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #dc2626;
            margin-bottom: 20px;
        }
        .warning {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .button {
            background: #dc2626;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #b91c1c;
        }
        .button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .success {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #166534;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            display: none;
        }
        .error {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            display: none;
        }
        .log {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 Emergency Cache & Cookie Clear</h1>
        
        <div class="warning">
            <strong>Warning!</strong> This page is designed to resolve cache and cookie issues. 
            After clearing, all saved data will be deleted and the application will be reloaded.
        </div>

        <p>If you experience data display issues, authentication problems, or the application works incorrectly, 
        use this page to clear cache and cookies.</p>

        <div>
            <button class="button" onclick="clearAllCaches()">🧹 Clear All Caches</button>
            <button class="button" onclick="clearCookies()">🍪 Clear Cookies</button>
            <button class="button" onclick="clearCacheAndCookies()">🗑️ Clear Cache & Cookies</button>
            <button class="button" onclick="emergencyClear()">🚨 Emergency Clear</button>
            <button class="button" onclick="showCacheStatus()">🔍 Show Cache Status</button>
        </div>

        <div id="success" class="success">
            ✅ Operation completed successfully! Page will be reloaded in 3 seconds...
        </div>

        <div id="error" class="error">
            ❌ Error during operation. Try refreshing the page manually.
        </div>

        <div id="log" class="log"></div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.style.display = 'block';
            logDiv.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearAllCaches() {
            log('🧹 Starting to clear all caches...');
            
            try {
                // Clear localStorage
                const keysToRemove = [
                    'navigation-store',
                    'resizable-panel-width',
                    'sidebar-expanded-items',
                    'auth-store',
                    'client-store'
                ];

                keysToRemove.forEach(key => {
                    if (localStorage.getItem(key)) {
                        localStorage.removeItem(key);
                        log(`🗑️ Removed localStorage key: ${key}`);
                    }
                });

                // Clear all keys with app prefixes
                const allKeys = Object.keys(localStorage);
                allKeys.forEach(key => {
                    if (key.startsWith('drcr_') || key.startsWith('navigation-') || 
                        key.startsWith('auth-') || key.startsWith('client-')) {
                        localStorage.removeItem(key);
                        log(`🗑️ Removed by prefix: ${key}`);
                    }
                });

                // Clear sessionStorage
                sessionStorage.clear();
                log('🗑️ SessionStorage cleared');

                // Clear browser caches
                if ('caches' in window) {
                    caches.keys().then(cacheNames => {
                        return Promise.all(
                            cacheNames.map(cacheName => {
                                log(`🗑️ Deleting cache: ${cacheName}`);
                                return caches.delete(cacheName);
                            })
                        );
                    }).then(() => {
                        log('🗑️ Browser caches cleared');
                        showSuccess();
                    });
                } else {
                    showSuccess();
                }

            } catch (error) {
                log(`❌ Error: ${error.message}`);
                showError();
            }
        }

        function emergencyClear() {
            log('🚨 EMERGENCY CACHE CLEAR');
            
            try {
                // Clear everything
                localStorage.clear();
                sessionStorage.clear();
                log('🗑️ All storages cleared');

                // Clear browser caches
                if ('caches' in window) {
                    caches.keys().then(cacheNames => {
                        return Promise.all(
                            cacheNames.map(cacheName => {
                                log(`🗑️ Deleting cache: ${cacheName}`);
                                return caches.delete(cacheName);
                            })
                        );
                    }).then(() => {
                        log('🗑️ All browser caches deleted');
                        showSuccess();
                    });
                } else {
                    showSuccess();
                }

            } catch (error) {
                log(`❌ Emergency clear error: ${error.message}`);
                showError();
            }
        }

        function clearCookies() {
            log('🍪 Starting to clear cookies...');
            
            try {
                const cookies = document.cookie.split(';');
                
                cookies.forEach(cookie => {
                    const eqPos = cookie.indexOf('=');
                    const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
                    
                    // Delete cookie by setting it to expire in the past
                    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
                    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname};`;
                    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${window.location.hostname};`;
                    
                    log(`🍪 Removed cookie: ${name}`);
                });
                
                log('✅ Cookies cleared successfully');
                showSuccess();
                
            } catch (error) {
                log(`❌ Error clearing cookies: ${error.message}`);
                showError();
            }
        }

        function clearCacheAndCookies() {
            log('🗑️ Starting to clear cache and cookies...');
            
            try {
                // Clear cache
                clearAllCaches();
                
                // Clear cookies
                clearCookies();
                
                log('✅ Cache and cookies cleared successfully');
                showSuccess();
                
            } catch (error) {
                log(`❌ Error clearing cache and cookies: ${error.message}`);
                showError();
            }
        }

        function showCacheStatus() {
            log('🔍 CACHE STATUS');
            log('================');
            
            const localStorageKeys = Object.keys(localStorage);
            const sessionStorageKeys = Object.keys(sessionStorage);
            
            log(`LocalStorage keys (${localStorageKeys.length}): ${localStorageKeys.join(', ')}`);
            log(`SessionStorage keys (${sessionStorageKeys.length}): ${sessionStorageKeys.join(', ')}`);
            
            // Show cookies
            const cookies = document.cookie.split(';').map(c => c.trim()).filter(c => c);
            log(`Cookies (${cookies.length}): ${cookies.join(', ')}`);
            
            if ('caches' in window) {
                caches.keys().then(cacheNames => {
                    log(`Browser caches (${cacheNames.length}): ${cacheNames.join(', ')}`);
                });
            }
        }

        function showSuccess() {
            log('✅ Cache clearing completed successfully!');
            document.getElementById('success').style.display = 'block';
            document.getElementById('error').style.display = 'none';
            
            setTimeout(() => {
                window.location.href = '/';
            }, 3000);
        }

        function showError() {
            log('❌ Error clearing cache');
            document.getElementById('error').style.display = 'block';
            document.getElementById('success').style.display = 'none';
        }

        // Auto-log on page load
        log('📄 Emergency cache & cookie clear page loaded');
        log('Select an action to clear cache and/or cookies');
    </script>
</body>
</html> 