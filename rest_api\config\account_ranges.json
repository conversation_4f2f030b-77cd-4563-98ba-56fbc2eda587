{"account_classification_ranges": {"prepayment_candidates": [{"start": 620, "end": 629, "description": "Prepayments (Current Assets)"}], "revenue_accounts": [{"start": 200, "end": 299, "description": "Revenue & Income"}], "fixed_asset_accounts": [{"start": 710, "end": 799, "description": "Fixed Assets & Depreciation"}], "bank_accounts": [{"start": 0, "end": 99, "description": "Bank Accounts (No specific range - named accounts)"}], "current_assets": [{"start": 610, "end": 699, "description": "Current Assets (Debtors, Prepayments, Inventory)"}], "classic_exclusions": [{"start": 493, "end": 494, "description": "Travel Expenses"}, {"start": 404, "end": 404, "description": "Bank Fees"}, {"start": 401, "end": 401, "description": "Audit & Accountancy Fees"}, {"start": 441, "end": 441, "description": "Legal Expenses"}, {"start": 412, "end": 412, "description": "Consulting"}, {"start": 420, "end": 424, "description": "Entertainment Expenses"}], "expense_accounts": [{"start": 310, "end": 329, "description": "Direct Costs"}, {"start": 400, "end": 499, "description": "Overhead Expenses"}, {"start": 500, "end": 509, "description": "Tax Expenses"}], "current_liabilities": [{"start": 800, "end": 899, "description": "Current Liabilities"}], "long_term_liabilities": [{"start": 900, "end": 929, "description": "Non-current Liabilities"}], "equity_accounts": [{"start": 950, "end": 999, "description": "Equity & Retained Earnings"}], "current_assets_fallback": [{"start": 610, "end": 699, "description": "Current Assets (keyword search for prepaid/advance/deposit)"}, {"start": 1100, "end": 1199, "description": "Extended Current Assets (keyword search for prepaid/advance/deposit)"}]}, "xero_specific_accounts": {"vat_accounts": [{"start": 820, "end": 820, "description": "VAT Control Account"}], "paye_accounts": [{"start": 825, "end": 826, "description": "PAYE & NIC Payable"}, {"start": 947, "end": 947, "description": "Student Loan Deductions Payable"}, {"start": 868, "end": 868, "description": "Earnings Orders Payable"}], "corporation_tax": [{"start": 500, "end": 500, "description": "Corporation Tax (Expense)"}, {"start": 830, "end": 830, "description": "Provision for Corporation Tax (Liability)"}, {"start": 920, "end": 920, "description": "Deferred Tax (Non-current Liability)"}], "depreciation_accounts": [{"start": 711, "end": 771, "description": "Accumulated Depreciation/Amortisation (odd numbers)"}, {"start": 416, "end": 416, "description": "Depreciation Expense"}]}, "configuration_notes": {"prepayment_candidates": "Account 620 (Prepayments) - the primary prepayment account in Xero UK defaults", "revenue_accounts": "200-299 range covers Sales, Other Revenue, and Interest Income - excluded from prepayment scanning", "fixed_asset_accounts": "710-799 range includes all fixed assets and their accumulated depreciation - excluded from scanning", "bank_accounts": "Xero uses named bank accounts (no fixed numbering) - excluded from prepayment scanning", "current_assets": "610-699 range includes Accounts Receivable, Prepayments, and Inventory", "current_assets_fallback": "610-699 and 1100-1199 ranges for keyword-based prepayment detection (covers accounts like 1103 Prepayments)", "classic_exclusions": "Specific overhead accounts that rarely contain prepayments", "expense_accounts": "310-329 (Direct Costs) and 400-499 (Overheads) - included in prepayment scanning", "current_liabilities": "800-899 range includes Accounts Payable, Accruals, PAYE, VAT - excluded from scanning", "long_term_liabilities": "900-929 range for loans and deferred tax - excluded from scanning", "equity_accounts": "950-999 range for share capital, retained earnings, drawings - excluded from scanning"}, "xero_account_examples": {"200": "Sales → revenue_accounts (excluded)", "310": "Cost of Goods Sold → expense_accounts (included)", "400": "Advertising & Marketing → expense_accounts (included)", "401": "Audit & Accountancy fees → classic_exclusions (excluded)", "404": "Bank Fees → classic_exclusions (excluded)", "449": "Motor Vehicle Expenses → expense_accounts (included)", "493": "Travel – National → classic_exclusions (excluded)", "500": "Corporation Tax → expense_accounts (included)", "610": "Accounts Receivable → current_assets (excluded)", "620": "Prepayments → prepayment_candidates (auto-scan)", "630": "Inventory → current_assets (excluded)", "710": "Office Equipment → fixed_asset_accounts (excluded)", "800": "Accounts Payable → current_liabilities (excluded)", "820": "VAT → vat_accounts (excluded)", "960": "Retained Earnings → equity_accounts (excluded)"}, "prepayment_keywords": ["prepaid", "prepayment", "advance", "deposit", "retainer", "annual", "quarterly", "subscription", "licence", "license", "insurance", "rent", "rates", "maintenance", "service contract"], "xero_notes": {"bank_accounts": "Xero uses descriptive names for bank accounts (e.g., 'Business Account', 'Direct Debit – GBP') rather than numbered codes", "vat_handling": "VAT accounts use code 820 and are automatically managed by Xero's VAT features", "depreciation_pattern": "Fixed assets use even numbers (710, 720, 740, etc.) while accumulated depreciation uses odd numbers (711, 721, 741, etc.)", "gaps_in_numbering": "Xero leaves gaps in numbering (e.g., 200, 260, 270) to allow for custom accounts to be inserted", "tracking_categories": "Xero uses tracking categories (code 877) for department/project tracking separate from account codes"}}