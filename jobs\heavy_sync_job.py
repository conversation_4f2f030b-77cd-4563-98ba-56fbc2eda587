import asyncio
from rest_api.routes.sync import heavy_sync_processor

# build_context_from_env will be implemented separately (see jobs/utils.py)
from jobs.utils import build_context_from_env

if __name__ == "__main__":
    ctx = build_context_from_env()
    asyncio.run(
        heavy_sync_processor(
            db=ctx.db,
            entity_id=ctx.entity_id,
            client_id=ctx.client_id,
            sync_job_id=ctx.sync_job_id,
            bills_to_process=ctx.bills_to_process,
            entity_settings=ctx.entity_settings,
            base_currency=ctx.base_currency,
            user_id="cloud-run-job",
            run_prepayment_detector=ctx.run_prepayment_detector,
        )
    ) 