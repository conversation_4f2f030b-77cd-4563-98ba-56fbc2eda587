{"indexes": [{"collectionGroup": "MANUAL_JOURNALS", "queryScope": "COLLECTION", "fields": [{"fieldPath": "entity_id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "journalDate", "order": "DESCENDING"}]}, {"collectionGroup": "MANUAL_JOURNALS", "queryScope": "COLLECTION", "fields": [{"fieldPath": "entity_id", "order": "ASCENDING"}, {"fieldPath": "journalDate", "order": "ASCENDING"}]}, {"collectionGroup": "AMORTIZATION_SCHEDULES", "queryScope": "COLLECTION", "fields": [{"fieldPath": "entity_id", "order": "ASCENDING"}, {"fieldPath": "_system_linkedManualJournalID", "order": "ASCENDING"}]}, {"collectionGroup": "ACCOUNTS", "queryScope": "COLLECTION", "fields": [{"fieldPath": "entity_id", "order": "ASCENDING"}, {"fieldPath": "accountType", "order": "ASCENDING"}]}, {"collectionGroup": "TRANSACTIONS", "queryScope": "COLLECTION", "fields": [{"fieldPath": "entityId", "order": "ASCENDING"}, {"fieldPath": "dateUtc", "order": "DESCENDING"}]}, {"collectionGroup": "AMORTIZATION_SCHEDULES", "queryScope": "COLLECTION", "fields": [{"fieldPath": "entityId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "PROPOSED_JOURNALS", "queryScope": "COLLECTION", "fields": [{"fieldPath": "entityId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "AUDIT_LOG", "queryScope": "COLLECTION", "fields": [{"fieldPath": "entityId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "COMMENTS", "queryScope": "COLLECTION", "fields": [{"fieldPath": "parent_type", "order": "ASCENDING"}, {"fieldPath": "parent_id", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "COMMENTS", "queryScope": "COLLECTION", "fields": [{"fieldPath": "client_id", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "COMMENTS", "queryScope": "COLLECTION", "fields": [{"fieldPath": "mentions", "arrayConfig": "CONTAINS"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "COMMENTS", "queryScope": "COLLECTION", "fields": [{"fieldPath": "parent_type", "order": "ASCENDING"}, {"fieldPath": "parent_id", "order": "ASCENDING"}, {"fieldPath": "deleted", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "COMMENTS", "queryScope": "COLLECTION", "fields": [{"fieldPath": "parent_type", "order": "ASCENDING"}, {"fieldPath": "parent_id", "order": "ASCENDING"}, {"fieldPath": "created_by", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}], "fieldOverrides": []}