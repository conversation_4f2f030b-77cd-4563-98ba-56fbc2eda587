#!/usr/bin/env node

/**
 * Cache Clear Script
 * Clears browser cache for the DRCR frontend application
 * 
 * Usage: node scripts/clear-cache.js [url]
 * Example: node scripts/clear-cache.js http://localhost:5173
 */

const puppeteer = require('puppeteer');

async function clearBrowserCache(url = 'http://localhost:5173') {
  console.log('🧹 Starting cache clear process...');
  console.log(`🌐 Target URL: ${url}`);
  
  let browser;
  
  try {
    // Launch browser
    browser = await puppeteer.launch({
      headless: false, // Show browser for debugging
      defaultViewport: null,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Enable console logging
    page.on('console', msg => {
      console.log(`Browser: ${msg.text()}`);
    });
    
    // Navigate to the page
    console.log('📄 Navigating to page...');
    await page.goto(url, { waitUntil: 'networkidle0' });
    
    // Wait a bit for the page to load
    await page.waitForTimeout(2000);
    
    // Execute cache clearing functions
    console.log('🗑️ Executing cache clear functions...');
    
    const clearResults = await page.evaluate(() => {
      // Check if our cache functions are available
      if (typeof window.clearAllCaches === 'function') {
        window.clearAllCaches();
        return { success: true, method: 'clearAllCaches' };
      } else if (typeof window.emergencyClearCache === 'function') {
        window.emergencyClearCache();
        return { success: true, method: 'emergencyClearCache' };
      } else {
        // Fallback: manual cache clearing
        try {
          // Clear localStorage
          const keysToRemove = [
            'navigation-store',
            'resizable-panel-width',
            'sidebar-expanded-items',
            'auth-store',
            'client-store'
          ];
          
          keysToRemove.forEach(key => {
            if (localStorage.getItem(key)) {
              localStorage.removeItem(key);
              console.log(`Removed localStorage key: ${key}`);
            }
          });
          
          // Clear sessionStorage
          sessionStorage.clear();
          console.log('SessionStorage cleared');
          
          // Clear browser caches
          if ('caches' in window) {
            return caches.keys().then(cacheNames => {
              return Promise.all(
                cacheNames.map(cacheName => {
                  console.log(`Deleting cache: ${cacheName}`);
                  return caches.delete(cacheName);
                })
              );
            }).then(() => {
              return { success: true, method: 'manual' };
            });
          }
          
          return { success: true, method: 'manual' };
        } catch (error) {
          return { success: false, error: error.message };
        }
      }
    });
    
    if (clearResults.success) {
      console.log(`✅ Cache cleared successfully using method: ${clearResults.method}`);
    } else {
      console.log(`❌ Cache clear failed: ${clearResults.error}`);
    }
    
    // Wait a bit more for any async operations
    await page.waitForTimeout(3000);
    
    // Reload the page to verify cache is cleared
    console.log('🔄 Reloading page to verify cache clear...');
    await page.reload({ waitUntil: 'networkidle0' });
    
    console.log('✅ Cache clear process completed successfully!');
    
  } catch (error) {
    console.error('❌ Error during cache clear:', error.message);
    process.exit(1);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Get URL from command line arguments
const targetUrl = process.argv[2] || 'http://localhost:5173';

// Check if Puppeteer is available
try {
  require.resolve('puppeteer');
} catch (error) {
  console.error('❌ Puppeteer is not installed. Please install it first:');
  console.error('npm install puppeteer');
  process.exit(1);
}

// Run the cache clear
clearBrowserCache(targetUrl)
  .then(() => {
    console.log('🎉 Cache clear script completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Cache clear script failed:', error);
    process.exit(1);
  }); 