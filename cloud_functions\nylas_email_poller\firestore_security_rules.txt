// Firestore Security Rules for Email Reply System
// Add these rules to your existing firestore.rules file

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // EMAIL_THREADS collection (existing - add REPLIES subcollection support)
    match /ENTITIES/{entityId}/EMAIL_THREADS/{threadId} {
      // Allow read/write for authenticated users with entity access
      allow read, write: if request.auth != null 
        && hasEntityAccess(entityId);
      
      // REPLIES subcollection - new rules
      match /REPLIES/{replyId} {
        // Allow authenticated users to read replies for their entities
        allow read: if request.auth != null 
          && hasEntityAccess(entityId);
        
        // Allow authenticated users to create replies for their entities
        allow create: if request.auth != null 
          && hasEntityAccess(entityId)
          && validateReplyData(resource.data);
        
        // Allow updates only by reply creator or system
        allow update: if request.auth != null 
          && hasEntityAccess(entityId)
          && (resource.data.created_by == request.auth.uid 
              || isSystemUser());
        
        // Allow deletion only by admin or system
        allow delete: if request.auth != null 
          && (isAdmin() || isSystemUser());
      }
    }
    
    // EMAILS collection (existing - no changes needed)
    match /ENTITIES/{entityId}/EMAILS/{emailId} {
      allow read, write: if request.auth != null 
        && hasEntityAccess(entityId);
    }
    
    // Helper functions for security validation
    function hasEntityAccess(entityId) {
      // Check if user has access to this entity
      // Implementation depends on your user/entity relationship model
      return request.auth != null;
      // TODO: Implement proper entity access checking when auth system is integrated
      // Example: return entityId in get(/databases/$(database)/documents/USERS/$(request.auth.uid)).data.accessible_entities;
    }
    
    function isSystemUser() {
      // Check if request is from system service (Cloud Functions)
      // This could be via service account or special system token
      return request.auth.token.email == "<EMAIL>"
        || request.auth.token.firebase.sign_in_provider == "custom";
    }
    
    function isAdmin() {
      // Check if user has admin privileges
      return request.auth != null 
        && request.auth.token.admin == true;
    }
    
    function validateReplyData(data) {
      // Validate reply document structure and required fields
      return data.keys().hasAll(['thread_id', 'entity_id', 'to_email', 'subject', 'body', 'status'])
        && data.status in ['pending', 'sent', 'failed']
        && data.entity_id is string
        && data.thread_id is string
        && data.to_email is string
        && data.subject is string
        && data.body is string
        && data.created_at is timestamp;
    }
  }
}

/*
Usage Notes:

1. Entity Access Control:
   - hasEntityAccess() function needs to be implemented based on your user/entity model
   - Currently allows all authenticated users - customize based on your requirements
   
2. System Authentication:
   - Cloud Functions should use service account authentication
   - isSystemUser() checks for system service account or custom tokens
   
3. Reply Validation:
   - validateReplyData() ensures reply documents have required fields
   - Validates status values and data types
   
4. Admin Access:
   - isAdmin() function should check for admin role/claims
   - Admins can delete replies for compliance/moderation
   
5. Security Considerations:
   - Users can only access replies for entities they have access to
   - Users can only update replies they created
   - System can update any reply (for status changes)
   - All operations require authentication

To deploy these rules:
1. Add to your firestore.rules file
2. Run: firebase deploy --only firestore:rules
3. Or update via Firebase Console

Example entity access implementation:
function hasEntityAccess(entityId) {
  return request.auth != null 
    && (
      // User is entity owner
      get(/databases/$(database)/documents/ENTITIES/$(entityId)).data.owner_id == request.auth.uid
      // Or user has explicit access
      || request.auth.uid in get(/databases/$(database)/documents/ENTITIES/$(entityId)).data.authorized_users
    );
}
*/