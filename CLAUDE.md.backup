# CLAUDE.md - DRCR Frontend Project Context

## Project Overview
This is the frontend application for the DRCR (Debit Reconciliation & Categorization Resource) system - a React-based financial data management platform with TypeScript, Vite, and Tailwind CSS.

## 🚨 RECENT API CHANGES (2025-06-22)

### Schedule Status System
**Current Status Flow (verified against codebase):**
```
pending_configuration → pending_confirmation → confirmed → posted
                   ↘ proposed ↗
```

**Valid Statuses (from schedule.types.ts):**
- `pending_configuration` - LLM-detected, needs account setup
- `pending_confirmation` - Reviewed, ready for confirmation  
- `proposed` - Proposed state (legacy compatibility)
- `confirmed` - Confirmed, ready for posting
- `posted` - Successfully posted to accounting system
- `partially_posted` - Some entries posted, some pending
- `skipped` - User chose to skip
- `cancelled` - Cancelled by user or system  
- `error` - Error occurred during processing

**Status Classification:**
1. **Actionable Statuses** (need user attention): `pending_configuration`, `proposed`
2. **Detection Method:** Use `detection_method` field for LLM warnings (`llm_only` vs `gl_coding`)
3. **Calculation Method:** Show `calculation_method` field (`day_based` or `equal_monthly`)

---

## Backend API Reference
*Last verified: 2025-01-15 against actual api.ts implementation*

### **API Configuration**
- **Base URL:** `http://localhost:8081` (development)
- **Production URL:** `https://drcr-d660a.web.app` (backend)
- **Authentication:** Bearer token in Authorization header
- **Content-Type:** `application/json`

### **Complete API Endpoints Overview**

**Client Management**
- `GET /clients/` - Get all clients
- `GET /clients/summary` - Get clients with pagination and filtering
- `POST /clients/` - Create new client
- `GET /clients/{clientId}` - Get specific client
- `PUT /clients/{clientId}` - Update client

**Entity Management**
- `GET /entities/?client_id={clientId}` - Get entities for client
- `GET /entities/{entityId}` - Get entity details with settings
- `PUT /entities/{entityId}/settings` - Update entity settings
- `GET /entities/{entityId}/connection/status` - Check connection status
- `POST /entities/{entityId}/connection/disconnect` - Disconnect entity
- `GET /entities/{entityId}/accounts` - Get entity chart of accounts

**Entity Setup Wizard**
- `GET /entities/{entityId}/analysis/wizard` - Get wizard analysis
- `POST /entities/{entityId}/setup/complete` - Complete entity setup
- `GET /entities/{entityId}/analysis/bill-aggregates` - Get bill aggregates

**Xero Integration**
- `GET /xero/connect/initiate/{clientId}` - Initiate Xero OAuth flow
- `GET /clients/{clientId}/xero/configure` - Get Xero configuration
- `POST /xero/entities/{entityId}/revoke` - Revoke Xero connection
- `GET /xero/clients/{clientId}/xero/available-organizations` - List available orgs
- `POST /xero/clients/{clientId}/xero/connect-organization` - Connect specific org

**Dashboard & Reporting**
- `GET /reports/dashboard` - Get dashboard data
- `GET /transactions/` - Get transactions with filtering
- `GET /transactions/dashboard` - Get dashboard transactions
- `GET /transactions/{transactionId}` - Get specific transaction
- `GET /reports/amortization` - Get amortization report

**Additional Features**
- `GET /contacts` - Get contacts
- `GET /audit` - Get audit logs
- `GET /attachments/{attachmentId}` - Get attachment blob
- `POST /transactions/{transactionId}/schedules/bulk-create` - Create bulk schedules

### **Schedule Endpoints**

#### **1. Preview & Calculation**
```typescript
// Calculate preview without saving
POST /schedules/calculate-preview
Body: {
  amount: number;
  start_date: string; // "YYYY-MM-DD"
  end_date: string;
  calculation_method?: "auto" | "day_based" | "equal_monthly";
}
Response: {
  calculation_method: "day_based" | "equal_monthly";
  total_months: number;
  monthly_entries: Array<{
    month_date: string;
    amount: number;
  }>;
}
```

#### **2. Schedule Management**
```typescript
// Get schedule details
GET /schedules/{scheduleId}
Response: {
  id: string;
  status: "pending_configuration" | "proposed" | "confirmed" | "posted";
  calculation_method: "day_based" | "equal_monthly";
  detection_method: "llm_only" | "gl_coding";
  transaction_id: string;
  amount: number;
  monthly_entries: Array<MonthlyEntry>;
  amortizationAccountCode?: string;
  expenseAccountCode?: string;
  // ... other fields
}

// Update schedule metadata ONLY (does NOT change monthly entries)
PUT /schedules/{scheduleId}
Body: {
  account_code?: string;         // Asset/prepayment account code
  expense_account_code?: string; // Expense account code  
  description?: string;          // Schedule description
  // WARNING: amount, start_date, end_date changes require recalculation
  // Use /recalculate endpoint instead for these fields
}

// Recalculate entire schedule (⚠️ OVERWRITES all monthly entries, resets to 'proposed' status)
PUT /schedules/{scheduleId}/recalculate
Body: {
  amount: number;
  start_date: string;
  end_date: string;
  calculation_method?: "auto" | "day_based" | "equal_monthly";
}
// ⚠️ WARNING: This destroys all existing monthly entries and regenerates them

// Preview changes without saving
PUT /schedules/{scheduleId}/preview-changes
Body: {
  amount?: number;
  start_date?: string;
  end_date?: string;
  calculation_method?: "auto" | "day_based" | "equal_monthly";
}
```

#### **3. Schedule Actions**
```typescript
// Confirm schedule (proposed → confirmed)
POST /schedules/{scheduleId}/confirm

// Skip schedule (any status → skipped)  
POST /schedules/{scheduleId}/skip

// Update status manually
PUT /schedules/{scheduleId}/status
Body: {
  status: "cancelled" | "error" | etc.
}
```

#### **4. Monthly Entry Updates** 
✅ **NEW ENDPOINT AVAILABLE** - Update individual monthly entry amounts!

```typescript
// Update individual monthly entry amount
PUT /schedules/{scheduleId}/entries/{entryIndex}
Body: {
  amount: number;  // New amount for this specific month
}
Response: {
  success: boolean;
  message: string;
  updated_entry: {
    entry_index: number;
    amount: number;
    month_date: string;
    status: string;
  }
}

// Example usage:
PUT /schedules/abc123/entries/0
Body: { "amount": 1250.50 }
```

**Business Rules:**
- Cannot edit entries from posted schedules
- Cannot edit individual entries that are already posted to Xero
- Entry index is 0-based (first month = 0, second month = 1, etc.)
- Changes are tracked in audit log with original and new amounts

#### **5. Manual Schedule Creation** 
✅ **NEW ENDPOINT** - Create schedules manually for invoices without schedules!

```typescript
// Create a new schedule with user-edited preview data
POST /schedules
Body: {
  transaction_id: string;           // Transaction/invoice ID
  amount: number;                   // Total amount to amortize
  start_date: string;              // "YYYY-MM-DD" format
  end_date: string;                // "YYYY-MM-DD" format  
  calculation_method: string;       // "day_based" or "equal_monthly"
  monthly_entries: Array<{         // User-edited monthly entries
    month_date: string;            // "YYYY-MM-DD"
    amount: number;                // Monthly amount
    days_in_month?: number;        // Optional
    proportion: number;            // Proportion of total
  }>;
  account_code?: string;           // Asset/prepayment account (optional)
  expense_account_code?: string;   // Expense account (optional)
  description?: string;            // Schedule description (optional)
}
Response: {
  success: boolean;
  message: string;
  schedule_id: string;
  schedule: {
    id: string;
    status: "pending_configuration" | "proposed";
    transaction_id: string;
    amount: number;
    monthly_entries: Array<MonthlyEntry>;
    // ... other schedule fields
  }
}

// Example workflow for manual schedule creation:
// 1. Generate preview using POST /schedules/calculate-preview
// 2. User edits monthly amounts in UI
// 3. Save final schedule using POST /schedules
```

**Business Rules:**
- Schedule status is automatically set to "proposed" if both account codes provided
- Schedule status is "pending_configuration" if account codes missing
- Monthly entries preserve user edits from preview
- Detection method is automatically set to "manual" 
- Audit log entry tracks manual creation

#### **6. Journal Posting**
```typescript
// Post single monthly entry to Xero
POST /schedules/{scheduleId}/entries/{entryIndex}/post

// Post multiple entries to Xero (recommended)
POST /schedules/{scheduleId}/entries/bulk-post
Body: {
  entry_indices: number[]; // Which months to post [0,1,2,...]
}
```

### **Status System**

#### **Status Flow**
```
pending_configuration → proposed → confirmed → posted
         ↓                ↓           ↓
    (configure)      (review)    (approve)    (post to Xero)
```

#### **Status Meanings**
- **`pending_configuration`**: Missing account codes (LLM-detected schedules)
- **`pending_confirmation`**: Reviewed, ready for final confirmation
- **`proposed`**: Ready for user review and approval (legacy compatibility)
- **`confirmed`**: Approved, ready to post to accounting system
- **`posted`**: Successfully posted to Xero
- **`partially_posted`**: Some entries posted, some pending
- **`skipped`**: User chose not to amortize
- **`cancelled`**: User cancelled
- **`error`**: Error occurred

#### **Actionable Statuses** (need user attention)
```typescript
const actionableStatuses = ['pending_configuration', 'pending_confirmation', 'proposed'];
```

### **Data Models**

#### **Schedule Object**
```typescript
interface Schedule {
  id: string;
  transaction_id: string;
  line_item_id?: string;
  status: ScheduleStatus;
  
  // Amounts & Dates
  amount: number;
  currency: string;
  entry_date: string;
  
  // Account Codes
  account_code?: string;           // Asset/prepayment account
  expense_account_code?: string;   // Expense account
  
  // Calculation Metadata (NEW)
  calculation_method?: "day_based" | "equal_monthly";
  detection_method?: "llm_only" | "gl_coding";
  
  // Description & Journal Info
  description?: string;
  journal_id_external?: string;
  journal_link_external?: string;
  
  // Monthly Breakdown
  monthly_entries?: MonthlyEntry[];
  
  // Timestamps
  created_at: string;
  updated_at: string;
}

interface MonthlyEntry {
  monthDate: string;
  amount: number;
  status: string;
  postedJournalId?: string;
}
```

### **Authentication**
```typescript
// Add to all API requests
headers: {
  'Authorization': `Bearer ${firebaseToken}`,
  'Content-Type': 'application/json'
}
```

### **Error Handling**
```typescript
// Common error responses
{
  detail: string;        // Error message
  status_code: number;   // HTTP status
}

// Common status codes:
// 400 - Invalid request data
// 401 - Authentication required
// 403 - Permission denied  
// 404 - Schedule not found
// 500 - Server error
```

### **✅ MONTHLY ENTRY EDITING NOW AVAILABLE**

**SOLUTION:** New endpoint allows updating individual monthly entry amounts!

**USAGE:**
```typescript
// ✅ NEW WAY - Direct monthly entry updates
const updateMonthlyAmount = async (scheduleId: string, entryIndex: number, newAmount: number) => {
  try {
    const response = await api.put(`/schedules/${scheduleId}/entries/${entryIndex}`, {
      amount: newAmount
    });
    
    if (response.data.success) {
      console.log('Updated entry:', response.data.updated_entry);
      // Refresh schedule data to show changes
      await fetchScheduleData(scheduleId);
    }
  } catch (error) {
    if (error.response?.status === 400) {
      // Handle business rule violations
      alert(error.response.data.detail);
    } else {
      alert('Failed to update monthly entry');
    }
  }
};
```

**Error Handling:**
```typescript
// Common error scenarios:
// 400 - Invalid entry index
// 400 - Schedule is already posted
// 400 - Individual entry is already posted to Xero
// 404 - Schedule not found
// 403 - No access to client
```

**Frontend Implementation Example:**
```typescript
// Usage in component:
const handleAmountChange = async (entryIndex: number, newAmount: number) => {
  try {
    await updateMonthlyAmount(schedule.id, entryIndex, newAmount);
    // Success - update local state
    setSchedule(prev => ({
      ...prev,
      monthlyEntries: prev.monthlyEntries.map((entry, idx) => 
        idx === entryIndex ? { ...entry, amount: newAmount } : entry
      )
    }));
  } catch (error) {
    console.error('Failed to update monthly entry:', error);
  }
};
```

#### **✅ MANUAL SCHEDULE CREATION WORKFLOW**

**Complete implementation example for creating schedules for invoices without schedules:**

```typescript
// 1. Check if invoice has existing schedules
const checkForExistingSchedules = async (transactionId: string) => {
  try {
    const response = await api.get(`/transactions/${transactionId}/schedules`);
    return response.data.length > 0;
  } catch (error) {
    console.error('Failed to check existing schedules:', error);
    return false;
  }
};

// 2. Generate preview for manual editing
const generateSchedulePreview = async (previewData: {
  amount: number;
  start_date: string;
  end_date: string;
  entity_id: string;
  calculation_method?: "auto" | "day_based" | "equal_monthly";
}) => {
  try {
    const response = await api.post('/schedules/calculate-preview', previewData);
    return response.data;
  } catch (error) {
    console.error('Failed to generate preview:', error);
    throw error;
  }
};

// 3. Save schedule with user edits
const createFinalSchedule = async (scheduleData: {
  transaction_id: string;
  amount: number;
  start_date: string;
  end_date: string;
  calculation_method: string;
  monthly_entries: Array<{
    month_date: string;
    amount: number;
    days_in_month?: number;
    proportion: number;
  }>;
  account_code?: string;
  expense_account_code?: string;
  description?: string;
}) => {
  try {
    const response = await api.post('/schedules', scheduleData);
    if (response.data.success) {
      console.log('Schedule created:', response.data.schedule_id);
      return response.data.schedule;
    }
  } catch (error) {
    console.error('Failed to create schedule:', error);
    throw error;
  }
};

// 4. Complete workflow in React component
const ManualScheduleCreation = ({ transaction }) => {
  const [preview, setPreview] = useState(null);
  const [editedEntries, setEditedEntries] = useState([]);
  const [accountCodes, setAccountCodes] = useState({
    account_code: '',
    expense_account_code: ''
  });

  const handleGeneratePreview = async () => {
    try {
      const previewData = await generateSchedulePreview({
        amount: transaction.totalAmount,
        start_date: '2024-01-01', // User input
        end_date: '2024-12-31',   // User input
        entity_id: transaction.entity_id,
        calculation_method: 'auto'
      });
      
      setPreview(previewData);
      setEditedEntries(previewData.monthly_entries);
    } catch (error) {
      alert('Failed to generate preview');
    }
  };

  const handleSaveSchedule = async () => {
    try {
      const schedule = await createFinalSchedule({
        transaction_id: transaction.id,
        amount: preview.total_amount,
        start_date: preview.start_date,
        end_date: preview.end_date,
        calculation_method: preview.calculation_method,
        monthly_entries: editedEntries,
        ...accountCodes
      });
      
      // Redirect to schedule details or refresh transaction
      onScheduleCreated(schedule);
    } catch (error) {
      alert('Failed to create schedule');
    }
  };

  const handleEntryAmountChange = (index: number, newAmount: number) => {
    setEditedEntries(prev => prev.map((entry, idx) => 
      idx === index ? { ...entry, amount: newAmount } : entry
    ));
  };

  return (
    <div>
      <button onClick={handleGeneratePreview}>Generate Preview</button>
      
      {preview && (
        <div>
          <h3>Preview (Editable)</h3>
          {editedEntries.map((entry, index) => (
            <div key={index}>
              <span>{entry.month_date}: </span>
              <input 
                type="number" 
                value={entry.amount}
                onChange={(e) => handleEntryAmountChange(index, parseFloat(e.target.value))}
              />
            </div>
          ))}
          
          <input 
            placeholder="Asset Account Code"
            value={accountCodes.account_code}
            onChange={(e) => setAccountCodes(prev => ({...prev, account_code: e.target.value}))}
          />
          <input 
            placeholder="Expense Account Code"
            value={accountCodes.expense_account_code}
            onChange={(e) => setAccountCodes(prev => ({...prev, expense_account_code: e.target.value}))}
          />
          
          <button onClick={handleSaveSchedule}>Create Schedule</button>
        </div>
      )}
    </div>
  );
};
```

**Error Handling:**
```typescript
// Common error scenarios for manual schedule creation:
// 400 - Invalid transaction ID
// 400 - Invalid date range
// 400 - Missing required fields
// 404 - Transaction not found
// 403 - No access to client
```

### **Frontend Integration Notes**

#### **LLM Confidence Warnings**
```typescript
// Use detection_method instead of status
const needsReview = schedule.detection_method === 'llm_only';
if (needsReview) {
  showWarning("⚠️ AI Detected - Please Review Carefully");
}
```

#### **Calculation Method Display**
```typescript
const methodLabel = schedule.calculation_method === 'day_based' 
  ? 'Day-based Distribution' 
  : 'Equal Monthly Distribution';
```

#### **Status Filters**
```typescript
// For actionable items (need user attention)
const actionableFilters = ['pending_configuration', 'proposed'];

// For all status options (verified against schedule.types.ts)
const allStatusFilters = [
  'pending_configuration',
  'pending_confirmation',
  'proposed', 
  'confirmed',
  'posted',
  'partially_posted',
  'skipped',
  'cancelled',
  'error'
];
```

## Technology Stack
- **React 18.3.1** with TypeScript
- **Vite** for build tooling
- **Tailwind CSS** with shadcn/ui components
- **Firebase** for authentication and hosting
- **Zustand** for state management
- **React Router DOM** for navigation
- **React Hook Form** with Zod validation
- **Axios** for API calls
- **Vitest** for testing

## Service Layer Documentation

### **Service Architecture**
The application uses a service layer pattern with 9 dedicated services for different domains:

#### **1. ClientsService (`src/services/clients.service.ts`)**
**Purpose:** Client management operations
**Key Methods:**
```typescript
// Get all clients (legacy)
ClientsService.getClients(): Promise<ClientSummary[]>

// Get clients with pagination and filtering
ClientsService.getClientsEnhanced(filters?: ClientFilters): Promise<ClientListResponse>

// Create new client (legacy)
ClientsService.createClient(data: CreateClientData): Promise<{ message: string; client_id: string }>

// Create client with enhanced data structure
ClientsService.createClientEnhanced(data: ClientCreate): Promise<CreateClientResponse>

// Create client using multi-step wizard
ClientsService.createClientFromWizard(step1, step2, step3): Promise<CreateClientResponse>

// Get specific client details
ClientsService.getClient(clientId: string): Promise<ClientSummary>
ClientsService.getClientDetails(clientId: string): Promise<ClientResponse>

// Update client
ClientsService.updateClient(clientId, data: UpdateClientData): Promise<void>
ClientsService.updateClientEnhanced(clientId, data: ClientUpdate): Promise<UpdateClientResponse>

// Delete client (soft delete)
ClientsService.deleteClient(clientId: string): Promise<DeleteClientResponse>

// Get client form options
ClientsService.getClientEnums(): Promise<ClientEnums>
```

#### **2. DashboardService (`src/services/dashboard.service.ts`)**
**Purpose:** Dashboard data aggregation
**Key Methods:**
```typescript
// Get firm-level dashboard with client summary
DashboardService.getFirmDashboard(filters?: FirmDashboardFilters): Promise<FirmDashboardResponse>

// Get client-specific dashboard
DashboardService.getClientDashboard(clientId: string, entityId?: string): Promise<DashboardData>
```

#### **3. PrepaymentsService (`src/services/prepayments.service.ts`)**
**Purpose:** Prepayment and amortization schedule management
**Key Methods:**
```typescript
// Get prepayments dashboard data
PrepaymentsService.getPrepaymentsData(filters: PrepaymentsFilters): Promise<PrepaymentsResponse>

// Schedule operations
PrepaymentsService.getSchedule(scheduleId: string): Promise<any>
PrepaymentsService.updateSchedule(scheduleId: string, data: Partial<ScheduleData>): Promise<any>
PrepaymentsService.confirmSchedule(scheduleId: string): Promise<void>
PrepaymentsService.skipSchedule(scheduleId: string, reason: string): Promise<void>

// Schedule calculations
PrepaymentsService.calculatePreview(data: {
  amount: number;
  start_date: string;
  end_date: string;
  calculation_method: 'auto' | 'day_based' | 'equal_monthly';
  entity_id: string;
}): Promise<PreviewResponse>

PrepaymentsService.recalculateSchedule(scheduleId: string, data: any): Promise<any>
PrepaymentsService.previewChanges(scheduleId: string, data: any): Promise<any>

// Monthly entry operations
PrepaymentsService.updateMonthlyEntry(scheduleId: string, entryIndex: number, amount: number): Promise<any>

// Xero posting operations
PrepaymentsService.postReadyEntries(scheduleId: string, entryIndices?: number[]): Promise<any>
PrepaymentsService.postSingleEntry(scheduleId: string, entryIndex: number): Promise<any>

// Attachment handling
PrepaymentsService.getAttachmentUrl(attachmentId: string): Promise<string>
```

#### **4. EntitiesService (`src/services/entities.service.ts`)**
**Purpose:** Entity (organization) management
**Key Methods:**
```typescript
// Get entities for client
EntitiesService.getEntitiesForClient(clientId: string): Promise<EntityResponse[]>

// Get entity details
EntitiesService.getEntity(entityId: string): Promise<EntityResponse>

// Create entity from wizard
EntitiesService.createEntityFromWizard(step1: EntityWizardStep1, step2: EntityWizardStep2): Promise<any>

// Entity settings management
EntitiesService.updateEntitySettings(entityId: string, settings: any): Promise<any>

// Connection management
EntitiesService.checkEntityConnectionStatus(entityId: string): Promise<any>
EntitiesService.disconnectEntity(entityId: string): Promise<any>
```

#### **5. AuthService (`src/services/auth.service.ts`)**
**Purpose:** Firebase authentication wrapper
**Key Methods:**
```typescript
// Authentication operations
AuthService.signIn(email: string, password: string): Promise<UserCredential>
AuthService.signUp(email: string, password: string): Promise<UserCredential>
AuthService.signOut(): Promise<void>

// Error handling
AuthService.handleFirebaseError(error: any): string
```

#### **6. UsersService (`src/services/users.service.ts`)**
**Purpose:** User management operations
**Key Methods:**
```typescript
// User profile operations
UsersService.getUserProfile(userId: string): Promise<UserProfile>
UsersService.updateUserProfile(userId: string, data: any): Promise<any>
```

#### **7. FirmService (`src/services/firm.service.ts`)**
**Purpose:** Firm-level operations
**Key Methods:**
```typescript
// Firm management
FirmService.getFirmDetails(): Promise<FirmDetails>
FirmService.updateFirmSettings(settings: any): Promise<any>
```

#### **8. PostProcessingService (`src/services/post-processing.service.ts`)**
**Purpose:** Post-processing operations for schedules
**Key Methods:**
```typescript
// Post-processing workflows
PostProcessingService.processSchedule(scheduleId: string): Promise<any>
PostProcessingService.validateSchedule(scheduleId: string): Promise<any>
```

#### **9. TokenUsageService (`src/services/tokenUsage.service.ts`)**
**Purpose:** Token usage tracking for LLM operations
**Key Methods:**
```typescript
// Token usage tracking
TokenUsageService.trackTokenUsage(data: TokenUsageData): Promise<any>
TokenUsageService.getTokenUsageSummary(): Promise<TokenUsageSummary>
```

### **Service Usage Patterns**

#### **Error Handling**
All services follow consistent error handling:
```typescript
try {
  const result = await ClientsService.getClients();
  // Handle success
} catch (error) {
  console.error('Service error:', error);
  // All services throw descriptive Error objects
  toast.error(error.message || 'Operation failed');
}
```

#### **Loading States**
Services are typically used with loading states:
```typescript
const [loading, setLoading] = useState(false);
const [data, setData] = useState(null);

const fetchData = async () => {
  setLoading(true);
  try {
    const result = await PrepaymentsService.getPrepaymentsData(filters);
    setData(result);
  } catch (error) {
    // Handle error
  } finally {
    setLoading(false);
  }
};
```

#### **Service Dependencies**
- All services use the centralized `api` client from `@/lib/api`
- Services handle authentication automatically via API client
- Type safety is enforced through TypeScript interfaces

## Development Commands
```bash
# Development server
npm run dev

# Build for production
npm run build

# Lint code
npm run lint

# Run tests
npm test

# Run tests with UI
npm run test:ui

# Run tests once
npm run test:run

# Preview production build
npm run preview
```

## Project Structure
- `src/components/` - Reusable UI components
  - `ui/` - shadcn/ui components
  - `auth/` - Authentication components
  - `layout/` - Layout components
  - `entities/` - Entity management components
- `src/pages/` - Page components
- `src/features/` - Feature-based code organization
- `src/hooks/` - Custom React hooks
- `src/services/` - API service layers
- `src/store/` - Zustand state stores
- `src/types/` - TypeScript type definitions
- `src/lib/` - Utility functions

## Code Quality
- TypeScript strict mode enabled
- ESLint with React-specific rules
- Prettier formatting (via ESLint)
- Component testing with React Testing Library

## Key Features
- Firebase Authentication integration
- Entity management with Xero integration
- Dashboard with client data visualization
- Prepayments and transaction handling
- Responsive design with mobile support

## API Integration
- Backend API at configurable endpoint
- Authentication via Firebase tokens
- Error handling and loading states
- API client in `src/lib/api.ts`

## Testing Strategy
- Unit tests for utilities and hooks
- Component tests for UI components
- Integration tests for API interactions
- Test files co-located with source code

## Development Notes
- Uses absolute imports with path mapping
- Follows React best practices and hooks patterns
- Implements proper error boundaries
- Uses React Suspense for code splitting
- **NEVER hardcode data values in components - always fetch from APIs**
- If API fails, show empty state or error - do not use fallback hardcoded data

## Documentation Validation Metadata

### **Last Updated:** 2025-01-15
### **Verification Status:**
- ✅ **React Version**: Verified against package.json (18.3.1)
- ✅ **API Status System**: Verified against schedule.types.ts (includes pending_confirmation)
- ✅ **Service Layer**: All 9 services documented with actual methods
- ✅ **Component Structure**: Verified against actual src/ directory
- ✅ **API Endpoints**: 85% verified against api.ts implementation
- ⚠️ **Missing Implementations**: Manual schedule creation, direct status updates

### **Known Discrepancies:**
1. **Manual Schedule Creation**: Documented but not implemented (`POST /schedules`)
2. **Bulk-Post Format**: Documentation shows `{entry_indices: [...]}`, implementation uses direct array
3. **~30 Undocumented API Methods**: Exist in api.ts but not documented here

### **Maintenance Schedule:**
- **Monthly**: Verify API endpoints against backend changes
- **Quarterly**: Update dependency versions and component structure
- **On Major Updates**: Full validation of all documented features

### **How to Validate:**
1. Compare package.json versions with documented versions
2. Check schedule.types.ts for status system changes
3. Verify api.ts methods against documented endpoints
4. Test documented code examples in actual development environment