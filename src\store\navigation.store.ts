import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { DEFAULT_STATUS_FILTERS } from '@/constants/status-filters';

interface NavigationState {
  // ACCPAY page selections
  accpay: {
    selectedClientId: string | null;
    selectedEntityId: string | 'all';
    selectedStatusFilters: string[];
  };
  
  // Bills Amortization page selections
  billsAmortization: {
    selectedClientId: string | null;
    selectedEntityId: string | null; // No 'all' option for Bills Amortization
    selectedStatusFilters: string[];
  };
  
  // Actions
  setAccpayClientId: (clientId: string | null) => void;
  setAccpayEntityId: (entityId: string | 'all') => void;
  setAccpayStatusFilters: (filters: string[]) => void;
  setAccpaySelections: (clientId: string | null, entityId: string | 'all') => void;
  clearAccpaySelections: () => void;
  
  // Bills Amortization actions
  setBillsAmortizationClientId: (clientId: string | null) => void;
  setBillsAmortizationEntityId: (entityId: string | null) => void;
  setBillsAmortizationStatusFilters: (filters: string[]) => void;
  setBillsAmortizationSelections: (clientId: string | null, entityId: string | null) => void;
  clearBillsAmortizationSelections: () => void;
}

export const useNavigationStore = create<NavigationState>()(
  persist(
    (set, get) => ({
      // Initial state
      accpay: {
        selectedClientId: null,
        selectedEntityId: 'all',
        selectedStatusFilters: [...DEFAULT_STATUS_FILTERS]
      },
      
      billsAmortization: {
        selectedClientId: null,
        selectedEntityId: null,
        selectedStatusFilters: [...DEFAULT_STATUS_FILTERS]
      },

      // Actions
      setAccpayClientId: (clientId: string | null) => {
        set((state) => ({
          accpay: {
            ...state.accpay,
            selectedClientId: clientId,
            // Keep existing entity selection - let components decide if reset is needed
          }
        }));
      },

      setAccpayEntityId: (entityId: string | 'all') => {
        console.log('🔄 Navigation store: Setting entity ID to:', entityId);
        set((state) => ({
          accpay: {
            ...state.accpay,
            selectedEntityId: entityId
          }
        }));
      },

      setAccpayStatusFilters: (filters: string[]) => {
        set((state) => ({
          accpay: {
            ...state.accpay,
            selectedStatusFilters: filters
          }
        }));
      },

      setAccpaySelections: (clientId: string | null, entityId: string | 'all') => {
        set((state) => ({
          accpay: {
            ...state.accpay,
            selectedClientId: clientId,
            selectedEntityId: entityId
          }
        }));
      },

      clearAccpaySelections: () => {
        set((state) => ({
          accpay: {
            selectedClientId: null,
            selectedEntityId: 'all',
            selectedStatusFilters: [...DEFAULT_STATUS_FILTERS]
          }
        }));
      },

      // Bills Amortization actions
      setBillsAmortizationClientId: (clientId: string | null) => {
        set((state) => ({
          billsAmortization: {
            ...state.billsAmortization,
            selectedClientId: clientId,
            // Reset entity when client changes
            selectedEntityId: null
          }
        }));
      },

      setBillsAmortizationEntityId: (entityId: string | null) => {
        console.log('🔄 Navigation store: Setting Bills Amortization entity ID to:', entityId);
        set((state) => ({
          billsAmortization: {
            ...state.billsAmortization,
            selectedEntityId: entityId
          }
        }));
      },

      setBillsAmortizationStatusFilters: (filters: string[]) => {
        console.log('🔄 Navigation store: Setting Bills Amortization status filters to:', filters);
        set((state) => ({
          billsAmortization: {
            ...state.billsAmortization,
            selectedStatusFilters: filters
          }
        }));
      },

      setBillsAmortizationSelections: (clientId: string | null, entityId: string | null) => {
        set((state) => ({
          billsAmortization: {
            ...state.billsAmortization,
            selectedClientId: clientId,
            selectedEntityId: entityId
          }
        }));
      },

      clearBillsAmortizationSelections: () => {
        set((state) => ({
          billsAmortization: {
            selectedClientId: null,
            selectedEntityId: null,
            selectedStatusFilters: [...DEFAULT_STATUS_FILTERS]
          }
        }));
      },
    }),
    {
      name: 'navigation-store', // localStorage key
      partialize: (state) => ({ 
        accpay: state.accpay,
        billsAmortization: state.billsAmortization
      }), // Persist both accpay and billsAmortization selections
    }
  )
);

// Selectors for easier access
export const useAccpaySelections = () => {
  const { accpay, setAccpayClientId, setAccpayEntityId, setAccpayStatusFilters, setAccpaySelections } = useNavigationStore();
  
  return {
    selectedClientId: accpay.selectedClientId,
    selectedEntityId: accpay.selectedEntityId,
    selectedStatusFilters: accpay.selectedStatusFilters,
    setClientId: setAccpayClientId,
    setEntityId: setAccpayEntityId,
    setStatusFilters: setAccpayStatusFilters,
    setSelections: setAccpaySelections,
  };
};

export const useBillsAmortizationSelections = () => {
  const { 
    billsAmortization, 
    setBillsAmortizationClientId, 
    setBillsAmortizationEntityId, 
    setBillsAmortizationStatusFilters,
    setBillsAmortizationSelections 
  } = useNavigationStore();
  
  return {
    selectedClientId: billsAmortization.selectedClientId,
    selectedEntityId: billsAmortization.selectedEntityId,
    selectedStatusFilters: billsAmortization.selectedStatusFilters || [...DEFAULT_STATUS_FILTERS],
    setClientId: setBillsAmortizationClientId,
    setEntityId: setBillsAmortizationEntityId,
    setStatusFilters: setBillsAmortizationStatusFilters,
    setSelections: setBillsAmortizationSelections,
  };
};