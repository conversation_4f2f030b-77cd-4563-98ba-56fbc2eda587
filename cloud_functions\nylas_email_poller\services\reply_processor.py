"""
Reply Processing Service

Handles email reply workflows with Cloud Tasks integration and Firestore tracking.
Provides template management and async processing for scalable reply operations.
"""

import os
import logging
from typing import Dict, Optional
from datetime import datetime
from google.cloud import firestore, tasks_v2
from .nylas_service import NylasService

logger = logging.getLogger(__name__)

class ReplyProcessor:
    """Service for processing email replies with async task management"""
    
    def __init__(self):
        project_id = os.getenv("GCP_PROJECT_ID")
        if not project_id:
            raise ValueError("GCP_PROJECT_ID must be set")
        
        self.db = firestore.Client(project=project_id)
        self.nylas_service = NylasService()
        self.project_id = project_id
        
        # Cloud Tasks client for async processing
        try:
            self.tasks_client = tasks_v2.CloudTasksClient()
            self.queue_path = self.tasks_client.queue_path(
                project_id, 
                os.getenv("CLOUD_TASKS_LOCATION", "europe-west2"), 
                "reply-queue"
            )
        except Exception as e:
            logger.warning(f"Cloud Tasks not available: {str(e)}")
            self.tasks_client = None
    
    def validate_reply_permissions(self, entity_id: str, user_id: str, thread_id: str) -> bool:
        """
        Validate that user has permission to reply on behalf of entity
        
        Args:
            entity_id: Entity identifier
            user_id: User attempting to send reply
            thread_id: Thread being replied to
            
        Returns:
            Boolean indicating if reply is permitted
        """
        try:
            # Check if user has access to entity
            entity_ref = self.db.collection('ENTITIES').document(entity_id)
            entity_doc = entity_ref.get()
            
            if not entity_doc.exists:
                logger.warning(f"Entity {entity_id} not found")
                return False
            
            # Check if thread exists for this entity
            thread_ref = entity_ref.collection('EMAIL_THREADS').document(thread_id)
            thread_doc = thread_ref.get()
            
            if not thread_doc.exists:
                logger.warning(f"Thread {thread_id} not found for entity {entity_id}")
                return False
            
            # TODO: Add user permission validation when auth system is integrated
            # For now, assume permission granted if entity and thread exist
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating reply permissions: {str(e)}")
            return False
    
    def enqueue_reply_task(self, reply_data: Dict) -> Dict:
        """
        Enqueue reply processing task for async execution
        
        Args:
            reply_data: Reply information dictionary
            
        Returns:
            Task enqueue result
        """
        try:
            if not self.tasks_client:
                # Fallback to synchronous processing if Cloud Tasks unavailable
                logger.info("Cloud Tasks unavailable, processing reply synchronously")
                return self.process_reply_sync(reply_data)
            
            # Create task payload
            task_payload = {
                "reply_data": reply_data,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # Create Cloud Task
            task = {
                "http_request": {
                    "http_method": tasks_v2.HttpMethod.POST,
                    "url": f"https://{os.getenv('CLOUD_RUN_URL', 'localhost:8081')}/process-reply",
                    "headers": {"Content-Type": "application/json"},
                    "body": str(task_payload).encode()
                }
            }
            
            # Enqueue task
            response = self.tasks_client.create_task(
                request={"parent": self.queue_path, "task": task}
            )
            
            logger.info(f"Reply task enqueued: {response.name}")
            return {"success": True, "task_name": response.name}
            
        except Exception as e:
            logger.error(f"Error enqueuing reply task: {str(e)}")
            # Fallback to synchronous processing
            return self.process_reply_sync(reply_data)
    
    def process_reply_sync(self, reply_data: Dict) -> Dict:
        """
        Process reply synchronously (fallback or direct processing)
        
        Args:
            reply_data: Reply information dictionary
            
        Returns:
            Processing result
        """
        try:
            entity_id = reply_data["entity_id"]
            thread_id = reply_data["thread_id"]
            
            # Create reply tracking document
            reply_doc_data = {
                "thread_id": thread_id,
                "entity_id": entity_id,
                "to_email": reply_data["to_email"],
                "subject": reply_data["subject"],
                "body": reply_data["body"],
                "reply_to_message_id": reply_data["reply_to_message_id"],
                "status": "pending",
                "created_at": datetime.utcnow(),
                "created_by": reply_data.get("user_id", "system"),
                "template_used": reply_data.get("template", None)
            }
            
            # Store reply document
            thread_ref = self.db.collection('ENTITIES').document(entity_id).collection('EMAIL_THREADS').document(thread_id)
            reply_ref = thread_ref.collection('REPLIES').document()
            reply_ref.create(reply_doc_data)
            
            reply_id = reply_ref.id
            logger.info(f"Created reply tracking document: {reply_id}")
            
            # Send reply via Nylas
            send_result = self.nylas_service.send_reply(reply_data)
            
            # Update reply status based on result
            if send_result["success"]:
                reply_ref.update({
                    "status": "sent",
                    "external_message_id": send_result["external_message_id"],
                    "sent_at": datetime.utcnow(),
                    "nylas_request_id": send_result.get("request_id")
                })
                logger.info(f"Reply sent successfully: {reply_id}")
                return {"success": True, "reply_id": reply_id, "external_message_id": send_result["external_message_id"]}
            else:
                reply_ref.update({
                    "status": "failed",
                    "error_message": send_result["error"],
                    "failed_at": datetime.utcnow()
                })
                logger.error(f"Reply failed: {reply_id} - {send_result['error']}")
                return {"success": False, "reply_id": reply_id, "error": send_result["error"]}
                
        except Exception as e:
            error_msg = f"Error processing reply: {str(e)}"
            logger.error(error_msg)
            return {"success": False, "error": error_msg}
    
    def get_reply_templates(self) -> Dict:
        """
        Get available reply templates for DRCR-specific responses
        
        Returns:
            Dictionary of template categories and content
        """
        return {
            "acknowledgment": {
                "document_received": {
                    "subject": "Re: {original_subject}",
                    "body": "Thank you for sending your document(s). We have received them and will begin processing shortly. You will receive an update once processing is complete."
                },
                "multiple_documents": {
                    "subject": "Re: {original_subject}",
                    "body": "Thank you for sending your {document_count} documents totaling ${total_amount}. We have received them and will begin processing shortly."
                }
            },
            "status_updates": {
                "processing_complete": {
                    "subject": "Re: {original_subject} - Processing Complete",
                    "body": "Your document(s) have been processed successfully and exported to Xero. Please check your accounting system for the updated entries."
                },
                "exported_to_xero": {
                    "subject": "Re: {original_subject} - Exported to Xero",
                    "body": "Your documents have been successfully processed and exported to your Xero accounting system. All prepayment schedules have been created automatically."
                }
            },
            "error_handling": {
                "attachment_issue": {
                    "subject": "Re: {original_subject} - Attachment Issue",
                    "body": "We encountered an issue processing your attachment. Please ensure the file is a valid PDF or image format and try sending it again."
                },
                "missing_attachment": {
                    "subject": "Re: {original_subject} - Missing Attachment",
                    "body": "It appears your email may be missing an attachment. Please resend with the required documents attached."
                },
                "corrupted_file": {
                    "subject": "Re: {original_subject} - File Issue",
                    "body": "The attached file appears to be corrupted or unreadable. Please resend with a fresh copy of the document."
                }
            },
            "requests": {
                "need_more_info": {
                    "subject": "Re: {original_subject} - Additional Information Needed",
                    "body": "Thank you for your submission. We need some additional information to process your request. Please provide: {details_needed}"
                }
            }
        }
    
    def apply_template(self, template_category: str, template_name: str, variables: Dict) -> Dict:
        """
        Apply template with variable substitution
        
        Args:
            template_category: Category of template (e.g., "acknowledgment")
            template_name: Specific template name (e.g., "document_received")
            variables: Dictionary of variables for substitution
            
        Returns:
            Template with variables applied
        """
        try:
            templates = self.get_reply_templates()
            template = templates.get(template_category, {}).get(template_name)
            
            if not template:
                raise ValueError(f"Template not found: {template_category}.{template_name}")
            
            # Apply variable substitution
            subject = template["subject"].format(**variables)
            body = template["body"].format(**variables)
            
            return {
                "subject": subject,
                "body": body,
                "template": f"{template_category}.{template_name}"
            }
            
        except Exception as e:
            logger.error(f"Error applying template: {str(e)}")
            return {
                "subject": "Re: {original_subject}".format(**variables),
                "body": "Thank you for your email. We have received it and will respond shortly.",
                "template": "fallback"
            }