"""
Narration Helper for Journal Entries

Handles building narration text for Xero journal entries with:
- Custom narration precedence over auto-generated
- Length validation (500 char limit)
- Consistent formatting across all journal posting functions
"""

from typing import Dict, Any, Optional
from datetime import datetime
from fastapi import HTTPException


def build_narration(schedule_data: Dict[str, Any], entry_context: Dict[str, Any]) -> str:
    """
    Build narration text for journal entries with precedence:
    1. Custom narration (if provided) - literal use
    2. Auto-generated default pattern
    
    Args:
        schedule_data: Dictionary containing schedule information
        entry_context: Dictionary containing entry-specific context:
            - transaction_details: Transaction metadata
            - entry_index: Current entry index (0-based)
            - total_entries: Total number of entries
            
    Returns:
        Formatted narration string (max 500 characters)
        
    Raises:
        HTTPException: 422 if custom narration exceeds 500 characters
    """
    # Check for custom narration first
    custom_narration = schedule_data.get("custom_narration") or schedule_data.get("narration")
    if custom_narration and custom_narration.strip():
        # Use custom narration literally
        narration = custom_narration.strip()
        
        # Validate length
        if len(narration) > 500:
            raise HTTPException(
                status_code=422,
                detail=f"Custom narration exceeds maximum length of 500 characters (current: {len(narration)})"
            )
        
        return narration
    
    # Fall back to auto-generated narration
    return _build_auto_generated_narration(schedule_data, entry_context)


def _build_auto_generated_narration(schedule_data: Dict[str, Any], entry_context: Dict[str, Any]) -> str:
    """
    Build auto-generated narration using the standard template:
    "Amortization: {supplier_name} - {invoice_ref} - {invoice_date} - {entry_info}"
    
    Args:
        schedule_data: Schedule information
        entry_context: Entry-specific context
        
    Returns:
        Auto-generated narration string
    """
    transaction_details = entry_context.get("transaction_details")
    entry_index = entry_context.get("entry_index", 0)
    total_entries = entry_context.get("total_entries")
    
    # Extract supplier name
    supplier_name = "Unknown Supplier"
    if transaction_details and hasattr(transaction_details, 'metadata') and transaction_details.metadata:
        contact_name = transaction_details.metadata.get('contact_name')
        if contact_name and contact_name.strip():
            supplier_name = contact_name.strip()
    
    # Extract invoice reference
    invoice_ref = "Invoice"
    if transaction_details:
        if hasattr(transaction_details, 'document_number') and transaction_details.document_number:
            invoice_ref = transaction_details.document_number.strip()
        elif schedule_data.get('transactionReference'):
            invoice_ref = schedule_data['transactionReference'].strip()
    
    # Extract and format invoice date
    invoice_date = ""
    if transaction_details and hasattr(transaction_details, 'date_issued') and transaction_details.date_issued:
        if isinstance(transaction_details.date_issued, datetime):
            invoice_date = transaction_details.date_issued.strftime('%d/%m/%Y')
        elif hasattr(transaction_details.date_issued, 'strftime'):
            invoice_date = transaction_details.date_issued.strftime('%d/%m/%Y')
    
    # Build entry information
    if total_entries and total_entries > 1:
        entry_info = f"{entry_index + 1}/{total_entries}"
    else:
        entry_info = f"Entry {entry_index + 1}"
    
    # Construct narration
    parts = ["Amortization", supplier_name]
    
    if invoice_ref and invoice_ref != "Invoice":
        parts.append(invoice_ref)
    
    if invoice_date:
        parts.append(invoice_date)
        
    parts.append(entry_info)
    
    narration = " - ".join(parts)
    
    # Ensure it doesn't exceed 500 characters (should never happen with auto-generated, but safety check)
    if len(narration) > 500:
        # Truncate if somehow too long
        narration = narration[:497] + "..."
    
    return narration


def validate_narration_length(narration: str) -> str:
    """
    Validate narration length and raise appropriate error if too long.
    
    Args:
        narration: Narration text to validate
        
    Returns:
        Validated narration text
        
    Raises:
        HTTPException: 422 if narration exceeds 500 characters
    """
    if len(narration) > 500:
        raise HTTPException(
            status_code=422,
            detail=f"Narration exceeds maximum length of 500 characters (current: {len(narration)})"
        )
    
    return narration.strip()