#!/usr/bin/env python3
"""
Fix Nylas permissions for sending emails
"""

import requests
import json
import os

NYLAS_API_KEY = "nyk_v0_BlT3sXqnvDnmP3R8nWgn9ngMiMW0RZL6WeforK3GcznWbIPwbLO4noOzHgBb3oqy"
NYLAS_CLIENT_ID = "ea1377ac-d472-4ed4-baeb-e45291030daf"
GRANT_ID = "45fe69de-7b6f-4237-b526-c5ce4a549427"
API_URI = "https://api.eu.nylas.com/v3"

headers = {
    'Authorization': f'Bearer {NYLAS_API_KEY}',
    'Content-Type': 'application/json'
}

def check_current_grant():
    """Check current grant permissions"""
    print("🔍 Checking current grant permissions...")
    
    url = f"{API_URI}/grants/{GRANT_ID}"
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        grant_data = response.json().get('data', {})
        print(f"✅ Grant Status: {grant_data.get('grant_status')}")
        print(f"📧 Email: {grant_data.get('email')}")
        print(f"🔐 Scopes: {grant_data.get('scopes', [])}")
        
        scopes = grant_data.get('scopes', [])
        has_send = any('compose' in scope or 'send' in scope for scope in scopes)
        
        if has_send:
            print("✅ Send permissions already granted!")
            return True
        else:
            print("❌ Missing send permissions")
            return False
    else:
        print(f"❌ Error checking grant: {response.status_code} - {response.text}")
        return False

def create_auth_url():
    """Create authentication URL with send permissions"""
    print("\n🔗 Creating authentication URL with send permissions...")
    
    auth_data = {
        "client_id": NYLAS_CLIENT_ID,
        "provider": "google",
        "redirect_uri": "https://api.us.nylas.com/v3/connect/callback",
        "scope": [
            "https://www.googleapis.com/auth/gmail.readonly",
            "https://www.googleapis.com/auth/gmail.compose",
            "https://www.googleapis.com/auth/gmail.send"
        ]
    }
    
    url = f"{API_URI}/connect/auth"
    response = requests.post(url, headers=headers, json=auth_data)
    
    if response.status_code in [200, 201]:
        auth_response = response.json().get('data', {})
        auth_url = auth_response.get('auth_url')
        
        print(f"✅ Authentication URL created!")
        print(f"🌐 Visit this URL to re-authenticate with send permissions:")
        print(f"   {auth_url}")
        print(f"\n📋 After authentication, you'll get a new grant ID to replace: {GRANT_ID}")
        
        return auth_url
    else:
        print(f"❌ Error creating auth URL: {response.status_code} - {response.text}")
        return None

def test_send_capability():
    """Test if we can send emails with current grant"""
    print("\n🧪 Testing send capability...")
    
    test_payload = {
        "to": [{"email": "<EMAIL>"}],
        "from": [{"email": "<EMAIL>"}],
        "subject": "Test Email - DO NOT SEND",
        "body": "This is a test"
    }
    
    # Just validate the payload without sending
    url = f"{API_URI}/grants/{GRANT_ID}/messages"
    
    # First try to get messages to see if grant is working
    messages_url = f"{API_URI}/grants/{GRANT_ID}/messages"
    response = requests.get(f"{messages_url}?limit=1", headers=headers)
    
    if response.status_code == 200:
        print("✅ Grant can read emails")
        
        # Now test send endpoint (this will likely fail with 403)
        print("🔄 Testing send endpoint...")
        send_response = requests.post(url, headers=headers, json=test_payload)
        
        if send_response.status_code in [200, 201]:
            print("✅ Send capability confirmed!")
            return True
        elif send_response.status_code == 403:
            print("❌ 403 Forbidden - Missing send permissions")
            return False
        else:
            print(f"❓ Unexpected response: {send_response.status_code} - {send_response.text}")
            return False
    else:
        print(f"❌ Grant not working: {response.status_code} - {response.text}")
        return False

def main():
    """Main function to fix permissions"""
    print("🔧 Nylas Email Send Permissions Fixer")
    print("=" * 50)
    
    # Step 1: Check current permissions
    has_send = check_current_grant()
    
    if has_send:
        # Test if send actually works
        can_send = test_send_capability()
        if can_send:
            print("\n🎉 Everything is working! No fixes needed.")
            return
    
    # Step 2: Test current send capability
    if not has_send:
        can_send = test_send_capability()
    
    # Step 3: Create new auth URL if needed
    if not can_send:
        print(f"\n🔧 Need to fix permissions...")
        auth_url = create_auth_url()
        
        if auth_url:
            print(f"\n📝 Next steps:")
            print(f"1. Visit the authentication URL above")
            print(f"2. Sign in with the Gmail account (<EMAIL>)")
            print(f"3. Grant all permissions including 'Compose and send emails'")
            print(f"4. Copy the new grant ID from the success page")
            print(f"5. Update your environment variables with the new grant ID")
            print(f"\n🔄 Then run the email test again!")

if __name__ == "__main__":
    main()