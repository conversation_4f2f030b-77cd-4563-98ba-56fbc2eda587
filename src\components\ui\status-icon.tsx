import React from 'react';
import { EntityStatus } from '@/types/entity.types';
import { cn } from '@/lib/utils';

interface StatusIconConfig {
  emoji: string;
  color: string;
  label: string;
  bgColor?: string;
}

// Centralized status icon mapping
const STATUS_ICON_MAP: Record<EntityStatus | 'unknown', StatusIconConfig> = {
  [EntityStatus.ACTIVE]: {
    emoji: '🟢',
    color: 'text-green-600',
    label: 'Active',
    bgColor: 'bg-green-50',
  },
  [EntityStatus.ERROR]: {
    emoji: '🔴',
    color: 'text-red-600',
    label: 'Error',
    bgColor: 'bg-red-50',
  },
  [EntityStatus.DISCONNECTED]: {
    emoji: '🟡',
    color: 'text-yellow-600',
    label: 'Disconnected',
    bgColor: 'bg-yellow-50',
  },
  [EntityStatus.SYNCING]: {
    emoji: '🔄',
    color: 'text-blue-600',
    label: 'Syncing',
    bgColor: 'bg-blue-50',
  },
  [EntityStatus.PENDING]: {
    emoji: '⏳',
    color: 'text-orange-600',
    label: 'Pending',
    bgColor: 'bg-orange-50',
  },
  [EntityStatus.INACTIVE]: {
    emoji: '⚫',
    color: 'text-gray-600',
    label: 'Inactive',
    bgColor: 'bg-gray-50',
  },
  unknown: {
    emoji: '❓',
    color: 'text-gray-500',
    label: 'Unknown',
    bgColor: 'bg-gray-50',
  },
};

interface StatusIconProps {
  status: EntityStatus | string | undefined;
  showLabel?: boolean;
  showTooltip?: boolean;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

/**
 * Reusable status icon component with consistent styling and accessibility
 * Provides tooltips for color-blind users and semantic meaning
 */
export function StatusIcon({ 
  status, 
  showLabel = false, 
  showTooltip = true,
  className,
  size = 'sm' 
}: StatusIconProps) {
  // Normalize status and get config
  const normalizedStatus = (status && Object.values(EntityStatus).includes(status as EntityStatus)) 
    ? status as EntityStatus 
    : 'unknown';
  
  const config = STATUS_ICON_MAP[normalizedStatus];
  
  // Size classes
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  const component = (
    <div className={cn(
      "inline-flex items-center gap-1",
      sizeClasses[size],
      className
    )}>
      <span 
        className={cn("flex-shrink-0", config.color)}
        role="img"
        aria-label={config.label}
      >
        {config.emoji}
      </span>
      {showLabel && (
        <span className={cn("text-xs", config.color)}>
          {config.label}
        </span>
      )}
    </div>
  );

  // Wrap with tooltip if requested
  if (showTooltip) {
    return (
      <div title={`Connection Status: ${config.label}`}>
        {component}
      </div>
    );
  }

  return component;
}

/**
 * Get status configuration for external use
 */
export function getStatusConfig(status: EntityStatus | string | undefined): StatusIconConfig {
  const normalizedStatus = (status && Object.values(EntityStatus).includes(status as EntityStatus)) 
    ? status as EntityStatus 
    : 'unknown';
  
  return STATUS_ICON_MAP[normalizedStatus];
}

/**
 * Get all available status options for dropdowns/selects
 */
export function getAllStatusOptions(): Array<{value: EntityStatus; config: StatusIconConfig}> {
  return Object.entries(STATUS_ICON_MAP)
    .filter(([key]) => key !== 'unknown')
    .map(([value, config]) => ({
      value: value as EntityStatus,
      config,
    }));
}