export interface UserFilterOption {
  value: string;
  label: string;
  count?: number;
}

export const USER_STATUS_OPTIONS: UserFilterOption[] = [
  {
    value: 'active',
    label: 'Active',
  },
  {
    value: 'inactive',
    label: 'Inactive',
  },
  {
    value: 'invited',
    label: 'Invited',
  },
  {
    value: 'suspended',
    label: 'Suspended',
  },
];

export const USER_ROLE_OPTIONS: UserFilterOption[] = [
  {
    value: 'firm_admin',
    label: 'Administrator',
  },
  {
    value: 'firm_staff',
    label: 'Staff',
  },
];

export const DEFAULT_USER_FILTER_OPTIONS: UserFilterOption[] = [
  ...USER_STATUS_OPTIONS,
  ...USER_ROLE_OPTIONS,
];

export const ACTIONABLE_USER_STATUSES = ['invited', 'suspended'];
export const STANDARD_USER_STATUSES = ['active', 'inactive', 'invited', 'suspended'];
export const USER_ROLES = ['firm_admin', 'firm_staff'];