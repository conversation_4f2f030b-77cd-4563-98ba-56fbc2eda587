"""
Comment Service - Business logic for comment system operations
Following DRCR service layer patterns with client-scoped security integration
"""
from typing import List, Optional, Dict, Any, Tuple
from google.cloud import firestore
from google.cloud.firestore import SERVER_TIMESTAMP
import logging
from datetime import datetime, timezone
import uuid
from fastapi import HTT<PERSON>Exception

from ..schemas.comment_schemas import (
    CommentCreate, CommentUpdate, CommentOut, CommentsListResponse,
    CommentFilters, CommentStats, ParentType
)
from ..core.firebase_auth import AuthUser
from ..utils.field_access import get_field

logger = logging.getLogger(__name__)


class CommentService:
    """Service for handling comment system business logic"""
    
    def __init__(self, db):
        self.db = db
    
    async def create_comment(
        self,
        comment_data: CommentCreate,
        current_user: AuthUser
    ) -> CommentOut:
        """
        Create a new comment with client-scoped security validation
        
        Args:
            comment_data: Comment creation data
            current_user: Authenticated user making the request
            
        Returns:
            CommentOut: Created comment data
            
        Raises:
            HTTPException: If validation fails or access is denied
        """
        try:
            # Validate user has access to the parent record
            await self._validate_parent_record_access(
                comment_data.parent_type,
                comment_data.parent_id,
                current_user
            )
            
            # Get client_id for the parent record
            client_id = await self._get_parent_record_client_id(
                comment_data.parent_type,
                comment_data.parent_id
            )
            
            # Validate mentioned users are within the same client scope
            if comment_data.mention_uids:
                await self._validate_mention_users(comment_data.mention_uids, client_id)
            
            # Create comment document with mention mapping
            comment_id = str(uuid.uuid4())
            
            # Create mention mapping from UIDs to display names found in text
            mention_mapping = {}
            if comment_data.mention_uids:
                for uid in comment_data.mention_uids:
                    user_info = await self._get_user_info(uid)
                    mention_mapping[uid] = user_info.get("display_name", "Unknown User")
            
            comment_doc_data = {
                "parent_type": comment_data.parent_type.value,
                "parent_id": comment_data.parent_id,
                "client_id": client_id,
                "entity_id": await self._get_parent_record_entity_id(
                    comment_data.parent_type, comment_data.parent_id
                ),
                "text": comment_data.text,
                "mentions": comment_data.mention_uids or [],
                "mention_mapping": mention_mapping,  # UID -> display_name mapping
                "created_by": current_user.uid,
                "created_at": SERVER_TIMESTAMP,
                "updated_at": None,
                "deleted": False,
                "reply_to_comment_id": None  # Future feature
            }
            
            # Save to Firestore
            doc_ref = self.db.collection("COMMENTS").document(comment_id)
            await doc_ref.set(comment_doc_data)
            
            # Retrieve the saved document to get server timestamp
            saved_doc = await doc_ref.get()
            if not saved_doc.exists:
                raise HTTPException(status_code=500, detail="Failed to create comment")
            
            # Return formatted comment
            return await self._format_comment_response(saved_doc, current_user)
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating comment: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail="Failed to create comment")
    
    async def get_comments_for_record(
        self,
        parent_type: ParentType,
        parent_id: str,
        current_user: AuthUser,
        filters: Optional[CommentFilters] = None,
        page: int = 1,
        limit: int = 25
    ) -> CommentsListResponse:
        """
        Get comments for a specific record with pagination and filtering
        
        Args:
            parent_type: Type of parent record
            parent_id: ID of parent record
            current_user: Authenticated user
            filters: Optional filters
            page: Page number (1-based)
            limit: Items per page
            
        Returns:
            CommentsListResponse: Paginated comments list
        """
        try:
            # Validate user has access to the parent record
            await self._validate_parent_record_access(parent_type, parent_id, current_user)
            
            # Build base query
            query = self.db.collection("COMMENTS").where(
                filter=firestore.FieldFilter("parent_type", "==", parent_type.value)
            ).where(
                filter=firestore.FieldFilter("parent_id", "==", parent_id)
            )
            
            # Apply filters
            if filters:
                if not filters.show_deleted:
                    query = query.where(filter=firestore.FieldFilter("deleted", "==", False))
                
                if filters.created_by:
                    query = query.where(filter=firestore.FieldFilter("created_by", "==", filters.created_by))
                
                if filters.mentions_user:
                    query = query.where(filter=firestore.FieldFilter("mentions", "array_contains", filters.mentions_user))
                
                if filters.date_from:
                    query = query.where(filter=firestore.FieldFilter("created_at", ">=", filters.date_from))
                
                if filters.date_to:
                    query = query.where(filter=firestore.FieldFilter("created_at", "<=", filters.date_to))
            else:
                # Default: exclude deleted comments
                query = query.where(filter=firestore.FieldFilter("deleted", "==", False))
            
            # Order by creation time (newest first)
            query = query.order_by("created_at", direction="DESCENDING")
            
            # Get total count
            total_docs = await query.count().get()
            total_count = total_docs[0][0].value
            
            # Apply pagination
            offset = (page - 1) * limit
            paginated_query = query.limit(limit).offset(offset)
            
            # Execute query
            docs = paginated_query.stream()
            
            # Format comments
            comments = []
            async for doc in docs:
                comment = await self._format_comment_response(doc, current_user)
                
                # Apply text search filter if specified
                if filters and filters.search_text:
                    if filters.search_text.lower() not in comment.text.lower():
                        continue
                
                comments.append(comment)
            
            # Calculate pagination info
            total_pages = (total_count + limit - 1) // limit if total_count > 0 else 0
            
            return CommentsListResponse(
                comments=comments,
                total_count=total_count,
                page=page,
                per_page=limit,
                pages=total_pages
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting comments: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail="Failed to retrieve comments")
    
    async def update_comment(
        self,
        comment_id: str,
        update_data: CommentUpdate,
        current_user: AuthUser
    ) -> CommentOut:
        """
        Update an existing comment (only by the author)
        
        Args:
            comment_id: ID of comment to update
            update_data: Updated comment data
            current_user: Authenticated user
            
        Returns:
            CommentOut: Updated comment data
        """
        try:
            # Get existing comment
            doc_ref = self.db.collection("COMMENTS").document(comment_id)
            doc = await doc_ref.get()
            
            if not doc.exists:
                raise HTTPException(status_code=404, detail="Comment not found")
            
            comment_data = doc.to_dict()
            
            # Validate ownership
            if comment_data.get("created_by") != current_user.uid:
                raise HTTPException(status_code=403, detail="Can only edit your own comments")
            
            # Validate client access
            client_id = comment_data.get("client_id")
            if not await self._user_has_client_access(current_user, client_id):
                raise HTTPException(status_code=403, detail="Access denied to this comment")
            
            # Validate mentioned users are within the same client scope
            if update_data.mention_uids:
                await self._validate_mention_users(update_data.mention_uids, client_id)
            
            # Check if text is actually different
            if update_data.text == comment_data.get("text"):
                raise HTTPException(status_code=409, detail="Text identical to current version")
            
            # Create mention mapping for updated mentions
            mention_mapping = {}
            if update_data.mention_uids:
                for uid in update_data.mention_uids:
                    user_info = await self._get_user_info(uid)
                    mention_mapping[uid] = user_info.get("display_name", "Unknown User")
            
            # Update the comment
            update_fields = {
                "text": update_data.text,
                "mentions": update_data.mention_uids or [],
                "mention_mapping": mention_mapping,
                "updated_at": SERVER_TIMESTAMP
            }
            
            await doc_ref.update(update_fields)
            
            # Retrieve updated document
            updated_doc = await doc_ref.get()
            return await self._format_comment_response(updated_doc, current_user)
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating comment: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail="Failed to update comment")
    
    async def delete_comment(
        self,
        comment_id: str,
        current_user: AuthUser
    ) -> Dict[str, Any]:
        """
        Soft delete a comment (only by the author)
        
        Args:
            comment_id: ID of comment to delete
            current_user: Authenticated user
            
        Returns:
            Dict with success status
        """
        try:
            # Get existing comment
            doc_ref = self.db.collection("COMMENTS").document(comment_id)
            doc = await doc_ref.get()
            
            if not doc.exists:
                raise HTTPException(status_code=404, detail="Comment not found")
            
            comment_data = doc.to_dict()
            
            # Validate ownership
            if comment_data.get("created_by") != current_user.uid:
                raise HTTPException(status_code=403, detail="Can only delete your own comments")
            
            # Validate client access
            client_id = comment_data.get("client_id")
            if not await self._user_has_client_access(current_user, client_id):
                raise HTTPException(status_code=403, detail="Access denied to this comment")
            
            # Soft delete
            await doc_ref.update({
                "deleted": True,
                "updated_at": SERVER_TIMESTAMP
            })
            
            return {"success": True, "message": "Comment deleted successfully"}
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting comment: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail="Failed to delete comment")
    
    # Private helper methods
    
    async def _validate_parent_record_access(
        self,
        parent_type: ParentType,
        parent_id: str,
        current_user: AuthUser
    ) -> None:
        """Validate user has access to the parent record"""
        
        if parent_type == ParentType.SCHEDULE:
            # Check if schedule exists and user has access
            schedule_doc = await self.db.collection("AMORTIZATION_SCHEDULES").document(parent_id).get()
            if not schedule_doc.exists:
                raise HTTPException(status_code=404, detail="Schedule not found")
            
            schedule_data = schedule_doc.to_dict()
            client_id = get_field(schedule_data, "client_id")
            
        elif parent_type == ParentType.TRANSACTION:
            # Check if transaction exists and user has access
            transaction_doc = await self.db.collection("TRANSACTIONS").document(parent_id).get()
            if not transaction_doc.exists:
                raise HTTPException(status_code=404, detail="Transaction not found")
                
            transaction_data = transaction_doc.to_dict()
            client_id = get_field(transaction_data, "client_id")
            
        elif parent_type == ParentType.ENTITY:
            # Check if entity exists and user has access
            entity_doc = await self.db.collection("ENTITIES").document(parent_id).get()
            if not entity_doc.exists:
                raise HTTPException(status_code=404, detail="Entity not found")
                
            entity_data = entity_doc.to_dict()
            client_id = get_field(entity_data, "client_id")
            
        else:
            # For other types, implement validation as needed
            raise HTTPException(status_code=400, detail=f"Comments not supported for {parent_type.value}")
        
        # Validate user has access to this client
        if not await self._user_has_client_access(current_user, client_id):
            raise HTTPException(status_code=403, detail="Access denied to parent record")
    
    async def _get_parent_record_client_id(self, parent_type: ParentType, parent_id: str) -> str:
        """Get client_id for the parent record"""
        
        if parent_type == ParentType.SCHEDULE:
            doc = await self.db.collection("AMORTIZATION_SCHEDULES").document(parent_id).get()
        elif parent_type == ParentType.TRANSACTION:
            doc = await self.db.collection("TRANSACTIONS").document(parent_id).get()
        elif parent_type == ParentType.ENTITY:
            doc = await self.db.collection("ENTITIES").document(parent_id).get()
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported parent type: {parent_type.value}")
        
        if not doc.exists:
            raise HTTPException(status_code=404, detail="Parent record not found")
        
        return get_field(doc.to_dict(), "client_id")
    
    async def _get_parent_record_entity_id(self, parent_type: ParentType, parent_id: str) -> Optional[str]:
        """Get entity_id for the parent record if applicable"""
        
        if parent_type == ParentType.SCHEDULE:
            doc = await self.db.collection("AMORTIZATION_SCHEDULES").document(parent_id).get()
            if doc.exists:
                return get_field(doc.to_dict(), "entity_id")
        elif parent_type == ParentType.TRANSACTION:
            doc = await self.db.collection("TRANSACTIONS").document(parent_id).get()
            if doc.exists:
                return get_field(doc.to_dict(), "entity_id")
        elif parent_type == ParentType.ENTITY:
            return parent_id  # Entity comments are on the entity itself
        
        return None
    
    async def _user_has_client_access(self, user: AuthUser, client_id: str) -> bool:
        """Check if user has access to the specified client"""
        
        # Firm admins have access to all clients in their firm
        if user.role == "firm_admin":
            return True
        
        # Staff users have access to assigned clients
        if user.assigned_client_ids and client_id in user.assigned_client_ids:
            return True
        
        return False
    
    async def _validate_mention_users(self, mention_uids: List[str], client_id: str) -> None:
        """Validate that mentioned users exist and have access to the client"""
        
        for uid in mention_uids:
            # Get user from FIRM_USERS collection
            user_query = self.db.collection("FIRM_USERS").where(
                filter=firestore.FieldFilter("user_id", "==", uid)
            )
            user_docs = user_query.stream()
            
            user_found = False
            async for user_doc in user_docs:
                user_data = user_doc.to_dict()
                
                # Check if user has access to this client
                user_role = get_field(user_data, "role")
                if user_role == "firm_admin":
                    user_found = True
                    break
                
                assigned_clients = get_field(user_data, "assigned_client_ids") or []
                if client_id in assigned_clients:
                    user_found = True
                    break
            
            if not user_found:
                raise HTTPException(
                    status_code=400, 
                    detail=f"Mentioned user {uid} not found or doesn't have access to this client"
                )
    
    async def _format_comment_response(self, doc, current_user: AuthUser) -> CommentOut:
        """Format Firestore document as CommentOut response"""
        
        comment_data = doc.to_dict()
        comment_data["comment_id"] = doc.id
        
        # Get author information
        author_uid = comment_data.get("created_by")
        author_info = await self._get_user_info(author_uid)
        
        # Format timestamps
        created_at = comment_data.get("created_at")
        updated_at = comment_data.get("updated_at")
        
        return CommentOut(
            comment_id=comment_data["comment_id"],
            parent_type=comment_data.get("parent_type"),
            parent_id=comment_data.get("parent_id"),
            client_id=comment_data.get("client_id"),
            entity_id=comment_data.get("entity_id"),
            text=comment_data.get("text"),
            mentions=comment_data.get("mentions", []),
            mention_mapping=comment_data.get("mention_mapping", {}),
            created_by=comment_data.get("created_by"),
            created_at=created_at.isoformat() if created_at else None,
            updated_at=updated_at.isoformat() if updated_at else None,
            deleted=comment_data.get("deleted", False),
            reply_to_comment_id=comment_data.get("reply_to_comment_id"),
            author_display_name=author_info.get("display_name"),
            author_email=author_info.get("email")
        )
    
    async def _get_user_info(self, uid: str) -> Dict[str, str]:
        """Get user display information"""
        
        try:
            user_query = self.db.collection("FIRM_USERS").where(
                filter=firestore.FieldFilter("user_id", "==", uid)
            )
            user_docs = user_query.stream()
            
            async for user_doc in user_docs:
                user_data = user_doc.to_dict()
                return {
                    "display_name": get_field(user_data, "display_name") or get_field(user_data, "email"),
                    "email": get_field(user_data, "email")
                }
            
            return {"display_name": "Unknown User", "email": ""}
            
        except Exception as e:
            logger.warning(f"Failed to get user info for {uid}: {str(e)}")
            return {"display_name": "Unknown User", "email": ""}