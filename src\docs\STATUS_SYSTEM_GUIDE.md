# Schedule Status System Guide

## Overview
This document outlines the unified status system for amortization schedules across the DRCR application. The system provides consistent status handling, action logic, and visual representation.

## Status Hierarchy

### Primary Statuses (Core Workflow)
1. **PENDING_CONFIGURATION**
   - **Description**: LLM-detected schedules that need account configuration
   - **User Actions**: Edit (required), Skip
   - **Color**: Orange (text-orange-600, border-orange-300, bg-orange-50)
   - **Next States**: PENDING_REVIEW (auto), PENDING_CONFIRMATION (auto), SKIPPED
   - **Auto-Progression**: Advances to PENDING_REVIEW when amortizationAccountCode is set; advances directly to PENDING_CONFIRMATION when both amortizationAccountCode and expenseAccountCode are set

2. **PENDING_REVIEW**
   - **Description**: Ready for user review and validation
   - **User Actions**: Edit, Confirm, Skip
   - **Color**: Yellow (text-yellow-600, border-yellow-300, bg-yellow-50)
   - **Next States**: PENDING_CONFIRMATION (auto), CONFIRMED, SKIPPED
   - **Auto-Progression**: Advances to PENDING_CONFIRMATION when expenseAccountCode is set

3. **PENDING_CONFIRMATION**
   - **Description**: Reviewed and ready for final confirmation
   - **User Actions**: Edit, Confirm, Skip
   - **Color**: Blue (text-blue-600, border-blue-300, bg-blue-50)
   - **Next States**: CONFIRMED, SKIPPED
   - **Auto-Progression**: None (requires manual confirmation)

4. **CONFIRMED**
   - **Description**: Confirmed and ready for posting to accounting system
   - **User Actions**: Skip (if needed)
   - **Color**: Blue (bg-blue-100, text-blue-700)
   - **Next States**: POSTED, PARTIALLY_POSTED, ERROR

5. **POSTED**
   - **Description**: Successfully posted to accounting system
   - **User Actions**: None (terminal state)
   - **Color**: Green (bg-green-600, text-white)
   - **Next States**: None (terminal)

### Intermediate Statuses
6. **PARTIALLY_POSTED**
   - **Description**: Some entries posted, some pending
   - **User Actions**: View details, potentially edit remaining
   - **Color**: Light Green (bg-green-100, text-green-700)
   - **Next States**: POSTED, ERROR

### Terminal Statuses
7. **SKIPPED**
   - **Description**: User chose to skip this schedule
   - **User Actions**: None (terminal state)
   - **Color**: Gray (text-gray-600)
   - **Next States**: None (terminal)

8. **CANCELLED**
   - **Description**: Cancelled by user or system
   - **User Actions**: None (terminal state)
   - **Color**: Gray (text-gray-600)
   - **Next States**: None (terminal)

9. **ERROR**
   - **Description**: Error occurred during processing
   - **User Actions**: Review error, potentially retry
   - **Color**: Red (destructive variant)
   - **Next States**: Depends on error resolution

10. **EXCLUDED**
    - **Description**: Line item excluded from amortization
    - **User Actions**: None (terminal state)
    - **Color**: Gray (text-gray-500)
    - **Next States**: None (terminal)

### Legacy Statuses (Compatibility)
- **PROPOSED**: Maps to PENDING_REVIEW
- **REQUIRES_ACTION**: Maps to PENDING_CONFIGURATION
- **VALIDATION_FAILED**: Maps to ERROR
- **FULLY_POSTED**: Maps to POSTED
- **ERROR_POSTING**: Maps to ERROR

## Action Logic

### Helper Functions
```typescript
// Check if status requires user action
isActionNeededStatus(status): boolean
// Returns true for: PENDING_CONFIGURATION, PENDING_REVIEW, PENDING_CONFIRMATION

// Check if schedule can be edited
isEditableStatus(status): boolean
// Returns true for: PENDING_CONFIGURATION, PENDING_REVIEW, PENDING_CONFIRMATION

// Check if schedule can be confirmed
isConfirmableStatus(status): boolean
// Returns true for: PENDING_CONFIGURATION, PENDING_REVIEW, PENDING_CONFIRMATION

// Check if schedule can be skipped
isSkippableStatus(status): boolean
// Returns true for: PENDING_CONFIGURATION, PENDING_REVIEW, PENDING_CONFIRMATION, CONFIRMED

// Check if status is terminal (no further actions)
isTerminalStatus(status): boolean
// Returns true for: POSTED, SKIPPED, CANCELLED, ERROR, EXCLUDED
```

### Action Button Matrix

| Status | Edit | Confirm | Skip | Color |
|--------|------|---------|------|-------|
| PENDING_CONFIGURATION | Yes | Yes | Yes | Orange |
| PENDING_REVIEW | Yes | Yes | Yes | Yellow |
| PENDING_CONFIRMATION | Yes | Yes | Yes | Blue |
| CONFIRMED | No | No | Yes | Blue |
| POSTED | No | No | No | Green |
| PARTIALLY_POSTED | No | No | No | Light Green |
| SKIPPED | No | No | No | Gray |
| CANCELLED | No | No | No | Gray |
| ERROR | No | No | No | Red |
| EXCLUDED | No | No | No | Gray |

## Visual Components

### StatusBadge Component
```typescript
<StatusBadge 
  status={ScheduleStatus.PENDING_CONFIGURATION} 
  size="md" 
/>
```

**Props:**
- `status`: ScheduleStatus enum value
- `size`: 'sm' | 'md' | 'lg' (default: 'md')
- `className`: additional CSS classes

### Usage Examples

#### In PrepaymentsPage (Line Items)
```typescript
// Action buttons based on status
{isActionNeededStatus(line.overallStatus) && (
  <div className="flex space-x-1 mt-1">
    {isConfirmableStatus(line.overallStatus) && (
      <Button onClick={() => handleLineConfirm(line.scheduleId)}>
        Confirm
      </Button>
    )}
    {isEditableStatus(line.overallStatus) && (
      <Button onClick={() => handleEditSchedule(line, invoice)}>
        Edit
      </Button>
    )}
    {isSkippableStatus(line.overallStatus) && (
      <Button onClick={() => handleLineSkip(line.scheduleId)}>
        Skip
      </Button>
    )}
  </div>
)}
```

#### In EditScheduleModal
```typescript
const canEdit = scheduleData?.status && isEditableStatus(mapBackendScheduleStatus(scheduleData.status));
const canConfirm = scheduleData?.status && isConfirmableStatus(mapBackendScheduleStatus(scheduleData.status));
const canSkip = scheduleData?.status && isSkippableStatus(mapBackendScheduleStatus(scheduleData.status));
```

#### In BulkEditScheduleModal
```typescript
<StatusBadge status={mapBackendScheduleStatus(item.status)} size="sm" />
```

## Automatic Status Progression

### Overview
The system now automatically advances schedule statuses when required fields are completed during save operations. This eliminates the need for users to manually advance statuses when all criteria are met.

### Progression Rules
1. **PENDING_CONFIGURATION → PENDING_REVIEW**: Triggered when `amortizationAccountCode` is set
2. **PENDING_CONFIGURATION → PENDING_CONFIRMATION**: Triggered when both `amortizationAccountCode` AND `expenseAccountCode` are set (skips PENDING_REVIEW)
3. **PENDING_REVIEW → PENDING_CONFIRMATION**: Triggered when `expenseAccountCode` is set
4. **PENDING_CONFIRMATION → CONFIRMED**: Only via explicit confirmation action (manual)

### Implementation Details
- Status progression occurs during the `PUT /schedules/{id}` endpoint
- The backend validates field completion and determines appropriate status advancement
- Response includes `status_progression` object when advancement occurs
- Frontend logs progression and refreshes data to reflect new status

### API Response Format
```json
{
  "message": "Schedule updated successfully",
  "schedule_id": "schedule_123",
  "status_progression": {
    "from": "pending_configuration",
    "to": "pending_confirmation"
  }
}
```

## Backend Integration

### Status Mapping
The `mapBackendScheduleStatus()` function handles conversion from backend string values to frontend enum values, including legacy status mapping.

### API Endpoints
- `PUT /schedules/{id}`: Update schedule (now includes monthlyEntries and automatic status progression)
- `POST /schedules/{id}/confirm`: Confirm schedule
- `POST /schedules/{id}/skip`: Skip schedule with reason

## Best Practices

1. **Always use helper functions** instead of hardcoded status checks
2. **Use StatusBadge component** for consistent visual representation
3. **Map backend statuses** using `mapBackendScheduleStatus()` before using in UI
4. **Check action permissions** using helper functions before showing buttons
5. **Provide clear tooltips** explaining what each action does
6. **Use consistent colors** across all components

## Troubleshooting

### Common Issues
1. **Action buttons not showing**: Check if status is mapped correctly and helper functions return true
2. **Inconsistent colors**: Ensure using StatusBadge component or getScheduleStatusConfig()
3. **Backend status mismatch**: Verify status mapping in mapBackendScheduleStatus()

### Debugging
```typescript
// Log status information
console.log('Status:', status);
console.log('Mapped status:', mapBackendScheduleStatus(status));
console.log('Is action needed:', isActionNeededStatus(mappedStatus));
console.log('Can edit:', isEditableStatus(mappedStatus));
console.log('Can confirm:', isConfirmableStatus(mappedStatus));
console.log('Can skip:', isSkippableStatus(mappedStatus));
```

## Future Enhancements

1. **Status transitions validation**: Prevent invalid status changes
2. **Audit trail**: Track status change history
3. **Bulk status operations**: Change status for multiple schedules
4. **Custom status workflows**: Allow configuration of status flow per client
5. **Status-based notifications**: Alert users when schedules need attention 