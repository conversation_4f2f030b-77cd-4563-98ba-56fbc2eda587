"""
Notification Processor Service

Handles the creation and delivery of mention notifications for comments.
Following DRCR service patterns with proper error handling and logging.
"""
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from google.cloud import firestore
from google.cloud.firestore import SERVER_TIMESTAMP

logger = logging.getLogger(__name__)


class NotificationProcessor:
    """Service for processing comment mention notifications"""
    
    def __init__(self, db: firestore.Client):
        self.db = db
    
    async def process_mention_notifications(
        self,
        comment_id: str,
        comment_data: Dict[str, Any],
        mentioned_uids: List[str]
    ) -> Dict[str, Any]:
        """
        Process mention notifications for a comment
        
        Args:
            comment_id: ID of the comment that contains mentions
            comment_data: Comment document data
            mentioned_uids: List of user UIDs that were mentioned
            
        Returns:
            Dict with processing results and metrics
        """
        try:
            notifications_created = 0
            emails_sent = 0
            
            # Get comment author information
            author_info = await self._get_user_info(comment_data.get("created_by"))
            
            # Get parent record information for context
            parent_context = await self._get_parent_record_context(
                comment_data.get("parent_type"),
                comment_data.get("parent_id")
            )
            
            # Process each mentioned user
            for uid in mentioned_uids:
                try:
                    # Get mentioned user information
                    user_info = await self._get_user_info(uid)
                    
                    if not user_info:
                        logger.warning(f"User not found for UID: {uid}")
                        continue
                    
                    # Check if user has access to the comment's client
                    if not await self._user_has_client_access(uid, comment_data.get("client_id")):
                        logger.warning(f"User {uid} doesn't have access to client {comment_data.get('client_id')}")
                        continue
                    
                    # Create notification document
                    notification_created = await self._create_notification(
                        user_uid=uid,
                        comment_id=comment_id,
                        comment_data=comment_data,
                        author_info=author_info,
                        parent_context=parent_context
                    )
                    
                    if notification_created:
                        notifications_created += 1
                        
                        # Send email notification if user has email notifications enabled
                        email_sent = await self._send_email_notification(
                            user_info=user_info,
                            comment_data=comment_data,
                            author_info=author_info,
                            parent_context=parent_context
                        )
                        
                        if email_sent:
                            emails_sent += 1
                    
                except Exception as e:
                    logger.error(f"Failed to process mention for user {uid}: {str(e)}")
                    continue
            
            logger.info(f"Processed {len(mentioned_uids)} mentions: {notifications_created} notifications, {emails_sent} emails")
            
            return {
                "notifications_created": notifications_created,
                "emails_sent": emails_sent,
                "mentioned_users_count": len(mentioned_uids)
            }
            
        except Exception as e:
            logger.error(f"Error processing mention notifications: {str(e)}", exc_info=True)
            raise
    
    async def _get_user_info(self, uid: str) -> Optional[Dict[str, Any]]:
        """Get user information from FIRM_USERS collection"""
        try:
            # Query FIRM_USERS by firebase_uid
            user_query = self.db.collection("FIRM_USERS").where(
                filter=firestore.FieldFilter("firebase_uid", "==", uid)
            )
            user_docs = user_query.stream()
            
            async for user_doc in user_docs:
                user_data = user_doc.to_dict()
                return {
                    "uid": uid,
                    "email": user_data.get("email"),
                    "display_name": user_data.get("display_name") or user_data.get("email"),
                    "role": user_data.get("role"),
                    "firm_id": user_data.get("firm_id"),
                    "assigned_client_ids": user_data.get("assigned_client_ids", []),
                    "notification_preferences": user_data.get("notification_preferences", {})
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting user info for {uid}: {str(e)}")
            return None
    
    async def _user_has_client_access(self, uid: str, client_id: str) -> bool:
        """Check if user has access to the specified client"""
        try:
            user_info = await self._get_user_info(uid)
            
            if not user_info:
                return False
            
            # Firm admins have access to all clients
            if user_info.get("role") == "firm_admin":
                return True
            
            # Staff users need to be assigned to the client
            assigned_clients = user_info.get("assigned_client_ids", [])
            return client_id in assigned_clients
            
        except Exception as e:
            logger.error(f"Error checking client access for user {uid}: {str(e)}")
            return False
    
    async def _get_parent_record_context(
        self,
        parent_type: str,
        parent_id: str
    ) -> Dict[str, Any]:
        """Get context information about the parent record"""
        try:
            context = {
                "type": parent_type,
                "id": parent_id,
                "name": f"{parent_type} {parent_id}",
                "url": self._generate_parent_record_url(parent_type, parent_id)
            }
            
            # Get specific information based on parent type
            if parent_type == "schedule":
                schedule_doc = await self.db.collection("AMORTIZATION_SCHEDULES").document(parent_id).get()
                if schedule_doc.exists:
                    schedule_data = schedule_doc.to_dict()
                    context["name"] = f"Amortization Schedule for {schedule_data.get('description', 'Unknown')}"
                    
            elif parent_type == "transaction":
                transaction_doc = await self.db.collection("TRANSACTIONS").document(parent_id).get()
                if transaction_doc.exists:
                    transaction_data = transaction_doc.to_dict()
                    context["name"] = f"Transaction: {transaction_data.get('description', 'Unknown')}"
                    
            elif parent_type == "entity":
                entity_doc = await self.db.collection("ENTITIES").document(parent_id).get()
                if entity_doc.exists:
                    entity_data = entity_doc.to_dict()
                    context["name"] = f"Entity: {entity_data.get('entity_name', 'Unknown')}"
            
            return context
            
        except Exception as e:
            logger.error(f"Error getting parent record context: {str(e)}")
            return {
                "type": parent_type,
                "id": parent_id,
                "name": f"{parent_type} {parent_id}",
                "url": "#"
            }
    
    def _generate_parent_record_url(self, parent_type: str, parent_id: str) -> str:
        """Generate URL to the parent record in the frontend"""
        
        # Base URL - this should be configurable
        base_url = "https://drcr-d660a.web.app"  # Production URL
        
        if parent_type == "schedule":
            return f"{base_url}/bills?schedule_id={parent_id}"
        elif parent_type == "transaction":
            return f"{base_url}/bills?transaction_id={parent_id}"
        elif parent_type == "entity":
            return f"{base_url}/entities/{parent_id}"
        else:
            return f"{base_url}/dashboard"
    
    async def _create_notification(
        self,
        user_uid: str,
        comment_id: str,
        comment_data: Dict[str, Any],
        author_info: Dict[str, Any],
        parent_context: Dict[str, Any]
    ) -> bool:
        """Create notification document in user's notifications subcollection"""
        try:
            # Create notification document
            notification_data = {
                "type": "mention",
                "comment_id": comment_id,
                "parent_type": comment_data.get("parent_type"),
                "parent_id": comment_data.get("parent_id"),
                "snippet": self._create_comment_snippet(comment_data.get("text", "")),
                "author_name": author_info.get("display_name", "Unknown User"),
                "author_uid": comment_data.get("created_by"),
                "parent_name": parent_context.get("name"),
                "parent_url": parent_context.get("url"),
                "read": False,
                "created_at": SERVER_TIMESTAMP,
                "client_id": comment_data.get("client_id"),
                "entity_id": comment_data.get("entity_id")
            }
            
            # Add to user's notifications subcollection
            notification_ref = self.db.collection("USERS").document(user_uid).collection("NOTIFICATIONS").document()
            await notification_ref.set(notification_data)
            
            logger.info(f"Created notification for user {user_uid} about comment {comment_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating notification for user {user_uid}: {str(e)}")
            return False
    
    def _create_comment_snippet(self, text: str, max_length: int = 120) -> str:
        """Create a snippet of the comment text for notifications"""
        if len(text) <= max_length:
            return text
        
        # Truncate and add ellipsis
        return text[:max_length - 3] + "..."
    
    async def _send_email_notification(
        self,
        user_info: Dict[str, Any],
        comment_data: Dict[str, Any],
        author_info: Dict[str, Any],
        parent_context: Dict[str, Any]
    ) -> bool:
        """Send email notification to mentioned user"""
        try:
            # Check if user has email notifications enabled
            notification_prefs = user_info.get("notification_preferences", {})
            if not notification_prefs.get("email_mentions", True):  # Default to True
                logger.info(f"Email notifications disabled for user {user_info.get('uid')}")
                return False
            
            # For now, just log the email that would be sent
            # In a full implementation, this would integrate with SendGrid or similar
            email_subject = f"You were mentioned in a comment by {author_info.get('display_name')}"
            email_body = self._create_email_body(
                user_info=user_info,
                comment_data=comment_data,
                author_info=author_info,
                parent_context=parent_context
            )
            
            logger.info(f"Would send email to {user_info.get('email')}:")
            logger.info(f"Subject: {email_subject}")
            logger.info(f"Body: {email_body[:200]}...")
            
            # TODO: Integrate with actual email service
            # await email_service.send_email(
            #     to=user_info.get("email"),
            #     subject=email_subject,
            #     body=email_body
            # )
            
            return True
            
        except Exception as e:
            logger.error(f"Error sending email notification: {str(e)}")
            return False
    
    def _create_email_body(
        self,
        user_info: Dict[str, Any],
        comment_data: Dict[str, Any],
        author_info: Dict[str, Any],
        parent_context: Dict[str, Any]
    ) -> str:
        """Create email body for mention notification"""
        
        snippet = self._create_comment_snippet(comment_data.get("text", ""))
        
        return f"""
Hi {user_info.get('display_name')},

{author_info.get('display_name')} mentioned you in a comment on {parent_context.get('name')}:

"{snippet}"

You can view and reply to this comment by clicking the link below:
{parent_context.get('url')}

---
DRCR Automated Prepayment Platform
        """.strip()