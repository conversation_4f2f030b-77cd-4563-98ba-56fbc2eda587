import React, { useState } from 'react';
import type { ReactNode } from 'react';
import { Check, ChevronsUpDown, Loader2 } from 'lucide-react';
import { But<PERSON> } from './button';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from './command';
import { Popover, PopoverContent, PopoverTrigger } from './popover';
import { cn } from '@/lib/utils';

export interface AutocompleteItem<T = string> {
  value: T;
  label: string;
  icon?: ReactNode;
  disabled?: boolean;
  metadata?: string; // Additional text like status, count, etc.
}

interface AutocompletePopoverProps<T = string> {
  items: AutocompleteItem<T>[];
  selectedValue: T | null;
  onSelect: (value: T) => void;
  itemKeyFn?: (item: AutocompleteItem<T>) => string; // Override for non-string IDs
  placeholder?: string;
  searchPlaceholder?: string;
  emptyMessage?: string;
  groupHeading?: string;
  isLoading?: boolean;
  disabled?: boolean;
  className?: string;
  'aria-label'?: string;
  screenReaderLabel?: string; // Label for screen readers on the input
  buttonClassName?: string;
  popoverClassName?: string;
  allowClear?: boolean;
  clearValue?: T; // Value to use when clearing (defaults to empty string for string types)
  showCheckmarks?: boolean;
  virtualized?: boolean; // Enable for large lists (>500 items)
  maxHeight?: string; // Custom max height for virtualized lists
}

/**
 * Reusable autocomplete popover component that wraps Command + Popover
 * Provides consistent keyboard handling, ARIA support, and UX patterns
 */
export function AutocompletePopover<T = string>({
  items,
  selectedValue,
  onSelect,
  itemKeyFn,
  placeholder = 'Select option...',
  searchPlaceholder = 'Search...',
  emptyMessage = 'No results found.',
  groupHeading,
  isLoading = false,
  disabled = false,
  className,
  'aria-label': ariaLabel,
  screenReaderLabel,
  buttonClassName,
  popoverClassName = 'w-[300px]',
  allowClear = false,
  clearValue = '' as T,
  showCheckmarks = true,
  virtualized = false,
  maxHeight = '300px',
}: AutocompletePopoverProps<T>) {
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState('');

  // Default key function
  const getItemKey = itemKeyFn || ((item: AutocompleteItem<T>) => 
    typeof item.value === 'string' ? item.value : String(item.value)
  );

  // Find selected item for display
  const selectedItem = selectedValue 
    ? items.find(item => item.value === selectedValue)
    : null;

  // Handle selection
  const handleSelect = (value: T) => {
    onSelect(value);
    setOpen(false);
    setSearchValue(''); // Clear search on selection
  };

  // Handle clear selection
  const handleClear = () => {
    if (allowClear && selectedValue) {
      onSelect(clearValue);
      setOpen(false);
    }
  };

  // Determine display text
  const displayText = selectedItem ? selectedItem.label : placeholder;

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          aria-label={ariaLabel}
          className={cn("justify-between bg-white text-xs h-8", buttonClassName, className)}
          disabled={disabled || isLoading}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">
            {isLoading && <Loader2 className="h-3 w-3 animate-spin flex-shrink-0" />}
            {selectedItem?.icon && <span className="flex-shrink-0">{selectedItem.icon}</span>}
            <span className="truncate">{displayText}</span>
          </div>
          <ChevronsUpDown className="h-3 w-3 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className={cn("p-0", popoverClassName)} align="start">
        <Command>
          <CommandInput 
            placeholder={searchPlaceholder}
            value={searchValue}
            onValueChange={setSearchValue}
            role="combobox"
            aria-autocomplete="list"
            aria-expanded={open}
            aria-haspopup="listbox"
            aria-label={screenReaderLabel}
          />
          <CommandList className={virtualized ? `max-h-[${maxHeight}] overflow-y-auto` : undefined}>
            <CommandEmpty className="py-6 text-center text-sm">
              <div tabIndex={0} className="focus:outline-none focus:bg-accent rounded px-2 py-1">
                {emptyMessage}
              </div>
            </CommandEmpty>
            
            {groupHeading && (
              <CommandGroup heading={groupHeading}>
                {/* TODO: Replace with react-window FixedSizeList when items.length > 500 */}
                {items.map((item) => (
                  <CommandItem
                    key={getItemKey(item)}
                    value={`${item.label} ${item.metadata || ''}`} // Include metadata in search
                    onSelect={() => handleSelect(item.value)}
                    disabled={item.disabled}
                    className="flex items-center gap-2"
                  >
                    {showCheckmarks && (
                      <Check
                        className={cn(
                          "h-4 w-4",
                          selectedValue === item.value ? "opacity-100" : "opacity-0"
                        )}
                      />
                    )}
                    {item.icon && <span className="flex-shrink-0">{item.icon}</span>}
                    <div className="flex items-center justify-between w-full">
                      <span className="flex-1">{item.label}</span>
                      {item.metadata && (
                        <span className="text-xs text-muted-foreground ml-2">
                          {item.metadata}
                        </span>
                      )}
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            )}
            
            {!groupHeading && 
              /* TODO: Replace with react-window FixedSizeList when items.length > 500 */
              items.map((item) => (
              <CommandItem
                key={getItemKey(item)}
                value={`${item.label} ${item.metadata || ''}`} // Include metadata in search
                onSelect={() => handleSelect(item.value)}
                disabled={item.disabled}
                className="flex items-center gap-2"
              >
                {showCheckmarks && (
                  <Check
                    className={cn(
                      "h-4 w-4",
                      selectedValue === item.value ? "opacity-100" : "opacity-0"
                    )}
                  />
                )}
                {item.icon && <span className="flex-shrink-0">{item.icon}</span>}
                <div className="flex items-center justify-between w-full">
                  <span className="flex-1">{item.label}</span>
                  {item.metadata && (
                    <span className="text-xs text-muted-foreground ml-2">
                      {item.metadata}
                    </span>
                  )}
                </div>
              </CommandItem>
            ))}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}