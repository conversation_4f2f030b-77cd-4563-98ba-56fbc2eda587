"""
Token Usage Models

Pydantic models for token usage analytics API responses.
"""
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime


class TokenOperationDetail(BaseModel):
    """Individual token operation detail"""
    provider: str = Field(..., description="LLM provider (openai, mistral)")
    model: str = Field(..., description="Model name")
    operation: str = Field(..., description="Operation type (ocr, analysis, etc.)")
    tokens: int = Field(..., description="Total tokens consumed")
    cost: float = Field(..., description="Cost in USD")
    timestamp: str = Field(..., description="Operation timestamp")


class SyncJobTokenUsage(BaseModel):
    """Token usage for a specific sync job"""
    sync_job_id: str = Field(..., description="Sync job ID")
    client_id: str = Field(..., description="Client ID")
    entity_id: Optional[str] = Field(None, description="Entity ID")
    status: Optional[str] = Field(None, description="Sync job status")
    openai_tokens: int = Field(0, description="Total OpenAI tokens consumed")
    mistral_tokens: int = Field(0, description="Total Mistral tokens consumed")
    total_cost: float = Field(0.0, description="Total cost in USD")
    operations_count: int = Field(0, description="Number of token operations")
    operations: List[TokenOperationDetail] = Field(default_factory=list, description="Detailed operation list")
    started_at: Optional[datetime] = Field(None, description="Job start time")
    completed_at: Optional[datetime] = Field(None, description="Job completion time")


class SyncJobSummary(BaseModel):
    """Summary of sync job for client token usage"""
    sync_job_id: str = Field(..., description="Sync job ID")
    entity_id: Optional[str] = Field(None, description="Entity ID")
    status: Optional[str] = Field(None, description="Sync job status")
    openai_tokens: int = Field(0, description="OpenAI tokens consumed")
    mistral_tokens: int = Field(0, description="Mistral tokens consumed")
    total_cost: float = Field(0.0, description="Total cost in USD")
    operations_count: int = Field(0, description="Number of operations")
    operations: Optional[List[TokenOperationDetail]] = Field(None, description="Detailed operations (only when detailed=true)")
    started_at: Optional[datetime] = Field(None, description="Job start time")
    completed_at: Optional[datetime] = Field(None, description="Job completion time")


class ClientTokenUsage(BaseModel):
    """Aggregated token usage for a client"""
    client_id: str = Field(..., description="Client ID")
    total_openai_tokens: int = Field(0, description="Total OpenAI tokens consumed")
    total_mistral_tokens: int = Field(0, description="Total Mistral tokens consumed")
    total_cost: float = Field(0.0, description="Total cost in USD")
    total_operations: int = Field(0, description="Total number of operations")
    sync_jobs_count: int = Field(0, description="Number of sync jobs included")
    start_date: Optional[datetime] = Field(None, description="Filter start date")
    end_date: Optional[datetime] = Field(None, description="Filter end date")
    sync_jobs: List[SyncJobSummary] = Field(default_factory=list, description="Recent sync jobs")


class TokenUsageSummary(BaseModel):
    """High-level token usage summary"""
    total_openai_tokens: int = Field(0, description="Total OpenAI tokens across all clients")
    total_mistral_tokens: int = Field(0, description="Total Mistral tokens across all clients")
    total_cost: float = Field(0.0, description="Total cost in USD")
    total_operations: int = Field(0, description="Total number of operations")
    sync_jobs_count: int = Field(0, description="Number of sync jobs included")
    clients_count: int = Field(0, description="Number of clients included")
    start_date: Optional[datetime] = Field(None, description="Filter start date")
    end_date: Optional[datetime] = Field(None, description="Filter end date")