#!/usr/bin/env python3
"""
Nylas Email Polling Cloud Function

Polls Nylas API for new emails and processes them into Firestore.
Triggered by Cloud Scheduler every 5 minutes.

Following DRCR patterns from xero_sync_consumer
"""

import os
import sys

# Add the current and parent directories to sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)

if current_dir not in sys.path:
    sys.path.insert(0, current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

try:
    from dotenv import load_dotenv
    load_dotenv()  # Load .env file as early as possible (local dev only)
except ImportError:
    pass  # dotenv not available in Cloud Functions

import logging
import json
from datetime import datetime
import functions_framework
from google.cloud import firestore

# Import our services
from services.email_processor import EmailProcessor
from utils.error_handler import ErrorHandler

# Initialize logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# GCP Project ID
GCP_PROJECT_ID = os.getenv("GCP_PROJECT_ID")

@functions_framework.http
def nylas_email_poller(request):
    """
    Cloud Function entry point for polling Nylas emails
    
    Triggered by Cloud Scheduler every 5 minutes to:
    1. Poll Nylas API for new emails
    2. Filter for entity emails (<EMAIL>)
    3. Store metadata in Firestore subcollections
    4. Track processing statistics
    
    Returns:
        JSON response with processing results
    """
    
    start_time = datetime.utcnow()
    
    # Initialize error handler outside try block so it's always available
    error_handler = ErrorHandler()
    
    try:
        logger.info("Starting Nylas email polling cycle")
        
        # Initialize services
        email_processor = EmailProcessor()
        
        # 1. Check if this is a test request
        request_data = request.get_json(silent=True) or {}
        is_test = request_data.get('test', False)
        
        if is_test:
            logger.info("Test polling request received")
        
        # 2. Process emails using thread-based approach
        logger.info("Processing emails using thread-based approach")
        result = email_processor.process_recent_emails()
        
        if result["processed_threads"] == 0 and result["processed_messages"] == 0:
            logger.info("No new threads or messages to process")
            return {
                "status": "success",
                "processed_threads": 0,
                "processed_messages": 0,
                "total_threads": 0,
                "duration_seconds": (datetime.utcnow() - start_time).total_seconds(),
                "timestamp": datetime.utcnow().isoformat()
            }
        
        # 4. Update statistics
        duration = (datetime.utcnow() - start_time).total_seconds()
        
        response_data = {
            "status": "success",
            "processed_threads": result["processed_threads"],
            "processed_messages": result["processed_messages"],
            "skipped_threads": result["skipped_threads"],
            "error_count": result["error_count"],
            "entities_affected": result["entities_affected"],
            "duration_seconds": duration,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        logger.info(f"Polling cycle completed: {response_data}")
        
        return response_data
        
    except Exception as e:
        duration = (datetime.utcnow() - start_time).total_seconds()
        error_message = str(e)
        
        logger.error(f"Polling cycle failed: {error_message}")
        
        # Log to error handler for monitoring
        try:
            error_handler.log_error("polling_failure", error_message, {
                "duration_seconds": duration,
                "timestamp": datetime.utcnow().isoformat()
            })
        except Exception:
            pass  # Don't fail if error logging fails
        
        return {
            "status": "error",
            "error": error_message,
            "duration_seconds": duration,
            "timestamp": datetime.utcnow().isoformat()
        }, 500

# For local testing
if __name__ == "__main__":
    # Simulate a Cloud Function request
    class MockRequest:
        def get_json(self, silent=True):
            return {"test": True}
    
    result = nylas_email_poller(MockRequest())
    print(json.dumps(result, indent=2))