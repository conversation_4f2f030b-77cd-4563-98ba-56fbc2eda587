# Firestore Schema for Email Reply System

## Overview
Extension of the existing email threading schema to support reply functionality with proper tracking and audit trails.

## Collections Structure

### EMAIL_THREADS (existing)
```
ENTITIES/{entity_id}/EMAIL_THREADS/{thread_id}
{
  thread_id: "thread_123",
  entity_id: "entity_456", 
  subject: "Re: Invoice Processing",
  participant_emails: ["<EMAIL>", "<EMAIL>"],
  message_count: 3,
  has_attachments: true,
  first_message_at: "2025-07-20T10:30:00Z",
  latest_message_at: "2025-07-20T14:45:00Z",
  thread_status: "active",
  created_at: "2025-07-20T10:30:00Z",
  updated_at: "2025-07-20T14:45:00Z",
  created_by: "polling_system"
}
```

### EMAILS (existing)
```
ENTITIES/{entity_id}/EMAILS/{email_id}
{
  email_id: "email_789",
  thread_id: "thread_123",
  entity_id: "entity_456",
  subject: "Re: Invoice Processing", 
  from_address: ["<EMAIL>"],
  to_addresses: ["<EMAIL>"],
  cc_addresses: [],
  sender_name: "John Doe",
  received_at: "2025-07-20T14:45:00Z",
  has_attachments: true,
  attachment_count: 1,
  attachments: [
    {
      id: "att_1", 
      filename: "invoice.pdf", 
      content_type: "application/pdf", 
      size: 45678
    }
  ],
  processing_status: "pending",
  metadata_sync_status: "completed",
  created_at: "2025-07-20T14:45:00Z",
  updated_at: "2025-07-20T14:45:00Z",
  created_by: "polling_system"
}
```

### REPLIES (new subcollection)
```
ENTITIES/{entity_id}/EMAIL_THREADS/{thread_id}/REPLIES/{reply_id}
{
  reply_id: "reply_456",           // Auto-generated document ID
  thread_id: "thread_123",         // Parent thread reference
  entity_id: "entity_456",         // Entity this reply belongs to
  
  // Reply content
  to_email: "<EMAIL>",  // Recipient email address
  from_email: "<EMAIL>", // Sender (entity email)
  subject: "Re: Invoice Processing - Received", // Email subject
  body: "Thank you for sending your invoice...", // Email body content
  
  // Threading information
  reply_to_message_id: "email_789", // Original message being replied to
  external_message_id: "nylas_msg_123", // Nylas message ID (when sent)
  
  // Processing status
  status: "sent",                  // pending|sent|failed
  
  // Template information
  template_used: "acknowledgment.document_received", // Template applied
  template_variables: {           // Variables used in template
    "original_subject": "Invoice Processing",
    "document_count": 1
  },
  
  // Timestamps
  created_at: "2025-07-20T15:00:00Z",  // When reply was initiated
  sent_at: "2025-07-20T15:00:15Z",     // When reply was successfully sent
  failed_at: null,                     // When reply failed (if applicable)
  
  // Audit information
  created_by: "user_123",          // User who initiated reply
  created_by_system: false,        // false=user initiated, true=auto-reply
  
  // Error handling
  error_message: null,             // Error details if status=failed
  retry_count: 0,                  // Number of retry attempts
  
  // Nylas integration
  nylas_request_id: "req_uuid_123", // Idempotency key used
  
  // Additional metadata
  reply_type: "manual",            // manual|template|auto
  processing_duration_ms: 150      // Time taken to process reply
}
```

## Query Patterns

### Get all replies for a thread
```javascript
ENTITIES/{entity_id}/EMAIL_THREADS/{thread_id}/REPLIES
  .orderBy('created_at', 'desc')
  .limit(10)
```

### Get recent replies for an entity
```javascript
ENTITIES/{entity_id}/EMAIL_THREADS/{thread_id}/REPLIES
  .where('status', '==', 'sent')
  .orderBy('sent_at', 'desc')
  .limit(50)
```

### Get failed replies for retry
```javascript
ENTITIES/{entity_id}/EMAIL_THREADS/{thread_id}/REPLIES
  .where('status', '==', 'failed')
  .where('retry_count', '<', 3)
  .orderBy('failed_at', 'asc')
```

### Get replies by user (audit trail)
```javascript
ENTITIES/{entity_id}/EMAIL_THREADS/{thread_id}/REPLIES
  .where('created_by', '==', 'user_123')
  .orderBy('created_at', 'desc')
```

## Indexes Required

```javascript
// Composite indexes for efficient queries
[
  {
    collectionGroup: "REPLIES",
    fields: [
      { fieldPath: "entity_id", order: "ASCENDING" },
      { fieldPath: "status", order: "ASCENDING" },
      { fieldPath: "created_at", order: "DESCENDING" }
    ]
  },
  {
    collectionGroup: "REPLIES", 
    fields: [
      { fieldPath: "status", order: "ASCENDING" },
      { fieldPath: "retry_count", order: "ASCENDING" },
      { fieldPath: "failed_at", order: "ASCENDING" }
    ]
  },
  {
    collectionGroup: "REPLIES",
    fields: [
      { fieldPath: "created_by", order: "ASCENDING" },
      { fieldPath: "created_at", order: "DESCENDING" }
    ]
  }
]
```

## Security Rules

See `firestore_security_rules.txt` for detailed security configuration.

## Migration Notes

- REPLIES is a new subcollection, no migration of existing data required
- EMAIL_THREADS and EMAILS collections remain unchanged
- Indexes should be created before deploying reply functionality
- Monitor document size limits (1 MiB per document)

## Performance Considerations

- Reply documents are small (~1-5 KB each)
- Subcollection structure provides automatic entity isolation
- Indexes support common query patterns efficiently
- Rate limiting at Cloud Tasks level prevents Gmail quota issues