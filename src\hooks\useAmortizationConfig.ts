import { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import type { HierarchicalBillsData } from '../types/hierarchical-bills.types';
import type { SelectedSummary } from './useSelection';
import type { MonthlyScheduleEntry } from '../types/schedule.types';
import { isManuallyEdited } from '../types/schedule.types';
import { PrepaymentsService } from '../services/prepayments.service';
import { withRunningBalance, isRevertingToOriginal } from '../utils/schedule-calculations';
import { debounce } from '../utils/debounce';
import { toast } from 'sonner';

export interface AmortizationConfig {
  method: string;
  startDate: string;
  endDate: string;
  prepaymentAccount: string;
  expenseAccount: string;
  numberOfPeriods: number;
  custom_narration?: string;
}

export interface ValidationMessages {
  canSave: string[];
  canPost: string[];
  canReset: string[];
  canSkip: string[];
}

export interface UseAmortizationConfigReturn {
  amortizationConfig: AmortizationConfig;
  validationMessages: ValidationMessages;
  handleConfigurationChange: (updates: Partial<AmortizationConfig>) => void;
  isPreviewLoading: boolean;
}

export function useAmortizationConfig(
  hierarchicalData: HierarchicalBillsData,
  selectedSummary: SelectedSummary,
  summaryData: any,
  monthlySchedule: MonthlyScheduleEntry[],
  setMonthlySchedule: (schedule: MonthlyScheduleEntry[]) => void,
  selectedEntityId?: string
): UseAmortizationConfigReturn {

  // Amortization configuration - editable state with defaults from schedules
  const [amortizationConfig, setAmortizationConfig] = useState<AmortizationConfig>({
    method: 'equal_monthly',
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
    numberOfPeriods: 12,
    prepaymentAccount: '',
    expenseAccount: '',
  });

  // Preview loading state
  const [isPreviewLoading, setIsPreviewLoading] = useState(false);

  // Request cancellation and caching
  const abortControllerRef = useRef<AbortController | null>(null);
  const previewCacheRef = useRef<Map<string, MonthlyScheduleEntry[]>>(new Map());
  const originalConfigRef = useRef<AmortizationConfig | null>(null);
  
  // Single-flight preview pattern
  const inFlightPromiseRef = useRef<Promise<void> | null>(null);
  const lastWantedParamsRef = useRef<AmortizationConfig | null>(null);

  // Update config defaults when selected items change
  useEffect(() => {
    // Get account codes and calculation method from selected line items to set as defaults
    const prepaymentAccounts = new Set<string>();
    const expenseAccounts = new Set<string>();
    const methods = new Set<string>();
    
    hierarchicalData.suppliers.forEach(supplier => {
      supplier.invoices.forEach(invoice => {
        invoice.lineItems.forEach(lineItem => {
          if (lineItem.isSelected) {
            if (lineItem.prepaymentAccountCode) {
              prepaymentAccounts.add(lineItem.prepaymentAccountCode);
            }
            if (lineItem.expenseAccountCode) {
              expenseAccounts.add(lineItem.expenseAccountCode);
            }
            if ((lineItem as any).calculationMethod) {
              methods.add((lineItem as any).calculationMethod);
            }
          }
        });
      });
    });

    setAmortizationConfig(prev => {
      const uniqueMethods = Array.from(methods);
      console.log('useAmortizationConfig: unique calculation methods from selection:', uniqueMethods);
      const inferredMethod = uniqueMethods.length === 1 ? uniqueMethods[0] : prev.method || 'equal_monthly';
      console.log('useAmortizationConfig: inferred method =', inferredMethod);

      return {
        ...prev,
        method: inferredMethod,
        startDate: summaryData.startDate,
        endDate: summaryData.endDate,
        numberOfPeriods: summaryData.numberOfPeriods,
        prepaymentAccount: prev.prepaymentAccount || Array.from(prepaymentAccounts)[0] || '',
        expenseAccount: prev.expenseAccount || Array.from(expenseAccounts)[0] || '',
      };
    });
  }, [summaryData, hierarchicalData.suppliers]);

  // Store original config on first setup
  useEffect(() => {
    if (!originalConfigRef.current && summaryData.startDate) {
      originalConfigRef.current = {
        method: 'equal_monthly',
        startDate: summaryData.startDate,
        endDate: summaryData.endDate,
        numberOfPeriods: summaryData.numberOfPeriods,
        prepaymentAccount: '',
        expenseAccount: '',
      };
    }
  }, [summaryData]);

  // Transform preview response to MonthlyScheduleEntry format
  const transformPreviewResponse = useCallback((response: any): MonthlyScheduleEntry[] => {
    const entries = response.monthly_entries.map((entry: any, index: number) => ({
      period: index + 1,
      date: entry.month_date,
      amount: entry.amount,
      originalAmount: null, // Will be set after transformation
      status: 'proposed' as const,
      runningBalance: 0 // Will be calculated
    }));

    // Calculate running balances
    return withRunningBalance(entries, selectedSummary.totalAmount);
  }, [selectedSummary.totalAmount]);

  // Preserve manual edits when timeline changes
  const preserveManualEdits = useCallback((
    newSchedule: MonthlyScheduleEntry[], 
    oldSchedule: MonthlyScheduleEntry[]
  ): MonthlyScheduleEntry[] => {
    // Create a map of manually edited amounts by date (more stable than period index)
    const editedAmountsByDate = new Map<string, { amount: number; originalAmount: number }>();
    
    oldSchedule.forEach(entry => {
      if (isManuallyEdited(entry)) {
        // Guard against duplicate dates - warn and use first occurrence
        if (editedAmountsByDate.has(entry.date)) {
          console.warn('Duplicate date detected in manual edits:', entry.date, 'Keeping first edit value');
          return;
        }
        
        editedAmountsByDate.set(entry.date, {
          amount: entry.amount,
          originalAmount: entry.originalAmount!
        });
      }
    });

    // Apply preserved edits to new schedule where dates match
    let appliedEditsCount = 0;
    const result = newSchedule.map(entry => {
      const editedData = editedAmountsByDate.get(entry.date);
      if (editedData) {
        appliedEditsCount++;
        return {
          ...entry,
          amount: editedData.amount,
          originalAmount: editedData.originalAmount
        };
      }
      return entry;
    });

    // Log if some manual edits were lost due to timeline changes
    const totalEdits = editedAmountsByDate.size;
    if (appliedEditsCount < totalEdits) {
      console.info(`Manual edit preservation: Applied ${appliedEditsCount}/${totalEdits} edits. ${totalEdits - appliedEditsCount} edits lost due to timeline changes.`);
    }

    return result;
  }, []);

  // Single-flight preview execution - ensures at most 2 requests per burst
  const executePreview = useCallback(async (config: AmortizationConfig): Promise<void> => {
    if (!selectedSummary.totalAmount || selectedSummary.count === 0) return;

    // Generate cache key
    const cacheKey = `${config.method}-${config.startDate}-${config.numberOfPeriods}-${selectedSummary.totalAmount}`;
    
    // Check cache first
    if (previewCacheRef.current.has(cacheKey)) {
      const cachedResult = previewCacheRef.current.get(cacheKey)!;
      setMonthlySchedule(cachedResult);
      return;
    }

    // Check if reverting to original
    if (originalConfigRef.current && isRevertingToOriginal(config, originalConfigRef.current)) {
      // Restore original amounts from current schedule
      const restoredSchedule = monthlySchedule.map(entry => ({
        ...entry,
        amount: entry.originalAmount ?? entry.amount
      }));
      setMonthlySchedule(restoredSchedule);
      previewCacheRef.current.set(cacheKey, restoredSchedule);
      return;
    }

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    try {
      setIsPreviewLoading(true);
      
      // Ensure we have a valid entity ID
      if (!selectedEntityId || selectedEntityId === 'all') {
        console.warn('No valid entity ID for preview calculation');
        toast.error('Please select a specific entity to calculate preview');
        return;
      }
      
      const response = await PrepaymentsService.calculatePreview({
        amount: selectedSummary.totalAmount,
        start_date: config.startDate,
        end_date: config.endDate,
        calculation_method: config.method as 'day_based' | 'equal_monthly',
        entity_id: selectedEntityId
      }, { signal: abortControllerRef.current.signal });

      // Validate response structure
      if (!response || !response.monthly_entries || !Array.isArray(response.monthly_entries)) {
        console.error('Invalid preview response format:', response);
        toast.error('Invalid response from preview calculation');
        return;
      }

      // Transform and set new schedule
      const newSchedule = transformPreviewResponse(response);
      
      // Preserve manual edits from previous schedule where possible
      const scheduleWithPreservedEdits = preserveManualEdits(newSchedule, monthlySchedule);
      
      // Cache the result
      previewCacheRef.current.set(cacheKey, scheduleWithPreservedEdits);
      setMonthlySchedule(scheduleWithPreservedEdits);
      
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        console.error('Preview failed:', error);
        const errorMessage = error.response?.data?.detail || error.message || 'Failed to calculate preview';
        toast.error(`Preview calculation failed: ${errorMessage}`);
      }
    } finally {
      setIsPreviewLoading(false);
      abortControllerRef.current = null;
    }
  }, [selectedSummary.totalAmount, selectedSummary.count, monthlySchedule, setMonthlySchedule, transformPreviewResponse, selectedEntityId]);

  // Single-flight preview trigger - guarantees at most 2 requests per burst
  const triggerPreview = useCallback(async (config: AmortizationConfig) => {
    // Store the latest wanted parameters
    lastWantedParamsRef.current = config;

    // If there's already a request in flight, just update the wanted params and return
    if (inFlightPromiseRef.current) {
      return;
    }

    // Start new request
    const executeAndCheck = async (): Promise<void> => {
      await executePreview(config);
      
      // After completion, check if we need to fire again with updated params
      const latestWanted = lastWantedParamsRef.current;
      if (latestWanted && JSON.stringify(latestWanted) !== JSON.stringify(config)) {
        // New params arrived while we were executing - fire once more
        await executePreview(latestWanted);
      }
    };

    inFlightPromiseRef.current = executeAndCheck().finally(() => {
      inFlightPromiseRef.current = null;
    });

    return inFlightPromiseRef.current;
  }, [executePreview]);

  // Debounced version of triggerPreview
  const debouncedTriggerPreview = useMemo(
    () => debounce(triggerPreview, 300),
    [triggerPreview]
  );

  // Cleanup: abort any pending requests on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }
    };
  }, []);

  // Handle configuration changes with auto-calculation
  const handleConfigurationChange = (updates: Partial<AmortizationConfig>) => {
    setAmortizationConfig(prev => {
      const updated = { ...prev, ...updates };
      
      // Auto-calculate end date when start date or periods change
      if (updates.startDate || updates.numberOfPeriods) {
        if (updated.startDate && updated.numberOfPeriods > 0) {
          const startDate = new Date(updated.startDate);
          const endDate = new Date(startDate);
          endDate.setMonth(endDate.getMonth() + updated.numberOfPeriods);
          updated.endDate = endDate.toISOString().split('T')[0];
        }
      }
      
      // Trigger preview when method, startDate, or numberOfPeriods change
      if (updates.method || updates.startDate || updates.numberOfPeriods) {
        debouncedTriggerPreview(updated);
      }
      
      return updated;
    });
  };

  // Validation helper functions
  const hasUnpostedEntries = (schedule: any[]): boolean => {
    return schedule.some(entry => entry.status !== 'posted');
  };

  const hasMandatoryFieldsPopulated = (config: AmortizationConfig): boolean => {
    return !!(config.prepaymentAccount && config.expenseAccount);
  };

  const amortizationCoversTotal = (schedule: any[], totalAmount: number): boolean => {
    if (schedule.length === 0 || totalAmount === 0) return false;
    
    const scheduleTotal = schedule.reduce((sum, entry) => sum + entry.amount, 0);
    // Allow small rounding differences (within 0.01)
    return Math.abs(scheduleTotal - totalAmount) <= 0.01;
  };

  // Centralized validation message generation
  const validationMessages = useMemo<ValidationMessages>(() => {
    const messages: ValidationMessages = {
      canSave: [],
      canPost: [],
      canReset: [],
      canSkip: [],
    };

    // Check if any selected schedules are posted
    const hasPostedSchedules = summaryData.selectedScheduleIds?.some((scheduleId: string) => {
      // Look for posted entries in the monthly schedule
      return monthlySchedule.some(entry => entry.status === 'posted');
    });

    // Save validation messages
    if (selectedSummary.count === 0) {
      messages.canSave.push('No bills selected');
    }
    if (hasPostedSchedules) {
      messages.canSave.push('Cannot modify posted schedules');
    }
    if (!amortizationConfig.prepaymentAccount) {
      messages.canSave.push('Prepayment account is required');
    }
    if (!amortizationConfig.expenseAccount) {
      messages.canSave.push('Expense account is required');
    }

    // Post validation messages
    if (selectedSummary.count === 0) {
      messages.canPost.push('No bills selected');
    }
    if (!hasUnpostedEntries(monthlySchedule)) {
      messages.canPost.push('All entries are already posted');
    }
    if (!hasMandatoryFieldsPopulated(amortizationConfig)) {
      if (!amortizationConfig.prepaymentAccount) {
        messages.canPost.push('Prepayment account is required');
      }
      if (!amortizationConfig.expenseAccount) {
        messages.canPost.push('Expense account is required');
      }
    }
    if (!amortizationCoversTotal(monthlySchedule, selectedSummary.totalAmount)) {
      const scheduleTotal = monthlySchedule.reduce((sum, entry) => sum + entry.amount, 0);
      messages.canPost.push(`Amortization total (${scheduleTotal.toFixed(2)}) doesn't match selected amount (${selectedSummary.totalAmount.toFixed(2)})`);
    }

    // Reset validation messages
    if (selectedSummary.count === 0) {
      messages.canReset.push('No bills selected');
    }

    return messages;
  }, [
    selectedSummary.count,
    selectedSummary.totalAmount,
    amortizationConfig.prepaymentAccount,
    amortizationConfig.expenseAccount,
    monthlySchedule,
    summaryData.selectedScheduleIds
  ]);

  return {
    amortizationConfig,
    validationMessages,
    handleConfigurationChange,
    isPreviewLoading,
  };
}