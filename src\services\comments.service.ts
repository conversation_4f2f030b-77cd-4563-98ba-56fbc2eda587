import { api } from '@/lib/api';

// Comment types following DRCR frontend patterns
export interface CommentCreate {
  parent_type: ParentType;
  parent_id: string;
  text: string;
  mention_uids?: string[];
}

export interface CommentUpdate {
  text: string;
  mention_uids?: string[];
}

export interface CommentOut {
  comment_id: string;
  parent_type: string;
  parent_id: string;
  client_id: string;
  entity_id?: string;
  text: string;
  mentions: string[];
  mention_mapping?: { [uid: string]: string };
  created_by: string;
  created_at: string;
  updated_at?: string;
  deleted: boolean;
  reply_to_comment_id?: string;
  author_display_name?: string;
  author_email?: string;
}

export interface CommentsListResponse {
  comments: CommentOut[];
  total_count: number;
  page: number;
  per_page: number;
  pages: number;
}

export interface CommentFilters {
  show_deleted?: boolean;
  created_by?: string;
  mentions_user?: string;
  search_text?: string;
  date_from?: string;
  date_to?: string;
}

export interface CommentStats {
  total_comments: number;
  comments_today: number;
  comments_this_week: number;
  top_commenters: any[];
  most_commented_records: any[];
}

export enum ParentType {
  SCHEDULE = 'schedule',
  TRANSACTION = 'transaction',
  INVOICE = 'invoice',
  MANUAL_JOURNAL = 'manual_journal',
  ENTITY = 'entity',
  CLIENT = 'client'
}

// Re-export types for backward compatibility
export type {
  CommentCreate as CommentCreateType,
  CommentUpdate as CommentUpdateType,
  CommentOut as CommentOutType,
  CommentsListResponse as CommentsListResponseType,
  CommentFilters as CommentFiltersType,
  CommentStats as CommentStatsType
};

export class CommentsService {
  /**
   * Create a new comment
   */
  static async createComment(commentData: CommentCreate): Promise<CommentOut> {
    try {
      console.log('Creating comment:', commentData);
      
      const response = await api.post<CommentOut>('/comments/', commentData);
      
      console.log('Comment created successfully:', response);
      return response;
      
    } catch (error: any) {
      console.error('Error creating comment:', error);
      
      // Handle validation errors
      if (error.response?.status === 422) {
        throw new Error(`Validation error: ${error.response.data?.detail || 'Invalid comment data'}`);
      }
      
      // Handle access denied
      if (error.response?.status === 403) {
        throw new Error('Access denied: You do not have permission to comment on this record');
      }
      
      // Handle parent record not found
      if (error.response?.status === 404) {
        throw new Error('Record not found: The item you are trying to comment on does not exist');
      }
      
      // Generic error
      throw new Error(error.response?.data?.detail || 'Failed to create comment');
    }
  }
  
  /**
   * Get comments for a specific record with pagination and filtering
   */
  static async getCommentsForRecord(
    parentType: ParentType,
    parentId: string,
    options: {
      page?: number;
      limit?: number;
      filters?: CommentFilters;
    } = {}
  ): Promise<CommentsListResponse> {
    try {
      const { page = 1, limit = 25, filters = {} } = options;
      
      console.log(`Getting comments for ${parentType}:${parentId}`, { page, limit, filters });
      
      // Build query parameters
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString()
      });
      
      // Add filters to query params
      if (filters.show_deleted) {
        params.append('show_deleted', 'true');
      }
      if (filters.created_by) {
        params.append('created_by', filters.created_by);
      }
      if (filters.mentions_user) {
        params.append('mentions_user', filters.mentions_user);
      }
      if (filters.search_text) {
        params.append('search_text', filters.search_text);
      }
      if (filters.date_from) {
        params.append('date_from', filters.date_from);
      }
      if (filters.date_to) {
        params.append('date_to', filters.date_to);
      }
      
      const response = await api.get<CommentsListResponse>(
        `/comments/${parentType}/${parentId}?${params.toString()}`
      );
      
      console.log('Comments retrieved successfully:', response);
      return response;
      
    } catch (error: any) {
      console.error('Error getting comments:', error);
      
      // Handle access denied
      if (error.response?.status === 403) {
        throw new Error('Access denied: You do not have permission to view comments on this record');
      }
      
      // Handle parent record not found
      if (error.response?.status === 404) {
        throw new Error('Record not found: The item you are looking for does not exist');
      }
      
      // Generic error
      throw new Error(error.response?.data?.detail || 'Failed to retrieve comments');
    }
  }
  
  /**
   * Update an existing comment
   */
  static async updateComment(commentId: string, updateData: CommentUpdate): Promise<CommentOut> {
    try {
      console.log('Updating comment:', commentId, updateData);
      
      const response = await api.put<CommentOut>(`/comments/${commentId}`, updateData);
      
      console.log('Comment updated successfully:', response);
      return response;
      
    } catch (error: any) {
      console.error('Error updating comment:', error);
      
      // Handle validation errors
      if (error.response?.status === 422) {
        throw new Error(`Validation error: ${error.response.data?.detail || 'Invalid comment data'}`);
      }
      
      // Handle access denied
      if (error.response?.status === 403) {
        throw new Error('Access denied: You can only edit your own comments');
      }
      
      // Handle comment not found
      if (error.response?.status === 404) {
        throw new Error('Comment not found');
      }
      
      // Handle identical text
      if (error.response?.status === 409) {
        throw new Error('No changes detected: The comment text is identical to the current version');
      }
      
      // Generic error
      throw new Error(error.response?.data?.detail || 'Failed to update comment');
    }
  }
  
  /**
   * Delete a comment (soft delete)
   */
  static async deleteComment(commentId: string): Promise<{ success: boolean; message: string }> {
    try {
      console.log('Deleting comment:', commentId);
      
      const response = await api.delete<{ success: boolean; message: string }>(`/comments/${commentId}`);
      
      console.log('Comment deleted successfully:', response);
      return response;
      
    } catch (error: any) {
      console.error('Error deleting comment:', error);
      
      // Handle access denied
      if (error.response?.status === 403) {
        throw new Error('Access denied: You can only delete your own comments');
      }
      
      // Handle comment not found
      if (error.response?.status === 404) {
        throw new Error('Comment not found');
      }
      
      // Generic error
      throw new Error(error.response?.data?.detail || 'Failed to delete comment');
    }
  }
  
  /**
   * Get a specific comment by ID
   */
  static async getCommentById(commentId: string): Promise<CommentOut> {
    try {
      console.log('Getting comment by ID:', commentId);
      
      const response = await api.get<CommentOut>(`/comments/${commentId}`);
      
      console.log('Comment retrieved successfully:', response);
      return response;
      
    } catch (error: any) {
      console.error('Error getting comment:', error);
      
      // Handle access denied
      if (error.response?.status === 403) {
        throw new Error('Access denied: You do not have permission to view this comment');
      }
      
      // Handle comment not found
      if (error.response?.status === 404) {
        throw new Error('Comment not found');
      }
      
      // Generic error
      throw new Error(error.response?.data?.detail || 'Failed to retrieve comment');
    }
  }
  
  /**
   * Get comment statistics for a record
   */
  static async getCommentStats(parentType: ParentType, parentId: string): Promise<CommentStats> {
    try {
      console.log(`Getting comment stats for ${parentType}:${parentId}`);
      
      const response = await api.get<CommentStats>(`/comments/stats/${parentType}/${parentId}`);
      
      console.log('Comment stats retrieved successfully:', response);
      return response;
      
    } catch (error: any) {
      console.error('Error getting comment stats:', error);
      
      // Handle access denied
      if (error.response?.status === 403) {
        throw new Error('Access denied: You do not have permission to view stats for this record');
      }
      
      // Handle parent record not found
      if (error.response?.status === 404) {
        throw new Error('Record not found: The item you are looking for does not exist');
      }
      
      // Generic error - return empty stats instead of throwing
      console.warn('Failed to get comment stats, returning empty stats');
      return {
        total_comments: 0,
        comments_today: 0,
        comments_this_week: 0,
        top_commenters: [],
        most_commented_records: []
      };
    }
  }
  
  /**
   * Search users for mention autocomplete
   * Uses existing users endpoint with search functionality
   */
  static async searchUsersForMention(query: string): Promise<Array<{
    uid: string;
    display_name: string;
    email: string;
  }>> {
    try {
      if (!query || query.length < 2) {
        return [];
      }
      
      console.log('Searching users for mention:', query);
      
      // Use existing users endpoint - this endpoint should already exist in DRCR
      const response = await api.get(`/auth/users?search=${encodeURIComponent(query)}&limit=10`);
      
      // Transform response to mention format
      const users = (response as any).users || response || [];
      
      return users.map((user: any) => ({
        uid: user.firebase_uid || user.uid,
        display_name: user.display_name || user.email,
        email: user.email
      }));
      
    } catch (error: any) {
      console.error('Error searching users for mention:', error);
      
      // Don't throw error for user search - just return empty array
      console.warn('Failed to search users, returning empty array');
      return [];
    }
  }
  
  /**
   * Utility method to format comment text with @mentions highlighted
   */
  static formatCommentText(text: string, mentions: string[] = []): string {
    // This is a simple implementation - can be enhanced with more sophisticated highlighting
    let formattedText = text;
    
    // For now, just return the text as-is
    // In a full implementation, this would parse @mentions and create clickable highlights
    return formattedText;
  }
  
  /**
   * Utility method to extract mentions from text
   */
  static extractMentionsFromText(text: string): string[] {
    const mentionRegex = /@(\w+(?:\.\w+)*)/g;
    const mentions = [];
    let match;
    
    while ((match = mentionRegex.exec(text)) !== null) {
      mentions.push(match[1]);
    }
    
    return mentions;
  }
}