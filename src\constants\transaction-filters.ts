// Transaction filter constants for AccpayAllPage
export interface TransactionFilterOption {
  value: string;
  label: string;
  description?: string;
  count?: number;
}

export interface RecommendationConfig {
  value: string;
  label: string;
  description: string;
  badgeVariant: 'default' | 'secondary' | 'outline' | 'destructive';
  badgeClassName: string;
}

// User-friendly recommendation mappings
export const RECOMMENDATION_CONFIG: Record<string, RecommendationConfig> = {
  'create_prepayment': {
    value: 'create_prepayment',
    label: 'Create Prepayment',
    description: 'Recommended for prepayment amortization',
    badgeVariant: 'default',
    badgeClassName: 'bg-orange-100 text-orange-700 border-orange-300'
  },
  'create_amortization_schedule': {
    value: 'create_amortization_schedule',
    label: 'Create Prepayment',
    description: 'Recommended for prepayment amortization',
    badgeVariant: 'default',
    badgeClassName: 'bg-orange-100 text-orange-700 border-orange-300'
  },
  'pending_analysis': {
    value: 'pending_analysis',
    label: 'Pending Analysis',
    description: 'Awaiting AI analysis completion',
    badgeVariant: 'outline',
    badgeClassName: 'bg-yellow-50 text-yellow-700 border-yellow-300'
  },
  'no_prepayment': {
    value: 'no_prepayment',
    label: 'No Prepayment',
    description: 'No prepayment detected',
    badgeVariant: 'secondary',
    badgeClassName: 'bg-gray-100 text-gray-700 border-gray-300'
  },
  'analysis_failed': {
    value: 'analysis_failed',
    label: 'Analysis Failed',
    description: 'Analysis could not be completed',
    badgeVariant: 'destructive',
    badgeClassName: 'bg-red-100 text-red-700 border-red-300'
  },
  'skipped': {
    value: 'skipped',
    label: 'Skipped',
    description: 'Below scanning threshold',
    badgeVariant: 'secondary',
    badgeClassName: 'bg-gray-100 text-gray-600 border-gray-300'
  }
};

// Confidence score ranges
export const CONFIDENCE_RANGES = {
  HIGH: { min: 0.8, max: 1.0, label: 'High (80-100%)', color: 'text-green-600' },
  MEDIUM: { min: 0.5, max: 0.79, label: 'Medium (50-79%)', color: 'text-yellow-600' },
  LOW: { min: 0, max: 0.49, label: 'Low (0-49%)', color: 'text-red-600' }
};

// Transaction status options
export const TRANSACTION_STATUS_OPTIONS: TransactionFilterOption[] = [
  { value: 'authorised', label: 'Authorised' },
  { value: 'paid', label: 'Paid' },
  { value: 'draft', label: 'Draft' },
  { value: 'submitted', label: 'Submitted' },
  { value: 'deleted', label: 'Deleted' },
  { value: 'voided', label: 'Voided' }
];

// Recommendation filter options
export const RECOMMENDATION_FILTER_OPTIONS: TransactionFilterOption[] = [
  { 
    value: 'create_prepayment', 
    label: 'Create Prepayment',
    description: 'Recommended for prepayment creation'
  },
  { 
    value: 'pending_analysis', 
    label: 'Pending Analysis',
    description: 'Awaiting AI analysis'
  },
  { 
    value: 'no_prepayment', 
    label: 'No Prepayment',
    description: 'No prepayment detected'
  },
  { 
    value: 'analysis_failed', 
    label: 'Analysis Failed',
    description: 'Analysis could not be completed'
  },
  { 
    value: 'skipped', 
    label: 'Skipped',
    description: 'Below scanning threshold'
  }
];

// Confidence score filter options
export const CONFIDENCE_FILTER_OPTIONS: TransactionFilterOption[] = [
  { 
    value: 'high', 
    label: 'High Confidence (80-100%)',
    description: 'Highly confident recommendations'
  },
  { 
    value: 'medium', 
    label: 'Medium Confidence (50-79%)',
    description: 'Moderately confident recommendations'
  },
  { 
    value: 'low', 
    label: 'Low Confidence (0-49%)',
    description: 'Low confidence recommendations'
  }
];

// Analysis status filter options - REMOVED (not needed)

// Default transaction filter options for the multi-select filter
export const DEFAULT_TRANSACTION_FILTER_OPTIONS: TransactionFilterOption[] = [
  ...RECOMMENDATION_FILTER_OPTIONS,
  ...CONFIDENCE_FILTER_OPTIONS
];

// Helper functions
export const getRecommendationConfig = (action: string): RecommendationConfig => {
  return RECOMMENDATION_CONFIG[action] || {
    value: action,
    label: action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    description: 'Unknown recommendation',
    badgeVariant: 'secondary',
    badgeClassName: 'bg-gray-100 text-gray-700 border-gray-300'
  };
};

export const getConfidenceRange = (score: number): typeof CONFIDENCE_RANGES.HIGH => {
  if (score >= CONFIDENCE_RANGES.HIGH.min) return CONFIDENCE_RANGES.HIGH;
  if (score >= CONFIDENCE_RANGES.MEDIUM.min) return CONFIDENCE_RANGES.MEDIUM;
  return CONFIDENCE_RANGES.LOW;
};

export const formatConfidenceScore = (score: number): string => {
  if (!score || score === 0) return '';
  return `${Math.round(score * 100)}%`;
};

// Filter matching functions
export const matchesRecommendationFilter = (transaction: any, filters: string[]): boolean => {
  if (filters.length === 0) return true;
  
  // Check if transaction was skipped
  if (transaction.skip_reason || transaction.metadata?.skip_reason) {
    return filters.includes('skipped');
  }
  
  const recommendation = transaction.metadata?.recommended_action;
  if (!recommendation) {
    return filters.includes('analysis_failed');
  }
  
  // Handle consolidated prepayment options
  if (recommendation === 'create_amortization_schedule' && filters.includes('create_prepayment')) {
    return true;
  }
  
  return filters.includes(recommendation);
};

export const matchesConfidenceFilter = (transaction: any, filters: string[]): boolean => {
  if (filters.length === 0) return true;
  
  // Skipped transactions don't have confidence scores
  if (transaction.skip_reason || transaction.metadata?.skip_reason) return false;
  
  const confidence = transaction.metadata?.confidence_score || 0;
  
  return filters.some(filter => {
    switch (filter) {
      case 'high':
        return confidence >= CONFIDENCE_RANGES.HIGH.min;
      case 'medium':
        return confidence >= CONFIDENCE_RANGES.MEDIUM.min && confidence < CONFIDENCE_RANGES.HIGH.min;
      case 'low':
        return confidence < CONFIDENCE_RANGES.MEDIUM.min;
      default:
        return false;
    }
  });
};

// matchesAnalysisFilter function removed - no longer needed