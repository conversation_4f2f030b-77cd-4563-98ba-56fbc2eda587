import React from 'react';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Checkbox } from '../ui/checkbox';
import { ScrollArea } from '../ui/scroll-area';
import { Search, FileText, AlertCircle } from 'lucide-react';
import type { InvoiceData } from '../../types/schedule.types';

interface BillsListProps {
  bills: (InvoiceData & { supplierName?: string })[];
  selectedBills: string[];
  searchTerm: string;
  onSearchChange: (term: string) => void;
  onBillSelection: (billId: string, selected: boolean) => void;
  onSelectAll: (selected: boolean) => void;
  isLoading: boolean;
  error: string | null;
}

export function BillsList({
  bills,
  selectedBills,
  searchTerm,
  onSearchChange,
  onBillSelection,
  onSelectAll,
  isLoading,
  error,
}: BillsListProps) {
  const allSelected = bills.length > 0 && selectedBills.length === bills.length;
  const someSelected = selectedBills.length > 0 && selectedBills.length < bills.length;

  const formatCurrency = (amount: number, currencyCode: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'action_needed':
      case 'pending_configuration':
        return 'text-orange-600 border-orange-300 bg-orange-50';
      case 'proposed':
      case 'pending_review':
        return 'text-yellow-600 border-yellow-300 bg-yellow-50';
      case 'pending_confirmation':
        return 'text-blue-600 border-blue-300 bg-blue-50';
      case 'confirmed':
        return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'posted':
      case 'fully_posted':
        return 'bg-green-600 text-white border-green-600';
      case 'partially_posted':
        return 'bg-green-100 text-green-700 border-green-200';
      case 'validation_error':
      case 'error':
        return 'bg-red-100 text-red-700 border-red-200';
      case 'skipped':
        return 'text-gray-600 bg-gray-50 border-gray-300';
      case 'cancelled':
        return 'text-gray-600 bg-gray-50 border-gray-300';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getStatusDot = (status: string) => {
    switch (status.toLowerCase()) {
      case 'action_needed':
      case 'pending_configuration':
        return 'bg-orange-600'; // #ea580c
      case 'proposed':
      case 'pending_review':
        return 'bg-yellow-600'; // #ca8a04
      case 'pending_confirmation':
        return 'bg-blue-600'; // #2563eb
      case 'confirmed':
        return 'bg-blue-700'; // #1d4ed8
      case 'posted':
      case 'fully_posted':
        return 'bg-green-600'; // #16a34a
      case 'partially_posted':
        return 'bg-green-500'; // #22c55e
      case 'validation_error':
      case 'error':
        return 'bg-red-600'; // #dc2626
      case 'skipped':
        return 'bg-gray-500'; // #6b7280
      case 'cancelled':
        return 'bg-gray-500';
      default:
        return 'bg-gray-400'; // #9ca3af
    }
  };

  const getBillTags = (bill: InvoiceData & { supplierName?: string }) => {
    const tags = [];
    
    // Check for multiple accounts
    const accountCodes = new Set(
      bill.amortizableLineItems
        .filter(item => item.expenseAccountCode)
        .map(item => item.expenseAccountCode)
    );
    
    if (accountCodes.size > 1) {
      tags.push('Multi COA');
    } else if (accountCodes.size === 1) {
      tags.push('Single COA');
    }

    // Check for tracking categories (if available in data)
    if (bill.amortizableLineItems.some(item => item.description.includes('tracking'))) {
      tags.push('Multi tracking');
    }

    // Check for attachments
    if (bill.hasAttachment) {
      tags.push('Attachment');
    }

    return tags;
  };

  if (error) {
    return (
      <div className="p-4 text-center">
        <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
        <p className="text-sm text-red-600">{error}</p>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Bills for Amortization</h2>
          <Badge variant="secondary" className="ml-2">
            {bills.length}
          </Badge>
        </div>

        {/* Search Bar */}
        <div className="relative">
          <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <Input
            placeholder="Search suppliers, invoices..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Select All */}
      {bills.length > 0 && (
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={allSelected}
              onCheckedChange={(checked) => onSelectAll(!!checked)}
            />
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onSelectAll(!allSelected)}
              className="text-sm font-normal"
            >
              {allSelected ? 'Deselect all' : 'Select all'}
            </Button>
          </div>
        </div>
      )}

      {/* Bills List */}
      <ScrollArea className="flex-1">
        {isLoading ? (
          <div className="p-4 space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2 mb-1"></div>
                <div className="h-3 bg-gray-200 rounded w-1/4"></div>
              </div>
            ))}
          </div>
        ) : bills.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <FileText className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm">No bills found</p>
            {searchTerm && (
              <p className="text-xs mt-1">Try adjusting your search term</p>
            )}
          </div>
        ) : (
          <div className="p-2">
            {bills.map((bill) => {
              const isSelected = selectedBills.includes(bill.invoiceId);
              const tags = getBillTags(bill);
              
              return (
                <div
                  key={bill.invoiceId}
                  className={`p-3 rounded-lg border mb-2 cursor-pointer transition-colors ${
                    isSelected 
                      ? 'border-blue-300 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                  onClick={() => onBillSelection(bill.invoiceId, !isSelected)}
                >
                  <div className="flex items-start space-x-3">
                    <Checkbox
                      checked={isSelected}
                      onClick={(e) => e.stopPropagation()}
                      onCheckedChange={(checked) => onBillSelection(bill.invoiceId, !!checked)}
                    />
                    
                    <div className="flex-1 min-w-0">
                      {/* Supplier Name */}
                      <div className="flex items-center space-x-2 mb-1">
                        <div 
                          className={`w-2 h-2 rounded-full ${getStatusDot(bill.overallStatus)}`}
                        />
                        <p className="font-medium text-sm truncate">
                          {bill.supplierName || 'Unknown Supplier'}
                        </p>
                      </div>

                      {/* Invoice Reference and Amount */}
                      <div className="flex items-center justify-between mb-2">
                        <p className="text-xs text-gray-600 truncate mr-2">
                          {bill.reference}
                        </p>
                        <p className="text-sm font-semibold">
                          {formatCurrency(bill.totalAmount, bill.currencyCode)}
                        </p>
                      </div>

                      {/* Date */}
                      <p className="text-xs text-gray-500 mb-2">
                        {formatDate(bill.invoiceDate)}
                      </p>

                      {/* Status Badge */}
                      <div className="mb-2">
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${getStatusColor(bill.overallStatus)}`}
                        >
                          {bill.overallStatus.replace('_', ' ').toUpperCase()}
                        </Badge>
                      </div>

                      {/* Tags */}
                      {tags.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {tags.map((tag, index) => (
                            <Badge 
                              key={index}
                              variant="secondary" 
                              className="text-xs px-1 py-0"
                            >
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </ScrollArea>
    </div>
  );
}