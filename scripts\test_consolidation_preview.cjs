#!/usr/bin/env node
/**
 * FRONTEND CONSOLIDATION PREVIEW ACCURACY TEST
 * 
 * This tests the CRITICAL requirement that the consolidation preview shown to users
 * in the UI matches exactly what the backend will actually do when posting.
 * 
 * If this test fails, users will see incorrect information about how many journals
 * will be created, which is a major UX problem.
 * 
 * HOW IT WORKS:
 * 1. Uses the same test data for frontend and backend logic
 * 2. Runs frontend consolidation preview calculation 
 * 3. Simulates backend grouping logic
 * 4. Validates they produce identical results
 */

// Simulate the frontend consolidation preview logic from BillsAmortizationPage.tsx
function calculateFrontendConsolidationPreview(hierarchicalData) {
    const groupingData = {};
    let totalEntries = 0;
    
    hierarchicalData.suppliers.forEach(supplier => {
        supplier.invoices.forEach(invoice => {
            invoice.lineItems.forEach(lineItem => {
                if (lineItem.isSelected && lineItem.scheduleId) {
                    // Use the exact field mapping from the frontend
                    const invoiceId = invoice.invoiceId;
                    const amortizationAccount = lineItem.prepaymentAccountCode || '';
                    const expenseAccount = lineItem.expenseAccountCode || '';
                    const groupKey = `${invoiceId}|${amortizationAccount}|${expenseAccount}`;
                    
                    if (!groupingData[groupKey]) {
                        groupingData[groupKey] = {
                            invoiceId,
                            amortizationAccount,
                            expenseAccount,
                            scheduleIds: [],
                            entryCount: 0
                        };
                    }
                    
                    if (!groupingData[groupKey].scheduleIds.includes(lineItem.scheduleId)) {
                        groupingData[groupKey].scheduleIds.push(lineItem.scheduleId);
                    }
                    
                    // Count monthly entries (proxy for journal dates)
                    if (lineItem.monthlyBreakdown) {
                        groupingData[groupKey].entryCount += Object.keys(lineItem.monthlyBreakdown).length;
                        totalEntries += Object.keys(lineItem.monthlyBreakdown).length;
                    }
                }
            });
        });
    });
    
    // Estimate journals: simplified to total unique month dates across all groups
    let estimatedJournals = 0;
    Object.values(groupingData).forEach(group => {
        // Each group with same accounts will create journals per month
        const avgMonthsPerGroup = group.entryCount / Math.max(group.scheduleIds.length, 1);
        estimatedJournals += Math.ceil(avgMonthsPerGroup);
    });
    
    const consolidatedGroups = Object.values(groupingData).filter(group => group.scheduleIds.length > 1).length;
    
    return {
        estimatedJournals: Math.max(estimatedJournals, 1),
        totalEntries,
        consolidatedGroups,
        groupingData
    };
}

// Simulate the backend grouping logic from schedules.py
function simulateBackendGrouping(schedulesData) {
    const consolidationGroups = {};
    
    Object.entries(schedulesData).forEach(([scheduleId, scheduleData]) => {
        const monthlyEntries = scheduleData.monthlyEntries || [];
        
        // Process all entries that are ready for posting
        const targetIndices = monthlyEntries
            .map((entry, index) => ({entry, index}))
            .filter(({entry}) => 
                ["proposed", "due_for_posting", "posting_failed"].includes(entry.status) && 
                !entry.xero_journal_id
            )
            .map(({index}) => index);
        
        if (targetIndices.length === 0) {
            return;
        }
        
        // Get account codes for grouping - handle both camelCase and snake_case
        const transactionId = scheduleData.transaction_id || scheduleData.transactionId || "";
        const prepaymentAccount = scheduleData.amortizationAccountCode || scheduleData.amortization_account_code || "";
        const expenseAccount = scheduleData.expenseAccountCode || scheduleData.expense_account_code || "";
        
        // Group by journal date
        const entriesByDate = {};
        targetIndices.forEach(entryIndex => {
            const entry = monthlyEntries[entryIndex];
            let journalDateRaw = entry.monthDate || entry.month_date || "";
            
            // Normalize journal date to string format
            let journalDate;
            if (journalDateRaw && typeof journalDateRaw === 'object' && journalDateRaw.toISOString) {
                journalDate = journalDateRaw.toISOString().substr(0, 10);
            } else {
                journalDate = String(journalDateRaw).substr(0, 10);
            }
            
            if (journalDate) {
                if (!entriesByDate[journalDate]) {
                    entriesByDate[journalDate] = [];
                }
                entriesByDate[journalDate].push(entryIndex);
            }
        });
        
        // Create groups for each date
        Object.entries(entriesByDate).forEach(([journalDate, indices]) => {
            const groupKey = `${transactionId}|${prepaymentAccount}|${expenseAccount}|${journalDate}`;
            
            if (!consolidationGroups[groupKey]) {
                consolidationGroups[groupKey] = {
                    schedules: {},
                    journal_date: journalDate,
                    transaction_id: transactionId,
                    prepayment_account: prepaymentAccount,
                    expense_account: expenseAccount
                };
            }
            
            consolidationGroups[groupKey].schedules[scheduleId] = {
                entry_indices: indices,
                schedule_data: scheduleData
            };
        });
    });
    
    return consolidationGroups;
}

// Test scenarios
const testScenarios = [
    {
        name: "Simple Consolidation - Same Bill, Same Accounts",
        description: "2 line items from same bill with same accounts should consolidate",
        expectedJournals: 3, // Jan, Feb, Mar
        expectedConsolidation: true,
        hierarchicalData: {
            suppliers: [{
                supplierId: "supplier_1",
                supplierName: "Test Supplier",
                invoices: [{
                    invoiceId: "invoice_123",
                    reference: "INV-001", 
                    lineItems: [
                        {
                            scheduleId: "schedule_1",
                            isSelected: true,
                            prepaymentAccountCode: "1200",
                            expenseAccountCode: "6200",
                            monthlyBreakdown: {
                                "2025-01": { amount: 100.00, status: "proposed" },
                                "2025-02": { amount: 100.00, status: "proposed" },
                                "2025-03": { amount: 100.00, status: "proposed" }
                            }
                        },
                        {
                            scheduleId: "schedule_2", 
                            isSelected: true,
                            prepaymentAccountCode: "1200", // Same accounts
                            expenseAccountCode: "6200",   // Same accounts
                            monthlyBreakdown: {
                                "2025-01": { amount: 150.00, status: "proposed" }, // Same months
                                "2025-02": { amount: 150.00, status: "proposed" },
                                "2025-03": { amount: 150.00, status: "proposed" }
                            }
                        }
                    ]
                }]
            }]
        },
        backendSchedulesData: {
            "schedule_1": {
                transaction_id: "invoice_123",
                amortizationAccountCode: "1200",
                expenseAccountCode: "6200",
                monthlyEntries: [
                    { status: "proposed", monthDate: "2025-01-31", amount: 100.00, xero_journal_id: null },
                    { status: "proposed", monthDate: "2025-02-28", amount: 100.00, xero_journal_id: null },
                    { status: "proposed", monthDate: "2025-03-31", amount: 100.00, xero_journal_id: null }
                ]
            },
            "schedule_2": {
                transaction_id: "invoice_123",
                amortizationAccountCode: "1200",
                expenseAccountCode: "6200", 
                monthlyEntries: [
                    { status: "proposed", monthDate: "2025-01-31", amount: 150.00, xero_journal_id: null },
                    { status: "proposed", monthDate: "2025-02-28", amount: 150.00, xero_journal_id: null },
                    { status: "proposed", monthDate: "2025-03-31", amount: 150.00, xero_journal_id: null }
                ]
            }
        }
    },
    {
        name: "No Consolidation - Different Expense Accounts",
        description: "2 schedules with different expense accounts should NOT consolidate",
        expectedJournals: 6, // 3 months × 2 separate schedules 
        expectedConsolidation: false,
        hierarchicalData: {
            suppliers: [{
                supplierId: "supplier_1",
                supplierName: "Test Supplier",
                invoices: [{
                    invoiceId: "invoice_123",
                    reference: "INV-001",
                    lineItems: [
                        {
                            scheduleId: "schedule_1",
                            isSelected: true, 
                            prepaymentAccountCode: "1200",
                            expenseAccountCode: "6200", // Office expenses
                            monthlyBreakdown: {
                                "2025-01": { amount: 100.00, status: "proposed" },
                                "2025-02": { amount: 100.00, status: "proposed" },
                                "2025-03": { amount: 100.00, status: "proposed" }
                            }
                        },
                        {
                            scheduleId: "schedule_2",
                            isSelected: true,
                            prepaymentAccountCode: "1200",
                            expenseAccountCode: "6300", // Software expenses - DIFFERENT!
                            monthlyBreakdown: {
                                "2025-01": { amount: 150.00, status: "proposed" },
                                "2025-02": { amount: 150.00, status: "proposed" },
                                "2025-03": { amount: 150.00, status: "proposed" }
                            }
                        }
                    ]
                }]
            }]
        },
        backendSchedulesData: {
            "schedule_1": {
                transaction_id: "invoice_123",
                amortizationAccountCode: "1200",
                expenseAccountCode: "6200",
                monthlyEntries: [
                    { status: "proposed", monthDate: "2025-01-31", amount: 100.00, xero_journal_id: null },
                    { status: "proposed", monthDate: "2025-02-28", amount: 100.00, xero_journal_id: null },
                    { status: "proposed", monthDate: "2025-03-31", amount: 100.00, xero_journal_id: null }
                ]
            },
            "schedule_2": {
                transaction_id: "invoice_123", 
                amortizationAccountCode: "1200",
                expenseAccountCode: "6300", // Different expense account
                monthlyEntries: [
                    { status: "proposed", monthDate: "2025-01-31", amount: 150.00, xero_journal_id: null },
                    { status: "proposed", monthDate: "2025-02-28", amount: 150.00, xero_journal_id: null },
                    { status: "proposed", monthDate: "2025-03-31", amount: 150.00, xero_journal_id: null }
                ]
            }
        }
    },
    {
        name: "Complex Multi-Invoice Scenario",
        description: "Multiple invoices with mixed consolidation opportunities",
        expectedJournals: 4, // 2 months for consolidated group + 2 months for separate schedule
        expectedConsolidation: true,
        hierarchicalData: {
            suppliers: [{
                supplierId: "supplier_1", 
                supplierName: "Test Supplier",
                invoices: [
                    {
                        invoiceId: "invoice_123",
                        reference: "INV-001",
                        lineItems: [
                            {
                                scheduleId: "schedule_1",
                                isSelected: true,
                                prepaymentAccountCode: "1200",
                                expenseAccountCode: "6200",
                                monthlyBreakdown: {
                                    "2025-01": { amount: 100.00, status: "proposed" },
                                    "2025-02": { amount: 100.00, status: "proposed" }
                                }
                            },
                            {
                                scheduleId: "schedule_2", 
                                isSelected: true,
                                prepaymentAccountCode: "1200", // Same accounts as schedule_1
                                expenseAccountCode: "6200",   // Same accounts as schedule_1
                                monthlyBreakdown: {
                                    "2025-01": { amount: 150.00, status: "proposed" },
                                    "2025-02": { amount: 150.00, status: "proposed" }
                                }
                            }
                        ]
                    },
                    {
                        invoiceId: "invoice_456", // Different invoice
                        reference: "INV-002", 
                        lineItems: [
                            {
                                scheduleId: "schedule_3",
                                isSelected: true,
                                prepaymentAccountCode: "1200",
                                expenseAccountCode: "6200", // Same accounts but different invoice
                                monthlyBreakdown: {
                                    "2025-01": { amount: 200.00, status: "proposed" },
                                    "2025-02": { amount: 200.00, status: "proposed" }
                                }
                            }
                        ]
                    }
                ]
            }]
        },
        backendSchedulesData: {
            "schedule_1": {
                transaction_id: "invoice_123",
                amortizationAccountCode: "1200",
                expenseAccountCode: "6200",
                monthlyEntries: [
                    { status: "proposed", monthDate: "2025-01-31", amount: 100.00, xero_journal_id: null },
                    { status: "proposed", monthDate: "2025-02-28", amount: 100.00, xero_journal_id: null }
                ]
            },
            "schedule_2": {
                transaction_id: "invoice_123", // Same transaction as schedule_1
                amortizationAccountCode: "1200",
                expenseAccountCode: "6200",
                monthlyEntries: [
                    { status: "proposed", monthDate: "2025-01-31", amount: 150.00, xero_journal_id: null },
                    { status: "proposed", monthDate: "2025-02-28", amount: 150.00, xero_journal_id: null }
                ]
            },
            "schedule_3": {
                transaction_id: "invoice_456", // Different transaction
                amortizationAccountCode: "1200",
                expenseAccountCode: "6200",
                monthlyEntries: [
                    { status: "proposed", monthDate: "2025-01-31", amount: 200.00, xero_journal_id: null },
                    { status: "proposed", monthDate: "2025-02-28", amount: 200.00, xero_journal_id: null }
                ]
            }
        }
    }
];

function runTest(scenario) {
    console.log(`\n🧪 Testing: ${scenario.name}`);
    console.log(`📝 ${scenario.description}`);
    
    // Run frontend consolidation preview
    const frontendResult = calculateFrontendConsolidationPreview(scenario.hierarchicalData);
    
    // Run backend consolidation grouping
    const backendGroups = simulateBackendGrouping(scenario.backendSchedulesData);
    const backendJournals = Object.keys(backendGroups).length;
    
    console.log(`📊 Results:`);
    console.log(`   Frontend estimated journals: ${frontendResult.estimatedJournals}`);
    console.log(`   Backend actual journal groups: ${backendJournals}`);
    console.log(`   Expected journals: ${scenario.expectedJournals}`);
    
    // Critical validation: Frontend preview must match backend reality
    const previewAccurate = frontendResult.estimatedJournals === backendJournals;
    const expectedMatch = backendJournals === scenario.expectedJournals;
    
    if (previewAccurate && expectedMatch) {
        console.log(`✅ PASS: Frontend preview matches backend exactly`);
        if (scenario.expectedConsolidation && frontendResult.consolidatedGroups > 0) {
            console.log(`✅ CONSOLIDATION: ${frontendResult.consolidatedGroups} groups consolidated`);
        }
        return true;
    } else {
        console.log(`❌ FAIL:`);
        if (!previewAccurate) {
            console.log(`   - Frontend preview (${frontendResult.estimatedJournals}) ≠ Backend reality (${backendJournals})`);
        }
        if (!expectedMatch) {
            console.log(`   - Backend result (${backendJournals}) ≠ Expected (${scenario.expectedJournals})`);
        }
        return false;
    }
}

function main() {
    console.log("🚀 FRONTEND CONSOLIDATION PREVIEW ACCURACY TEST");
    console.log("=" * 60);
    console.log("Testing that frontend consolidation preview matches backend behavior exactly.");
    console.log("If this fails, users will see incorrect journal count estimates.");
    console.log();
    
    let passed = 0;
    let total = testScenarios.length;
    
    testScenarios.forEach(scenario => {
        if (runTest(scenario)) {
            passed++;
        }
    });
    
    console.log("\n" + "=" * 60);
    if (passed === total) {
        console.log(`🎉 ALL TESTS PASSED (${passed}/${total})`);
        console.log("✅ Frontend consolidation preview is accurate");
        console.log("✅ Users will see correct journal count estimates");
        console.log("✅ Consolidation logic works as expected");
    } else {
        console.log(`❌ TESTS FAILED (${passed}/${total})`);
        console.log("🚨 Frontend preview does NOT match backend behavior");
        console.log("🚨 Users will see INCORRECT journal count estimates");
        console.log("🔧 Fix required before production deployment");
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = {
    calculateFrontendConsolidationPreview,
    simulateBackendGrouping,
    testScenarios
};