import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useFirmName } from '../hooks/useFirmName';
import { useClientStore } from '../store/client.store';
import { EntitiesService } from '../services/entities.service';
import { TokenUsageService } from '../services/tokenUsage.service';
import type { ClientTokenUsageResponse } from '../services/tokenUsage.service';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { Button } from '../components/ui/button';
import { Separator } from '../components/ui/separator';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '../components/ui/breadcrumb';
import {
  SidebarTrigger,
} from '../components/ui/sidebar';
import { CreditCard, Clock, AlertCircle, CheckCircle } from 'lucide-react';

export function BillingPage() {
  const navigate = useNavigate();
  const { firmName, isLoading: firmNameLoading } = useFirmName();
  const { clients, fetchClients } = useClientStore();
  
  const [aggregatedCredits, setAggregatedCredits] = useState<{
    total_credit_balance: number;
    total_credits_used: number;
    total_clients: number;
    clients_with_credits: number;
    last_activity?: string;
  } | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  type UsageJob = ClientTokenUsageResponse["sync_jobs"][number] & { entity_name?: string };
  const [usageHistory, setUsageHistory] = useState<UsageJob[]>([]);
  const [usageLoading, setUsageLoading] = useState(false);
  const [usageError, setUsageError] = useState<string | null>(null);
  const entityNameMap = useRef<Record<string, string>>({});
  const USAGE_SECTION_ID = "usage-history-section";

  useEffect(() => {
    const loadAggregatedCreditData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        // Fetch clients if not already loaded
        if (clients.length === 0) {
          await fetchClients();
        }
        
        // Get credit info for all clients
        const clientList = useClientStore.getState().clients;
        if (clientList.length > 0) {
          let totalBalance = 0;
          let totalUsed = 0;
          let clientsWithCredits = 0;
          let lastActivity: string | undefined;

          // Fetch credit info for each client
          const creditPromises = clientList.map(async (client) => {
            try {
              const response = await EntitiesService.getEntitiesForClient(client.client_id, {});
              if (response.credit_info) {
                totalBalance += response.credit_info.credit_balance || 0;
                totalUsed += response.credit_info.credits_used_total || 0;
                clientsWithCredits++;
                
                // Track most recent activity
                if (response.credit_info.last_credit_usage) {
                  if (!lastActivity || new Date(response.credit_info.last_credit_usage) > new Date(lastActivity)) {
                    lastActivity = response.credit_info.last_credit_usage;
                  }
                }
              }
              if (response.entities) {
                response.entities.forEach((ent:any)=>{ entityNameMap.current[ent.entity_id]=ent.entity_name; });
              }
              return { client: client.name, ...response.credit_info };
            } catch (err) {
              console.warn(`Failed to fetch credits for client ${client.name}:`, err);
              return null;
            }
          });

          await Promise.all(creditPromises);

          // Load usage history for each client
          setUsageLoading(true);
          const usageJobs: UsageJob[] = [];
          const usagePromises = clientList.map(async (client) => {
            try {
              const usageResp = await TokenUsageService.getClientTokenUsage(client.client_id, { limit: 20 });
              usageJobs.push(...usageResp.sync_jobs.map(j=> ({...j, entity_name: entityNameMap.current[j.entity_id] })));
            } catch (e) {
              console.warn('Failed to fetch usage for client', client.client_id, e);
            }
          });
          await Promise.all(usagePromises);
          // Sort by started_at DESC
          usageJobs.sort((a, b) => (b.started_at && a.started_at ? new Date(b.started_at).getTime() - new Date(a.started_at).getTime() : 0));
          setUsageHistory(usageJobs);
          setUsageLoading(false);

          setAggregatedCredits({
            total_credit_balance: totalBalance,
            total_credits_used: totalUsed,
            total_clients: clientList.length,
            clients_with_credits: clientsWithCredits,
            last_activity: lastActivity
          });
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load credit information');
      } finally {
        setIsLoading(false);
      }
    };

    loadAggregatedCreditData();
  }, [clients, fetchClients]);

  return (
    <>
      <header className="sticky top-0 z-10 flex h-16 shrink-0 items-center gap-2 border-b bg-background px-4">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem className="hidden md:block">
            <BreadcrumbLink
              href="#"
              onClick={(e) => {
                e.preventDefault();
                navigate('/dashboard');
              }}
              className="cursor-pointer"
            >
              {firmNameLoading ? 'Loading...' : firmName}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator className="hidden md:block" />
          <BreadcrumbItem>
            <BreadcrumbPage>Billing</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    </header>
    <div className="flex-1 overflow-auto">
      <div className="p-8 max-w-4xl">
          <div className="mb-8">
            <h1 className="text-2xl font-bold mb-2">Portfolio Credits & Billing</h1>
            <p className="text-muted-foreground">
              Overview of credit usage and balance across all your clients.
            </p>
          </div>

            <div className="space-y-6">
          {/* Portfolio Credit Overview Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Portfolio Credit Overview
              </CardTitle>
              <CardDescription>
                Aggregated credit balance and usage across all your clients.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="animate-pulse">
                  <div className="flex items-center justify-between mb-6">
                    <div>
                      <div className="h-12 bg-gray-200 rounded w-24 mb-2"></div>
                      <div className="h-4 bg-gray-200 rounded w-32"></div>
                    </div>
                    <div className="text-right">
                      <div className="h-6 bg-gray-200 rounded w-20 mb-2"></div>
                      <div className="h-4 bg-gray-200 rounded w-28"></div>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="text-center p-4 bg-muted rounded-lg">
                        <div className="h-8 bg-gray-200 rounded w-16 mx-auto mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-20 mx-auto"></div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : error ? (
                <div className="text-center py-8">
                  <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                  <p className="text-red-600 mb-4">{error}</p>
                  <Button onClick={() => window.location.reload()}>Try Again</Button>
                </div>
              ) : aggregatedCredits ? (
                <>
                  <div className="flex items-center justify-between mb-6">
                    <div>
                      <div className="text-4xl font-bold text-primary">{aggregatedCredits.total_credit_balance}</div>
                      <p className="text-muted-foreground">Total Credits Remaining</p>
                    </div>
                    <div className="text-right">
                      <Badge variant="default" className="flex items-center gap-1 mb-2">
                        <CheckCircle className="h-3 w-3" />
                        {aggregatedCredits.clients_with_credits} Active Clients
                      </Badge>
                      <p className="text-sm text-muted-foreground">1 credit = 1 page processed</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div className="text-center p-4 bg-muted rounded-lg">
                      <div className="text-2xl font-bold">{aggregatedCredits.total_credits_used}</div>
                      <div className="text-sm text-muted-foreground">Total Credits Used</div>
                    </div>
                    <div className="text-center p-4 bg-muted rounded-lg">
                      <div className="text-2xl font-bold">{aggregatedCredits.total_clients}</div>
                      <div className="text-sm text-muted-foreground">Total Clients</div>
                    </div>
                    <div className="text-center p-4 bg-muted rounded-lg">
                      <div className="text-2xl font-bold">{aggregatedCredits.clients_with_credits}</div>
                      <div className="text-sm text-muted-foreground">Clients with Credits</div>
                    </div>
                  </div>

                  {aggregatedCredits.last_activity && (
                    <div className="mb-6 p-3 bg-blue-50 rounded-lg border border-blue-200">
                      <div className="text-sm text-blue-800">
                        <strong>Last Activity:</strong> {new Date(aggregatedCredits.last_activity).toLocaleString()}
                      </div>
                    </div>
                  )}
                </>
              ) : (
                <div className="text-center py-8">
                  <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">No credit information available</p>
                  <p className="text-sm text-gray-500 mt-2">Make sure you have at least one client configured</p>
                </div>
              )}

              <div className="flex gap-2">
                <Button disabled className="bg-primary">
                  Buy 1,000 Credits - $10 (Coming Soon)
                </Button>
                <Button variant="outline" onClick={() => document.getElementById(USAGE_SECTION_ID)?.scrollIntoView({ behavior: 'smooth' })}>
                  <Clock className="h-4 w-4 mr-2" />
                  View Usage History
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Credit Packages Card */}
          <Card>
            <CardHeader>
              <CardTitle>Credit Packages</CardTitle>
              <CardDescription>
                Purchase additional credits for document processing.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-lg bg-primary/5 border-primary/20">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold">Standard Package</h3>
                    <Badge variant="default">Most Popular</Badge>
                  </div>
                  <div className="text-3xl font-bold mb-2">$10</div>
                  <div className="text-muted-foreground mb-4">1,000 Credits</div>
                  <ul className="text-sm space-y-1 mb-4">
                    <li>• Process up to 1,000 pages</li>
                    <li>• Credits never expire</li>
                    <li>• Instant activation</li>
                  </ul>
                  <Button disabled className="w-full">
                    Purchase (Coming Soon)
                  </Button>
                </div>

                <div className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold">Free Trial</h3>
                    <Badge variant="secondary">Current</Badge>
                  </div>
                  <div className="text-3xl font-bold mb-2">$0</div>
                  <div className="text-muted-foreground mb-4">1,000 Credits</div>
                  <ul className="text-sm space-y-1 mb-4">
                    <li>• Perfect for testing</li>
                    <li>• No payment required</li>
                    <li>• Full feature access</li>
                  </ul>
                  <Button variant="outline" disabled className="w-full">
                    Active
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Usage History */}
          <Card id={USAGE_SECTION_ID} className="mt-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" /> Usage History (Last {usageHistory.length} syncs)
              </CardTitle>
              <CardDescription>Credit usage per sync job.</CardDescription>
            </CardHeader>
            <CardContent>
              {usageLoading ? (
                <p>Loading usage history…</p>
              ) : usageError ? (
                <p className="text-red-500">{usageError}</p>
              ) : usageHistory.length === 0 ? (
                <p>No recent usage found.</p>
              ) : (
                <div className="overflow-auto">
                  <table className="min-w-full text-sm">
                    <thead>
                      <tr className="text-left border-b">
                        <th className="py-2 pr-4">Date</th>
                        <th className="py-2 pr-4">Entity</th>
                        <th className="py-2 pr-4 text-right">Credits Used</th>
                        <th className="py-2 pr-4 text-right">Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      {usageHistory.map((job) => (
                        <tr key={job.sync_job_id} className="border-b hover:bg-muted/50">
                          <td className="py-2 pr-4 whitespace-nowrap">{job.started_at ? new Date(job.started_at).toLocaleString() : '—'}</td>
                          <td className="py-2 pr-4">{job.entity_name ?? job.entity_id ?? '—'}</td>
                          <td className="py-2 pr-4 text-right">
                            {job.credit_usage?.credits_used ? job.credit_usage.credits_used.toLocaleString() : '—'}
                          </td>
                          <td className="py-2 pr-4 text-right">
                            <Badge variant={job.status === 'completed' ? 'default' : job.status === 'failed' ? 'destructive' : 'secondary'}>
                              {job.status || 'Unknown'}
                            </Badge>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Payment System Notice */}
          <Card className="border-amber-200 bg-amber-50">
            <CardContent className="pt-6">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-amber-600 mt-0.5" />
                <div>
                  <h3 className="font-semibold text-amber-800">Payment System Coming Soon</h3>
                  <p className="text-sm text-amber-700 mt-1">
                    Credit tracking is now live! Your actual credit usage is being monitored in real-time
                    with automatic page counting and credit deduction. Payment processing with Stripe 
                    integration is coming soon.
                  </p>
                  <div className="mt-3 text-sm text-amber-700">
                    <strong>Currently Active:</strong>
                    <ul className="list-disc list-inside mt-1 space-y-1">
                      <li>✅ Real-time credit balance tracking</li>
                      <li>✅ Automatic page counting and credit deduction</li>
                      <li>✅ Service-specific usage monitoring (OpenAI/Mistral)</li>
                    </ul>
                    <div className="mt-2">
                      <strong>Coming Soon:</strong>
                      <ul className="list-disc list-inside mt-1 space-y-1">
                        <li>Secure Stripe payment processing</li>
                        <li>Credit purchase options</li>
                        <li>Detailed usage history and analytics</li>
                        <li>Low credit warnings and notifications</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          </div>
      </div>
    </div>
    </>
  );
}
