import React, { useState } from 'react';
import { Settings2, ChevronDown } from 'lucide-react';
import { Button } from './button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuCheckboxItem } from './dropdown-menu';
import { cn } from '@/lib/utils';
import { 
  DEFAULT_DASHBOARD_FILTER_OPTIONS, 
  CLIENT_STATUS_OPTIONS, 
  CONNECTION_STATUS_OPTIONS, 
  ENTITY_TYPE_OPTIONS 
} from '@/constants/dashboard-filters';

export interface DashboardFilterOption {
  value: string;
  label: string;
  count?: number;
}

interface DashboardFilterSelectorProps {
  selectedFilters: string[];
  onFiltersChange: (filters: string[]) => void;
  filterOptions?: DashboardFilterOption[];
  isLoading?: boolean;
  disabled?: boolean;
  className?: string;
}

export function DashboardFilterSelector({
  selectedFilters,
  onFiltersChange,
  filterOptions = DEFAULT_DASHBOARD_FILTER_OPTIONS,
  isLoading = false,
  disabled = false,
  className,
}: DashboardFilterSelectorProps) {
  const [open, setOpen] = useState(false);

  const handleToggleFilter = (filterValue: string, checked: boolean) => {
    if (checked) {
      onFiltersChange([...selectedFilters.filter(f => f !== filterValue), filterValue]);
    } else {
      onFiltersChange(selectedFilters.filter(f => f !== filterValue));
    }
  };

  const handleSelectAll = () => {
    onFiltersChange(filterOptions.map(option => option.value));
  };

  const handleClearAll = () => {
    onFiltersChange([]);
  };

  const totalCount = filterOptions.reduce((sum, option) => sum + (option.count || 0), 0);

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="outline" 
          className={cn("flex items-center gap-1 h-8 text-xs", className)}
          disabled={disabled || isLoading}
          aria-label="Filter clients"
        >
          <Settings2 className="h-3 w-3" />
          Status ({selectedFilters.length})
          <ChevronDown className="h-3 w-3" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>Filter Clients</span>
          {totalCount > 0 && (
            <span className="text-xs text-muted-foreground">
              Total: {totalCount}
            </span>
          )}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {/* Bulk actions */}
        <div className="flex gap-1 p-1">
          <Button
            variant="ghost"
            size="sm"
            className="h-6 px-2 text-xs flex-1"
            onClick={handleSelectAll}
            disabled={selectedFilters.length === filterOptions.length}
          >
            Select All
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 px-2 text-xs flex-1"
            onClick={handleClearAll}
            disabled={selectedFilters.length === 0}
          >
            Clear All
          </Button>
        </div>
        
        <DropdownMenuSeparator />
        
        {/* All Clients option */}
        <DropdownMenuCheckboxItem
          checked={selectedFilters.length === 0}
          onCheckedChange={(checked) => {
            if (checked) {
              handleClearAll();
            }
          }}
        >
          <span className="font-medium">All Clients</span>
          {totalCount > 0 && (
            <span className="ml-auto text-xs text-muted-foreground">
              ({totalCount})
            </span>
          )}
        </DropdownMenuCheckboxItem>
        
        <DropdownMenuSeparator />
        
        {/* Client Status Group */}
        <div className="px-2 py-1">
          <div className="text-xs font-medium text-muted-foreground mb-1">Client Status</div>
          {CLIENT_STATUS_OPTIONS.map((option) => (
            <DropdownMenuCheckboxItem
              key={option.value}
              checked={selectedFilters.includes(option.value)}
              onCheckedChange={(checked) => handleToggleFilter(option.value, checked)}
              className="pl-6"
            >
              {option.label}
              {option.count !== undefined && (
                <span className="ml-auto text-xs text-muted-foreground">
                  ({option.count})
                </span>
              )}
            </DropdownMenuCheckboxItem>
          ))}
        </div>
        
        <DropdownMenuSeparator />
        
        {/* Connection Status Group */}
        <div className="px-2 py-1">
          <div className="text-xs font-medium text-muted-foreground mb-1">Connection Status</div>
          {CONNECTION_STATUS_OPTIONS.map((option) => (
            <DropdownMenuCheckboxItem
              key={option.value}
              checked={selectedFilters.includes(option.value)}
              onCheckedChange={(checked) => handleToggleFilter(option.value, checked)}
              className="pl-6"
            >
              {option.label}
              {option.count !== undefined && (
                <span className="ml-auto text-xs text-muted-foreground">
                  ({option.count})
                </span>
              )}
            </DropdownMenuCheckboxItem>
          ))}
        </div>
        
        <DropdownMenuSeparator />
        
        {/* Entity Type Group */}
        <div className="px-2 py-1">
          <div className="text-xs font-medium text-muted-foreground mb-1">Entity Type</div>
          {ENTITY_TYPE_OPTIONS.map((option) => (
            <DropdownMenuCheckboxItem
              key={option.value}
              checked={selectedFilters.includes(option.value)}
              onCheckedChange={(checked) => handleToggleFilter(option.value, checked)}
              className="pl-6"
            >
              {option.label}
              {option.count !== undefined && (
                <span className="ml-auto text-xs text-muted-foreground">
                  ({option.count})
                </span>
              )}
            </DropdownMenuCheckboxItem>
          ))}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}