import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from '../ui/card';
import { Label } from '../ui/label';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { AutocompletePopover } from '../ui/autocomplete-popover';
import type { AutocompleteItem } from '../ui/autocomplete-popover';
import { Button } from '../ui/button';
import { Settings2, Calendar, Building } from 'lucide-react';
import { api } from '../../lib/api';

interface AmortizationConfig {
  method: string;
  startDate: string;
  endDate: string;
  prepaymentAccount: string;
  expenseAccount: string;
  numberOfPeriods: number;
  custom_narration?: string;
}

interface Account {
  account_id: string;
  code: string;
  name: string;
  type: string;
  class: string;
  status: string;
}

interface AmortizationConfigurationProps {
  config: AmortizationConfig;
  onChange: (updates: Partial<AmortizationConfig>) => void;
  disabled: boolean; // true when no bills selected – triggers empty state
  readOnly?: boolean; // true for posted schedules – show UI but lock fields
  entityId?: string;
  selectedCount?: number;
}

interface EntitySettings {
  excluded_pnl_account_codes?: (string | null)[];
  prepayment_asset_account_codes?: (string | null)[];
}

export function AmortizationConfiguration({
  config,
  onChange,
  disabled,
  readOnly = false,
  entityId,
  selectedCount = 1,
}: AmortizationConfigurationProps) {
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [isLoadingAccounts, setIsLoadingAccounts] = useState(false);
  const [entitySettings, setEntitySettings] = useState<EntitySettings | null>(null);

  // Load chart of accounts
  useEffect(() => {
    const loadAccounts = async () => {
      console.log('AmortizationConfiguration: Loading accounts for entityId:', entityId);
      if (!entityId) {
        console.log('AmortizationConfiguration: No entityId, skipping account load');
        setAccounts([]);
        setEntitySettings(null);
        return;
      }

      try {
        setIsLoadingAccounts(true);
        console.log('AmortizationConfiguration: Fetching accounts from API...');
        const response = await api.get(`/entities/${entityId}/accounts`);
        console.log('AmortizationConfiguration: API response:', response);
        const accountsData = (response as any).accounts || [];
        console.log('AmortizationConfiguration: Setting accounts:', accountsData);
        setAccounts(accountsData);

        // Fetch entity details to access .settings
        try {
          const entityDetail = await api.getEntity(entityId);
          setEntitySettings(entityDetail.settings || null);
        } catch (settingsErr) {
          console.warn('AmortizationConfiguration: Failed to load entity settings from entity detail:', settingsErr);
          setEntitySettings(null);
        }
      } catch (error) {
        console.error('AmortizationConfiguration: Failed to load accounts:', error);
        setAccounts([]);
      } finally {
        setIsLoadingAccounts(false);
      }
    };

    loadAccounts();
  }, [entityId]);

  // Filter accounts by type
  console.log('AmortizationConfiguration: All accounts:', accounts);
  console.log('AmortizationConfiguration: Account types:', accounts.map(a => a.type));
  console.log('AmortizationConfiguration: Account statuses:', accounts.map(a => a.status));
  
  const excludedSet = new Set(
    (entitySettings?.excluded_pnl_account_codes || []).filter(Boolean) as string[]
  );

  const preferredPrepaymentSet = new Set(
    (entitySettings?.prepayment_asset_account_codes || []).filter(Boolean) as string[]
  );

  // Only ACTIVE accounts with non-empty code
  const activeAccounts = accounts.filter(
    acc => acc.status === 'ACTIVE' && acc.code && acc.code.trim() !== ''
  );

  // Prepayment accounts: only those listed in settings; if list empty -> all active
  let prepaymentAccounts = preferredPrepaymentSet.size > 0
    ? activeAccounts.filter(acc => preferredPrepaymentSet.has(acc.code))
    : activeAccounts;

  // Keep preferred ones first (redundant when filtered but harmless)
  prepaymentAccounts = [...prepaymentAccounts].sort((a, b) => a.code.localeCompare(b.code, undefined, { numeric: true }));

  // Expense accounts already filtered; sort by code
  const expenseAccounts = activeAccounts
    .filter(acc => !excludedSet.has(acc.code))
    .sort((a, b) => a.code.localeCompare(b.code, undefined, { numeric: true }));
  
  console.log('AmortizationConfiguration: Filtered prepayment accounts:', prepaymentAccounts);
  console.log('AmortizationConfiguration: Filtered expense accounts:', expenseAccounts);

  const formatAccountOption = (account: Account) => {
    return `${account.code} - ${account.name}`;
  };

  const handleMethodChange = (value: string) => {
    onChange({ method: value });
  };

  const handleStartDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onChange({ startDate: event.target.value });
  };

  const handlePeriodsChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const periods = parseInt(event.target.value) || 1;
    onChange({ numberOfPeriods: Math.max(1, Math.min(60, periods)) });
  };

  const handlePrepaymentAccountChange = (value: string) => {
    onChange({ prepaymentAccount: value });
  };

  const handleExpenseAccountChange = (value: string) => {
    onChange({ expenseAccount: value });
  };

  const handleNarrationChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange({ custom_narration: event.target.value });
  };


  if (disabled) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings2 className="h-5 w-5" />
            Schedule Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <Settings2 className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p className="text-lg font-medium mb-2">No Bills Selected</p>
            <p className="text-sm">Select bills from the left sidebar to view schedule configuration</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const isBulkMode = selectedCount > 1;
  const fieldsDisabled = readOnly || isBulkMode;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings2 className="h-5 w-5" />
          Amortization Configuration
          {isBulkMode && (
            <span className="ml-2 text-blue-600 text-sm font-normal">
              (Bulk mode - {selectedCount} bills)
            </span>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Amortization Method */}
          <div className="space-y-2">
            <Label htmlFor="method">Amortization Method</Label>
            <Select 
              value={config.method} 
              onValueChange={handleMethodChange}
              disabled={fieldsDisabled}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select method" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="equal_monthly">
                  Equal Monthly (Straight line)
                </SelectItem>
                <SelectItem value="day_based">
                  Day-based (prorated by days)
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Number of Periods */}
          <div className="space-y-2">
            <Label htmlFor="periods">Number of Periods (Months)</Label>
            <Input
              id="periods"
              type="number"
              min="1"
              max="60"
              value={config.numberOfPeriods}
              onChange={handlePeriodsChange}
              disabled={fieldsDisabled}
              className="w-full"
            />
          </div>

          {/* Start Date */}
          <div className="space-y-2">
            <Label htmlFor="startDate">Start Date</Label>
            <div className="relative">
              <Calendar className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                id="startDate"
                type="date"
                value={config.startDate}
                onChange={handleStartDateChange}
                disabled={fieldsDisabled}
                className="pl-10"
              />
            </div>
          </div>

          {/* End Date - Display only */}
          <div className="space-y-2">
            <Label>End Date</Label>
            <Input
              value={config.endDate}
              disabled
              className="bg-muted"
            />
            <div className="text-xs text-gray-500">Calculated automatically</div>
          </div>
        </div>

        {/* Account Selectors */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Prepayment Account */}
          <div className="space-y-2">
            <Label htmlFor="prepaymentAccount">
              <div className="flex items-center gap-2">
                <Building className="h-4 w-4" />
                Prepayment Account
              </div>
            </Label>
            <AutocompletePopover
              items={[
                // Show current value if it's not in the available accounts
                ...(config.prepaymentAccount && 
                   !prepaymentAccounts.some(acc => acc.code === config.prepaymentAccount) 
                   ? [{
                       value: config.prepaymentAccount,
                       label: `${config.prepaymentAccount} - (Current from schedules)`,
                       metadata: 'Current'
                     }] 
                   : []
                ),
                // Add all available prepayment accounts
                ...prepaymentAccounts.map(account => ({
                  value: account.code,
                  label: formatAccountOption(account),
                  metadata: account.type
                }))
              ]}
              selectedValue={config.prepaymentAccount || null}
              onSelect={handlePrepaymentAccountChange}
              placeholder="Select prepayment account"
              searchPlaceholder="Search prepayment accounts..."
              emptyMessage={isLoadingAccounts ? "Loading accounts..." : "No prepayment accounts available"}
              disabled={fieldsDisabled || isLoadingAccounts}
              isLoading={isLoadingAccounts}
              buttonClassName="h-9 text-sm px-3 py-2"
              className="w-full"
              popoverClassName="w-[400px]"
            />
          </div>

          {/* Expense Account */}
          <div className="space-y-2">
            <Label htmlFor="expenseAccount">
              <div className="flex items-center gap-2">
                <Building className="h-4 w-4" />
                Expense Account
              </div>
            </Label>
            <AutocompletePopover
              items={[
                // Show current value if it's not in the available accounts
                ...(config.expenseAccount && 
                   !expenseAccounts.some(acc => acc.code === config.expenseAccount) 
                   ? [{
                       value: config.expenseAccount,
                       label: `${config.expenseAccount} - (Current from schedules)`,
                       metadata: 'Current'
                     }] 
                   : []
                ),
                // Add all available expense accounts
                ...expenseAccounts.map(account => ({
                  value: account.code,
                  label: formatAccountOption(account),
                  metadata: account.type
                }))
              ]}
              selectedValue={config.expenseAccount || null}
              onSelect={handleExpenseAccountChange}
              placeholder="Select expense account"
              searchPlaceholder="Search expense accounts..."
              emptyMessage={isLoadingAccounts ? "Loading accounts..." : "No expense accounts available"}
              disabled={fieldsDisabled || isLoadingAccounts}
              isLoading={isLoadingAccounts}
              buttonClassName="h-9 text-sm px-3 py-2"
              className="w-full"
              popoverClassName="w-[400px]"
            />
          </div>
        </div>

        {/* Narration for Journal Entries */}
        <div className="space-y-2">
          <Label htmlFor="narration">Narration (Optional)</Label>
          <Textarea
            id="narration"
            value={config.custom_narration || ''}
            onChange={handleNarrationChange}
            placeholder="Custom narration for journal entries (leave blank for auto-generated)"
            disabled={fieldsDisabled}
            rows={2}
            maxLength={500}
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>
              This text will appear as the narration in Xero journal entries. 
              If left blank, system will auto-generate based on supplier and invoice details.
            </span>
            <span className="tabular-nums">
              {(config.custom_narration || '').length}/500
            </span>
          </div>
        </div>

        {/* Configuration Status */}
        {disabled && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <p className="text-sm text-yellow-800">
              Select bills from the left sidebar to configure amortization settings.
            </p>
          </div>
        )}

        {isBulkMode && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-sm text-blue-800">
              Configuration cannot be changed in bulk mode. Select a single bill to modify settings.
            </p>
          </div>
        )}

        {!disabled && !isBulkMode && (!config.prepaymentAccount || !config.expenseAccount) && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-sm text-blue-800">
              Complete the account selection to finalize the amortization configuration.
            </p>
          </div>
        )}

      </CardContent>
    </Card>
  );
}