/**
 * Cache Utilities - Global functions for cache management
 * These functions can be called from browser console for debugging
 */

import { cacheManager } from '../lib/cache-manager';

// Make cache manager available globally for debugging
declare global {
  interface Window {
    cacheManager: typeof cacheManager;
    clearAllCaches: () => void;
    getCacheStatus: () => any;
    clearCacheByType: (type: 'api' | 'localStorage' | 'sessionStorage' | 'browser') => void;
    clearCookies: () => void;
  }
}

// Expose cache manager to global scope for debugging
if (typeof window !== 'undefined') {
  window.cacheManager = cacheManager;
  window.clearAllCaches = () => cacheManager.clearAllCaches();
  window.getCacheStatus = () => cacheManager.getCacheStatus();
  window.clearCacheByType = (type) => cacheManager.clearCacheByType(type);
  window.clearCookies = () => cacheManager.clearCookies();
}

/**
 * Emergency cache clear function
 * Can be called from browser console: emergencyClearCache()
 */
export function emergencyClearCache(): void {
  console.log('🚨 EMERGENCY CACHE CLEAR INITIATED');
  
  try {
    // Clear all known localStorage keys
    const keysToRemove = [
      'navigation-store',
      'resizable-panel-width', 
      'sidebar-expanded-items',
      'auth-store',
      'client-store',
      'drcr_',
      'navigation-',
      'auth-',
      'client-'
    ];

    // Remove specific keys
    keysToRemove.forEach(key => {
      if (localStorage.getItem(key)) {
        localStorage.removeItem(key);
        console.log(`🗑️ Removed: ${key}`);
      }
    });

    // Remove keys by prefix
    const allKeys = Object.keys(localStorage);
    allKeys.forEach(key => {
      if (key.startsWith('drcr_') || key.startsWith('navigation-') || 
          key.startsWith('auth-') || key.startsWith('client-')) {
        localStorage.removeItem(key);
        console.log(`🗑️ Removed by prefix: ${key}`);
      }
    });

    // Clear sessionStorage
    sessionStorage.clear();
    console.log('🗑️ SessionStorage cleared');

    // Clear browser caches
    if ('caches' in window) {
      caches.keys().then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            console.log(`🗑️ Deleting cache: ${cacheName}`);
            return caches.delete(cacheName);
          })
        );
      }).then(() => {
        console.log('🗑️ Browser caches cleared');
      });
    }

    // Clear cookies
    cacheManager.clearCookies();

    console.log('✅ Emergency cache clear completed');
    console.log('🔄 Reloading page in 2 seconds...');
    
    setTimeout(() => {
      window.location.reload();
    }, 2000);

  } catch (error) {
    console.error('❌ Emergency cache clear failed:', error);
    console.log('🔄 Forcing page reload anyway...');
    window.location.reload();
  }
}

/**
 * Debug function to show current cache status
 */
export function debugCacheStatus(): void {
  console.log('🔍 CACHE STATUS DEBUG');
  console.log('=====================');
  
  const status = cacheManager.getCacheStatus();
  
  console.log('LocalStorage keys:', status.localStorageKeys);
  console.log('SessionStorage keys:', status.sessionStorageKeys);
  console.log('Has localStorage data:', status.hasLocalStorage);
  console.log('Has sessionStorage data:', status.hasSessionStorage);
  
  // Show Zustand store data
  try {
    const navigationStore = localStorage.getItem('navigation-store');
    if (navigationStore) {
      console.log('Navigation store data:', JSON.parse(navigationStore));
    }
    
    const authStore = localStorage.getItem('auth-store');
    if (authStore) {
      console.log('Auth store data:', JSON.parse(authStore));
    }
  } catch (error) {
    console.log('Could not parse store data:', error);
  }
}

// Make functions available globally
if (typeof window !== 'undefined') {
  (window as any).emergencyClearCache = emergencyClearCache;
  (window as any).debugCacheStatus = debugCacheStatus;
}

export { cacheManager }; 