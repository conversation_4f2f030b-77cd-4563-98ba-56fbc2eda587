import axios from 'axios';
import type { AxiosInstance, AxiosResponse, AxiosError, AxiosRequestConfig } from 'axios';
import { toast } from 'sonner';
import { auth } from './firebase';

// Extend AxiosRequestConfig to include our custom properties
interface ExtendedAxiosRequestConfig extends AxiosRequestConfig {
  cache?: boolean;
  cacheTtl?: number;
}

export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
}

// Type definitions for API responses
export interface EntitySummary {
  entity_id: string;
  entity_name: string;
  type: 'xero' | 'qbo' | 'manual';
  connection_status: 'active' | 'error' | 'disconnected' | 'syncing' | 'pending';
  last_sync?: string;
  error_message?: string;
  sync_status?: {
    is_syncing: boolean;
    current_step: string;
    progress_percentage: number;
    estimated_remaining?: string;
    user_message: string;
    last_sync_completed?: string;
    sync_duration_warning?: string;
  };
  // Amortization schedule status tracking
  schedule_status_counts?: {
    pending_configuration?: number;
    proposed?: number;
    pending_confirmation?: number;
    confirmed?: number;
    posted?: number;
    error?: number;
  };
  pending_schedules_count?: number;
  has_schedule_action_needed?: boolean;
}

export interface ClientSummary {
  client_id: string;
  name: string; // Fix: API returns 'name', not 'client_name'
  status: string;
  entities?: EntitySummary[]; // Make optional since it's not always included
  pending_items_count?: number;
  error_count?: number;
  last_activity?: string;
  overall_status?: 'ok' | 'action_needed' | 'error';
  // Additional fields from actual API response
  client_type?: string;
  industry?: string;
  entities_count?: number;
  active_entities_count?: number;
  // Amortization schedule status breakdown
  schedule_status_counts?: {
    pending_configuration?: number;
    proposed?: number;
    pending_confirmation?: number;
    confirmed?: number;
    posted?: number;
    error?: number;
  };
}

export interface EntitySettings {
  entity_id: string;
  entity_name: string;
  prepayment_asset_account_codes: string[];
  excluded_pnl_account_codes?: string[];
  default_expense_account_code?: string;
  default_amortization_months?: number;
  amortization_materiality_threshold?: number;
  auto_sync_enabled?: boolean;
  sync_frequency?: 'hourly' | 'daily' | 'weekly' | 'manual';
  sync_spend_money?: boolean;
  transaction_sync_start_date?: string;
  sync_invoices?: boolean;
  sync_bills?: boolean;
  sync_payments?: boolean;
  sync_bank_transactions?: boolean;
  sync_journal_entries?: boolean;
  auto_post_proposed_journals?: boolean;
  base_currency_code?: string;
  initial_sync_completed?: boolean;
  last_full_sync_date?: string;
}

export interface Account {
  account_id?: string;
  code: string;
  name: string;
  type: 'ASSET' | 'EXPENSE' | 'LIABILITY' | 'EQUITY' | 'REVENUE' | 'CURRENT';
  class?: 'ASSET' | 'EXPENSE' | 'LIABILITY' | 'EQUITY' | 'REVENUE';
  status?: string;
  description?: string;
  raw_xero_data?: {
    Class?: string;
    ReportingCode?: string;
    [key: string]: any;
  };
}

export interface DashboardData {
  proposed: {
    count: number;
    total_amount: number;
  };
  approved: {
    count: number;
    total_amount: number;
  };
  this_month: {
    count: number;
    total_amount: number;
  };
  recent_transactions: any[];
}

// Cache interface
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

// Request deduplication
interface PendingRequest {
  promise: Promise<any>;
  timestamp: number;
}

export class ApiClient {
  private client: AxiosInstance;
  private cache = new Map<string, CacheEntry<any>>();
  private pendingRequests = new Map<string, PendingRequest>();
  private readonly DEFAULT_CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly REQUEST_TIMEOUT = 60000; // 60 seconds - extended for slow backend

  constructor(baseURL: string = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081') {
    console.log('DEBUG: ApiClient initialized with baseURL:', baseURL);

    // Clear any existing cache on initialization
    this.cache.clear();

    this.client = axios.create({
      baseURL,
      timeout: this.REQUEST_TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache', // Prevent browser caching
      },
      // Windows-specific optimizations
      maxRedirects: 3,
      validateStatus: (status) => status < 500, // Don't retry on 4xx errors
    });

    this.setupInterceptors();
    this.startCacheCleanup();
  }

  private setupInterceptors() {
    // Add request interceptor for auth token
    this.client.interceptors.request.use(
      async (config) => {
        const token = await this.getAuthToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => {
        // Log successful responses for debugging
        if (response.config.url?.includes('xero/connect/initiate')) {
          console.log('Response interceptor - successful response:', {
            status: response.status,
            statusText: response.statusText,
            data: response.data,
            url: response.config.url
          });
        }
        return response;
      },
      (error: AxiosError) => {
        // Log errors in response interceptor
        if (error.config?.url?.includes('xero/connect/initiate')) {
          console.error('Response interceptor - error:', {
            message: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            url: error.config?.url
          });
        }
        return this.handleApiError(error);
      }
    );
  }

  private async getAuthToken(): Promise<string | null> {
    const user = auth.currentUser;
    if (user) {
      const token = await user.getIdToken();
      return token;
    }
    return null;
  }

  private handleApiError(error: AxiosError) {
    console.log('handleApiError called with:', {
      status: error.response?.status,
      url: error.config?.url,
      message: error.message
    });

    if (error.response?.status === 401) {
      // Handle unauthorized errors
      console.log('Unauthorized access. Token may be expired or invalid.');

      // If we're not already on the login page, redirect
      if (window.location.pathname !== '/login') {
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }

  // Cache management
  private getCacheKey(url: string, config?: ExtendedAxiosRequestConfig): string {
    const params = config?.params ? JSON.stringify(config.params) : '';
    return `${url}${params}`;
  }

  private getCachedData<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (entry && Date.now() - entry.timestamp < entry.ttl) {
      return entry.data;
    }
    if (entry) {
      this.cache.delete(key); // Remove expired entry
    }
    return null;
  }

  private setCachedData<T>(key: string, data: T, ttl: number = this.DEFAULT_CACHE_TTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  private startCacheCleanup(): void {
    // Clean up expired cache entries every 2 minutes (more frequent for local dev)
    setInterval(() => {
      const now = Date.now();
      for (const [key, entry] of this.cache.entries()) {
        if (now - entry.timestamp > entry.ttl) {
          this.cache.delete(key);
        }
      }
    }, 2 * 60 * 1000);
  }

  // Request deduplication
  private async deduplicateRequest<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
    const existing = this.pendingRequests.get(key);
    if (existing && Date.now() - existing.timestamp < 1000) { // 1 second deduplication window for faster local dev
      return existing.promise;
    }

    const promise = requestFn().finally(() => {
      this.pendingRequests.delete(key);
    });

    this.pendingRequests.set(key, {
      promise,
      timestamp: Date.now()
    });

    return promise;
  }

  async get<T>(url: string, config?: ExtendedAxiosRequestConfig): Promise<T> {
    const cacheKey = this.getCacheKey(url, config);

    // Check cache first (if caching is enabled)
    if (config?.cache !== false) {
      const cachedData = this.getCachedData<T>(cacheKey);
      if (cachedData) {
        console.log(`🚀 Cache HIT: ${url}`);
        return cachedData;
      }
    }

    // Deduplicate requests
    return this.deduplicateRequest(cacheKey, async () => {
      try {
        const startTime = performance.now();

        // Create a clean config object for axios, removing our custom properties
        const axiosConfig: AxiosRequestConfig = { ...config };
        delete (axiosConfig as any).cache;
        delete (axiosConfig as any).cacheTtl;

        const response = await this.client.get<T>(url, axiosConfig);

        const endTime = performance.now();
        const duration = Math.round(endTime - startTime);

        // Only log slow requests in development
        if (import.meta.env.DEV && duration > 1000) {
          console.warn(`🐌 SLOW API ${url}: ${duration}ms`);
        } else if (import.meta.env.DEV && duration > 500) {
          console.log(`⏱️ API ${url}: ${duration}ms`);
        }

        // Ensure we return the data, not the full response object
        const data = response.data;

        // Cache the response (if caching is enabled)
        if (config?.cache !== false) {
          this.setCachedData(cacheKey, data, config?.cacheTtl);
        }

        return data;
      } catch (error) {
        console.error(`API GET error for ${url}:`, error);

        // Log detailed error information
        if (error && typeof error === 'object') {
          const axiosError = error as any;
          console.error('Detailed error info:', {
            message: axiosError.message,
            code: axiosError.code,
            status: axiosError.response?.status,
            statusText: axiosError.response?.statusText,
            data: axiosError.response?.data,
            headers: axiosError.response?.headers,
            config: {
              url: axiosError.config?.url,
              method: axiosError.config?.method,
              baseURL: axiosError.config?.baseURL
            }
          });
        }

        throw error;
      }
    });
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig & { cache?: boolean; cacheTtl?: number }): Promise<T> {
    try {
      console.log(`ApiClient POST: ${url}`, data);

      // Create a clean config object for axios, removing our custom properties
      const axiosConfig: AxiosRequestConfig = { ...config };
      delete (axiosConfig as any).cache;
      delete (axiosConfig as any).cacheTtl;

      const response = await this.client.post<T>(url, data, axiosConfig);
      console.log(`ApiClient POST success: ${url}`, response.data);

      // Invalidate related cache entries on mutations
      this.invalidateCache(url);

      return response.data;
    } catch (error) {
      console.error(`ApiClient POST error: ${url}`, error);

      // Log detailed error information for debugging
      if (error && typeof error === 'object') {
        const axiosError = error as any;
        console.error('ApiClient POST detailed error:', {
          message: axiosError.message,
          code: axiosError.code,
          status: axiosError.response?.status,
          statusText: axiosError.response?.statusText,
          data: axiosError.response?.data,
          headers: axiosError.response?.headers,
          config: {
            url: axiosError.config?.url,
            method: axiosError.config?.method,
            baseURL: axiosError.config?.baseURL
          }
        });
      }

      throw error;
    }
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.put<T>(url, data, config);

    // Invalidate related cache entries on mutations
    this.invalidateCache(url);

    return response.data;
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.delete<T>(url, config);

    // Invalidate related cache entries on mutations
    this.invalidateCache(url);

    return response.data;
  }

  private invalidateCache(url: string): void {
    // Remove cache entries that might be affected by this mutation
    const keysToDelete: string[] = [];
    for (const key of this.cache.keys()) {
      if (key.includes(url.split('/')[1])) { // Match by resource type
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => this.cache.delete(key));
  }

  /**
   * Clear all API cache entries
   */
  clearCache(): void {
    this.cache.clear();
    this.pendingRequests.clear();
    console.log('🧹 API cache cleared');
  }

  // Client Management Methods with optimized caching
  async getClients(): Promise<{ clients: ClientSummary[] }> {
    return this.get<{ clients: ClientSummary[] }>('/clients/', {
      cache: true,
      cacheTtl: 10 * 60 * 1000 // Cache for 10 minutes - clients don't change often
    } as ExtendedAxiosRequestConfig);
  }

  async getClientsSummary(filters?: {
    page?: number;
    limit?: number;
    client_filter?: string;
    status_filter?: string;
  }): Promise<{
    clients: ClientSummary[];
    pagination: {
      current_page: number;
      page_size: number;
      total_items: number;
      total_pages: number;
    };
  }> {
    const params = new URLSearchParams();
    if (filters?.page) params.append('page', filters.page.toString());
    if (filters?.limit) params.append('limit', filters.limit.toString());
    if (filters?.client_filter) params.append('client_filter', filters.client_filter);
    if (filters?.status_filter) params.append('status_filter', filters.status_filter);

    const queryString = params.toString();
    return this.get(`/clients/summary${queryString ? `?${queryString}` : ''}`, {
      cache: false // Disable cache temporarily to ensure fresh data
    } as ExtendedAxiosRequestConfig);
  }

  async createClient(clientData: { name: string; [key: string]: any }): Promise<{ message: string; client_id: string; name: string }> {
    return this.post('/clients/', clientData);
  }

  async getClient(clientId: string): Promise<ClientSummary> {
    return this.get(`/clients/${clientId}`, {
      cache: true,
      cacheTtl: 60 * 1000
    } as ExtendedAxiosRequestConfig);
  }

  async updateClient(clientId: string, clientData: any): Promise<{ message: string }> {
    return this.put(`/clients/${clientId}`, clientData);
  }

  // Entity Management Methods
  async getEntitiesForClient(clientId: string): Promise<{ entities: EntitySummary[] }> {
    return this.get(`/entities/?client_id=${clientId}`, {
      cache: true,
      cacheTtl: 5 * 60 * 1000 // Cache for 5 minutes - entities don't change often
    } as ExtendedAxiosRequestConfig);
  }

  async getEntity(entityId: string): Promise<EntitySummary & { settings?: EntitySettings }> {
    return this.get(`/entities/${entityId}`, {
      cache: true,
      cacheTtl: 60 * 1000 // 1 minute cache
    } as ExtendedAxiosRequestConfig);
  }

  async updateEntitySettings(entityId: string, settings: Partial<EntitySettings>): Promise<{ message: string }> {
    return this.put(`/entities/${entityId}/settings`, settings);
  }

  async checkEntityConnectionStatus(entityId: string): Promise<{
    entity_id: string;
    entity_name: string;
    type: string;
    connection_status: {
      status: 'active' | 'error' | 'disconnected' | 'syncing' | 'pending';
      last_checked?: string;
      error_message?: string;
    };
  }> {
    return this.get(`/entities/${entityId}/connection/status`);
  }

  async disconnectEntity(entityId: string): Promise<{ message: string }> {
    return this.post(`/entities/${entityId}/connection/disconnect`);
  }

  // Entity Setup Wizard Methods
  async getEntityWizardAnalysis(entityId: string): Promise<{
    entity_id: string;
    entity_name: string;
    organization_info: {
      name: string;
      base_currency: string;
      financial_year_start?: string;
    };
    bills_analysis: {
      bills_with_attachments: number;
      total_bills: number;
      scanning_cost_estimate: string;
      expected_prepayments: string;
      financial_year_start: string;
    };
    accounts_analysis: {
      prepayment_asset_accounts: Array<{
        code: string;
        name: string;
        recommended: boolean;
      }>;
      relevant_expense_accounts: Array<{
        code: string;
        name: string;
      }>;
      suggested_exclusions: Array<{
        code: string;
        name: string;
      }>;
    };
    recommendations: {
      sync_start_date: string;
      sync_frequency: string;
      enable_ai_scanning: boolean;
      selected_asset_accounts: string[];
      excluded_accounts: string[];
      base_currency: string;
    };
  }> {
    return this.get(`/entities/${entityId}/analysis/wizard`);
  }

  async completeEntitySetup(entityId: string, setupData: {
    enable_ai_scanning: boolean;
    sync_settings: {
      sync_start_date: string;
      sync_frequency: string;
      auto_sync_enabled: boolean;
      sync_invoices: boolean;
      sync_bills: boolean;
      sync_payments: boolean;
      sync_bank_transactions: boolean;
      sync_journal_entries: boolean;
      sync_spend_money: boolean;
    };
    account_settings: {
      prepayment_asset_accounts: string[];
      excluded_accounts: string[];
      default_expense_account?: string;
      base_currency: string;
      amortization_materiality_threshold?: number;
      amortization_apply_50_percent_rule?: boolean;
    };
  }): Promise<{ message: string; entity_id: string }> {
    return this.post(`/entities/${entityId}/setup/complete`, setupData);
  }

  async getEntityBillAggregates(entityId: string, dateRangeMonths: number = 12, amountThreshold: number = 0, disableCache: boolean = false): Promise<{
    aggregates: {
      [month: string]: {
        by_account: {
          [accountCode: string]: {
            bills: number;
            with_attachments: number;
            total_amount: number;
          };
        };
        by_supplier: {
          [supplier: string]: {
            bills: number;
            with_attachments: number;
            total_amount: number;
          };
        };
        total_bills: number;
        total_with_attachments: number;
        total_amount: number;
      };
    };
    account_classifications: {
      prepayment_candidates: string[];
      exclude_recommended: string[];
      classic_exclusions: string[];
      revenue_accounts: string[];
      fixed_asset_accounts: string[];
      bank_accounts: string[];
      expense_accounts: string[];
      all_accounts: {
        [accountCode: string]: {
          name: string;
          type: string;
          class: string;
        };
      };
    };
    date_range: {
      start_date: string;
      end_date: string;
      months: number;
    };
    total_bills: number;
    total_with_attachments: number;
  }> {
    return this.get(`/entities/${entityId}/analysis/bill-aggregates?date_range_months=${dateRangeMonths}&amount_threshold=${amountThreshold}`, {
      cache: !disableCache
    } as ExtendedAxiosRequestConfig);
  }

  // Xero Integration Methods
  async initiateXeroConnection(clientId: string): Promise<{ authorization_url: string }> {
    console.log(`ApiClient: Initiating Xero connection for client ${clientId}`);

    // Use longer timeout and disable caching for Xero connection initiation
    const response = await this.get<{ authorization_url: string }>(`/xero/connect/initiate/${clientId}`, {
      timeout: 45000, // 45 seconds for initiation
      cache: false // Disable caching for this request
    } as ExtendedAxiosRequestConfig);

    console.log('ApiClient: Raw response from Xero initiate:', response);
    console.log('ApiClient: Response type:', typeof response);
    if (response && typeof response === 'object') {
      console.log('ApiClient: Response keys:', Object.keys(response));
    }
    return response;
  }

  async getXeroConfiguration(clientId: string, entities?: string): Promise<{
    client: {
      client_id: string;
      name: string;
      status: string;
    };
    entities: Array<{
      entity_id: string;
      entity_name: string;
      type: string;
      status: string;
      connection_status: string;
      settings: any;
      requires_configuration: boolean;
    }>;
    has_xero_entities: boolean;
    configuration_complete: boolean;
  }> {
    const params = entities ? `?entities=${entities}` : '';
    return this.get(`/clients/${clientId}/xero/configure${params}`, {
      cache: true,
      cacheTtl: 30 * 1000 // 30 second cache
    } as ExtendedAxiosRequestConfig);
  }

  async getEntityAccounts(entityId: string): Promise<{ accounts: Account[] }> {
    console.log(`🔍 Fetching accounts for entity: ${entityId} (using cached data)`);
    const result = await this.get(`/entities/${entityId}/accounts`, {
      timeout: 10000, // 10 seconds for cached accounts fetch
      cache: true, // Enable cache since we're using local data
      cacheTtl: 5 * 60 * 1000 // Cache for 5 minutes
    } as ExtendedAxiosRequestConfig);

    console.log(`📊 Entity accounts response:`, JSON.stringify(result, null, 2));

    // Handle the response properly - the API returns { accounts: Account[] }
    const rawAccounts = (result as any)?.accounts || [];
    console.log(`📊 Total accounts found: ${rawAccounts.length}`);
    
    // Normalize account properties (should already be normalized from our API)
    const accounts = rawAccounts.map((acc: any) => ({
      account_id: acc.account_id || acc.AccountID,
      code: acc.code || acc.Code || '',
      name: acc.name || acc.Name || '',
      type: acc.type || acc.Type,
      class: acc.class || acc.Class,
      status: acc.status || acc.Status,
      description: acc.description || acc.Description,
      raw_xero_data: acc.raw_xero_data || acc
    }));
    
    console.log(`📊 Asset accounts (by class): ${accounts.filter((acc: Account) => acc.class === 'ASSET').length}`);
    console.log(`📊 Asset accounts (by type): ${accounts.filter((acc: Account) => acc.type === 'ASSET').length}`);

    return { accounts };
  }

  // Legacy method - deprecated, use getEntityAccounts instead
  async getXeroAccounts(entityId: string): Promise<{ accounts: Account[] }> {
    console.warn('⚠️ getXeroAccounts is deprecated, use getEntityAccounts instead');
    return this.getEntityAccounts(entityId);
  }

  async revokeXeroConnection(entityId: string): Promise<{ message: string }> {
    return this.post(`/xero/entities/${entityId}/revoke`, {}, {
      timeout: 30000 // 30 seconds for revoke operation
    });
  }

  // Organization Selection Methods
  async getAvailableXeroOrganizations(clientId: string): Promise<{
    client_id: string;
    organizations: Array<{
      tenant_id: string;
      tenant_name: string;
      is_already_connected: boolean;
      connection_type: string;
    }>;
    reconnections_processed?: number;
    message?: string;
  }> {
    return this.get(`/xero/clients/${clientId}/xero/available-organizations`);
  }

  async connectSelectedXeroOrganization(clientId: string, tenantId: string): Promise<{
    message: string;
    entity: {
      entity_id: string;
      entity_name: string;
      requires_configuration: boolean;
      is_new_connection: boolean;
    };
  }> {
    console.log(`ApiClient: Connecting Xero organization ${tenantId} for client ${clientId}`);
    try {
      const result = await this.post<{
        message: string;
        entity: {
          entity_id: string;
          entity_name: string;
          requires_configuration: boolean;
          is_new_connection: boolean;
        };
      }>(`/xero/clients/${clientId}/xero/connect-organization`, {
        tenant_id: tenantId
      }, {
        timeout: 60000 // 60 seconds for organization connection (restored)
      });
      console.log('ApiClient: Connect organization successful:', result);
      return result;
    } catch (error) {
      console.error('ApiClient: Connect organization failed:', error);
      throw error;
    }
  }

  // Dashboard Methods
  async getClientDashboard(clientId: string, entityId?: string): Promise<DashboardData> {
    const params = entityId ? `?client_id=${clientId}&entity_id=${entityId}` : `?client_id=${clientId}`;
    return this.get(`/reports/dashboard${params}`);
  }

  // Transaction Methods
  async getTransactions(filters: {
    client_id: string;
    entity_id?: string;
    transaction_type?: string;
    status?: string;
    page?: number;
    limit?: number;
  }): Promise<{
    transactions: any[];
    total: number;
    page: number;
    limit: number;
    total_pages: number;
  }> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });
    return this.get(`/transactions/?${params.toString()}`);
  }

  async getDashboardTransactions(filters: {
    client_id?: string;
    entity_id?: string;
    status?: string;
    transaction_type?: string;
    require_action?: boolean;
    status_filters?: string[];
    page?: number;
    limit?: number;
  }, options?: { cache?: boolean }): Promise<{
    transactions: any[];
    total: number;
    page: number;
    limit: number;
    total_pages: number;
  }> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        if (key === 'status_filters' && Array.isArray(value)) {
          // Add each status filter as a separate parameter
          value.forEach((status: string) => {
            params.append('status_filters', status);
          });
        } else {
          params.append(key, value.toString());
        }
      }
    });
    
    const config = options?.cache !== undefined ? { cache: options.cache } : {};
    return this.get(`/transactions/dashboard?${params.toString()}`, config);
  }

  async getTransaction(transactionId: string): Promise<any> {
    return this.get(`/transactions/${transactionId}`);
  }

  // Schedule Methods
  async getSchedule(scheduleId: string): Promise<any> {
    return this.get(`/schedules/${scheduleId}`);
  }

  async updateSchedule(scheduleId: string, scheduleData: any): Promise<{
    message: string;
    schedule_id: string;
    status_progression?: {
      from: string;
      to: string;
    };
  }> {
    return this.put(`/schedules/${scheduleId}`, scheduleData);
  }

  async confirmSchedule(scheduleId: string): Promise<{ message: string }> {
    return this.post(`/schedules/${scheduleId}/confirm`);
  }

  async skipSchedule(scheduleId: string, reason: string): Promise<{ message: string }> {
    return this.post(`/schedules/${scheduleId}/skip`, { reason });
  }

  async createBulkSchedules(transactionId: string, bulkScheduleData: {
    line_item_ids: string[];
    amortization_start_date: string;
    number_of_periods: number;
    amortization_account_code: string;
    expense_account_code: string;
    notes?: string;
  }): Promise<{
    message: string;
    created_schedules: Array<{
      schedule_id: string;
      line_item_id: string;
      status: string;
    }>;
    total_created: number;
  }> {
    return this.post(`/transactions/${transactionId}/schedules/bulk-create`, bulkScheduleData);
  }

  // Reports Methods
  async getAmortizationReport(filters: {
    client_id: string;
    entity_id?: string;
    year?: number;
    month?: number;
  }): Promise<any> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });
    return this.get(`/reports/amortization?${params.toString()}`);
  }

  // Contact Methods
  async getContacts(entityId: string, filters?: {
    page?: number;
    limit?: number;
    sort_by?: string;
    sort_order?: string;
    name_filter?: string;
    contact_type?: string;
    email_filter?: string;
    source_system?: string;
    is_active?: boolean;
    has_amortization_settings?: boolean;
  }): Promise<any> {
    const params = new URLSearchParams();
    params.append('entity_id', entityId);
    
    if (filters?.page) params.append('page', filters.page.toString());
    if (filters?.limit) params.append('limit', filters.limit.toString());
    if (filters?.sort_by) params.append('sort_by', filters.sort_by);
    if (filters?.sort_order) params.append('sort_order', filters.sort_order);
    if (filters?.name_filter) params.append('name_filter', filters.name_filter);
    if (filters?.contact_type) params.append('contact_type', filters.contact_type);
    if (filters?.email_filter) params.append('email_filter', filters.email_filter);
    if (filters?.source_system) params.append('source_system', filters.source_system);
    if (filters?.is_active !== undefined) params.append('is_active', filters.is_active.toString());
    if (filters?.has_amortization_settings !== undefined) params.append('has_amortization_settings', filters.has_amortization_settings.toString());

    const queryString = params.toString();
    return this.get(`/contacts${queryString ? `?${queryString}` : ''}`, {
      cache: false
    } as ExtendedAxiosRequestConfig);
  }

  // Audit Log Methods
  async getAuditLogs(entityId: string, filters?: {
    page?: number;
    limit?: number;
    sort_by?: string;
    sort_order?: string;
    event_category?: string;
    event_type?: string;
    status?: string;
    date_from?: string;
    date_to?: string;
    user_id?: string;
    source_ip?: string;
    error_only?: boolean;
    has_details?: boolean;
    search_text?: string;
    min_duration_ms?: number;
    max_duration_ms?: number;
  }): Promise<any> {
    const params = new URLSearchParams();
    params.append('entity_id', entityId);
    
    if (filters?.page) params.append('page', filters.page.toString());
    if (filters?.limit) params.append('limit', filters.limit.toString());
    if (filters?.sort_by) params.append('sort_by', filters.sort_by);
    if (filters?.sort_order) params.append('sort_order', filters.sort_order);
    if (filters?.event_category) params.append('event_category', filters.event_category);
    if (filters?.event_type) params.append('event_type', filters.event_type);
    if (filters?.status) params.append('status', filters.status);
    if (filters?.date_from) params.append('date_from', filters.date_from);
    if (filters?.date_to) params.append('date_to', filters.date_to);
    if (filters?.user_id) params.append('user_id', filters.user_id);
    if (filters?.source_ip) params.append('source_ip', filters.source_ip);
    if (filters?.error_only !== undefined) params.append('error_only', filters.error_only.toString());
    if (filters?.has_details !== undefined) params.append('has_details', filters.has_details.toString());
    if (filters?.search_text) params.append('search_text', filters.search_text);
    if (filters?.min_duration_ms) params.append('min_duration_ms', filters.min_duration_ms.toString());
    if (filters?.max_duration_ms) params.append('max_duration_ms', filters.max_duration_ms.toString());

    const queryString = params.toString();
    return this.get(`/audit${queryString ? `?${queryString}` : ''}`, {
      cache: false
    } as ExtendedAxiosRequestConfig);
  }

  async getAttachmentBlob(attachmentId: string): Promise<string> {
    try {
      const token = await this.getAuthToken();
      const response = await fetch(`${this.client.defaults.baseURL}/attachments/${attachmentId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch attachment: ${response.status}`);
      }
      
      // Get the content type from the response
      const contentType = response.headers.get('content-type') || 'application/octet-stream';
      console.log('Attachment content type:', contentType);
      
      const blob = await response.blob();
      
      // Create blob with explicit type to help browser handle it correctly
      const typedBlob = new Blob([blob], { type: contentType });
      const blobUrl = URL.createObjectURL(typedBlob);
      
      console.log('Created blob URL:', blobUrl);
      return blobUrl;
    } catch (error) {
      console.error('Error fetching attachment:', error);
      throw error;
    }
  }
}

export const api = new ApiClient();
