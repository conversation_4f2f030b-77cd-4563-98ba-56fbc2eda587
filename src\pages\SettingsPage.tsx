import { SidebarLayout } from '../components/layout/SidebarLayout';
import { CacheClearButton } from '../components/ui/cache-clear-button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Separator } from '../components/ui/separator';

export function SettingsPage() {
  return (
    <SidebarLayout title="Settings">
      <div className="p-8 space-y-6">
        <div>
          <h1 className="text-2xl font-bold mb-2">Settings</h1>
          <p className="text-muted-foreground">
            Application settings and system parameters management.
          </p>
        </div>

        <Separator />

        {/* Cache Management Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span>Cache Management</span>
            </CardTitle>
            <CardDescription>
              Clearing cache and cookies can help resolve data display issues, performance problems, or authentication issues.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                If you experience data display issues, authentication problems, or the application works incorrectly, 
                try clearing the cache and/or cookies. This will remove all saved data and reload it fresh.
              </p>
              <div className="flex items-center gap-4">
                <CacheClearButton 
                  variant="destructive" 
                  size="sm"
                  type="cache"
                  onClear={() => {
                    console.log('Cache cleared by user');
                  }}
                />
                <CacheClearButton 
                  variant="outline" 
                  size="sm"
                  type="cookies"
                  onClear={() => {
                    console.log('Cookies cleared by user');
                  }}
                />
                <CacheClearButton 
                  variant="secondary" 
                  size="sm"
                  type="both"
                  onClear={() => {
                    console.log('Cache and cookies cleared by user');
                  }}
                />
              </div>
              <div className="flex items-center gap-4 mt-2">
                <span className="text-xs text-muted-foreground">
                  ⚠️ Page will be reloaded after clearing
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Additional Settings Sections */}
        <Card>
          <CardHeader>
            <CardTitle>Additional Settings</CardTitle>
            <CardDescription>
              Other application parameters will be added here.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Additional application settings will be available here in the future.
            </p>
          </CardContent>
        </Card>
      </div>
    </SidebarLayout>
  );
}
