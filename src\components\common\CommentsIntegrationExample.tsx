/**
 * Example Integration: Comments Panel with Bills for Amortization
 * 
 * This file demonstrates how to integrate the CommentsPanel component
 * into existing DRCR pages like the Bills for Amortization page.
 */

import React, { useState } from 'react';
import { CommentsPanel, useComments, ParentType } from '@/components/common';
import { Button } from '@/components/ui/button';
import { MessageSquare, X } from 'lucide-react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Sheet<PERSON>eader, Sheet<PERSON><PERSON><PERSON>, SheetTrigger } from '@/components/ui/sheet';
import { Badge } from '@/components/ui/badge';

// Example: Integration as a side panel in Bills for Amortization
interface BillsCommentsIntegrationProps {
  scheduleId: string;
  transactionId?: string;
  entityId?: string;
}

export const BillsCommentsIntegration: React.FC<BillsCommentsIntegrationProps> = ({
  scheduleId,
  transactionId,
  entityId
}) => {
  const [commentsOpen, setCommentsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'schedule' | 'transaction' | 'entity'>('schedule');
  
  // Use the comments hook to get comment counts for badges
  const scheduleComments = useComments({
    parentType: ParentType.SCHEDULE,
    parentId: scheduleId,
    autoLoad: true
  });
  
  const transactionComments = useComments({
    parentType: ParentType.TRANSACTION,
    parentId: transactionId || '',
    autoLoad: !!transactionId
  });
  
  const entityComments = useComments({
    parentType: ParentType.ENTITY,
    parentId: entityId || '',
    autoLoad: !!entityId
  });
  
  const getCurrentComments = () => {
    switch (activeTab) {
      case 'schedule':
        return scheduleComments;
      case 'transaction':
        return transactionComments;
      case 'entity':
        return entityComments;
      default:
        return scheduleComments;
    }
  };
  
  const getCurrentParentType = (): ParentType => {
    switch (activeTab) {
      case 'schedule':
        return ParentType.SCHEDULE;
      case 'transaction':
        return ParentType.TRANSACTION;
      case 'entity':
        return ParentType.ENTITY;
      default:
        return ParentType.SCHEDULE;
    }
  };
  
  const getCurrentParentId = () => {
    switch (activeTab) {
      case 'schedule':
        return scheduleId;
      case 'transaction':
        return transactionId || '';
      case 'entity':
        return entityId || '';
      default:
        return scheduleId;
    }
  };
  
  return (
    <div className="relative">
      {/* Comments Toggle Button */}
      <Sheet open={commentsOpen} onOpenChange={setCommentsOpen}>
        <SheetTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="gap-2"
            onClick={() => setCommentsOpen(true)}
          >
            <MessageSquare className="h-4 w-4" />
            Comments
            {scheduleComments.totalComments > 0 && (
              <Badge variant="secondary" className="ml-1">
                {scheduleComments.totalComments}
              </Badge>
            )}
          </Button>
        </SheetTrigger>
        
        <SheetContent side="right" className="w-[500px] p-0">
          <div className="flex flex-col h-full">
            <SheetHeader className="p-4 border-b">
              <SheetTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Comments
              </SheetTitle>
              
              {/* Tab Navigation */}
              <div className="flex gap-1 mt-3">
                <Button
                  variant={activeTab === 'schedule' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setActiveTab('schedule')}
                  className="gap-1"
                >
                  Schedule
                  {scheduleComments.totalComments > 0 && (
                    <Badge variant="secondary" className="text-xs">
                      {scheduleComments.totalComments}
                    </Badge>
                  )}
                </Button>
                
                {transactionId && (
                  <Button
                    variant={activeTab === 'transaction' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setActiveTab('transaction')}
                    className="gap-1"
                  >
                    Transaction
                    {transactionComments.totalComments > 0 && (
                      <Badge variant="secondary" className="text-xs">
                        {transactionComments.totalComments}
                      </Badge>
                    )}
                  </Button>
                )}
                
                {entityId && (
                  <Button
                    variant={activeTab === 'entity' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setActiveTab('entity')}
                    className="gap-1"
                  >
                    Entity
                    {entityComments.totalComments > 0 && (
                      <Badge variant="secondary" className="text-xs">
                        {entityComments.totalComments}
                      </Badge>
                    )}
                  </Button>
                )}
              </div>
            </SheetHeader>
            
            {/* Comments Panel */}
            <div className="flex-1 p-4">
              <CommentsPanel
                parentType={getCurrentParentType()}
                parentId={getCurrentParentId()}
                height="100%"
                enableMentions={true}
                className="border-none shadow-none"
              />
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
};

// Example: Inline comments integration
interface InlineCommentsProps {
  parentType: ParentType;
  parentId: string;
  title?: string;
}

export const InlineComments: React.FC<InlineCommentsProps> = ({
  parentType,
  parentId,
  title = 'Comments'
}) => {
  const [expanded, setExpanded] = useState(false);
  const { totalComments } = useComments({
    parentType,
    parentId,
    autoLoad: true
  });
  
  return (
    <div className="mt-6 space-y-4">
      {/* Comments Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          <h3 className="text-lg font-semibold">{title}</h3>
          {totalComments > 0 && (
            <Badge variant="secondary">
              {totalComments}
            </Badge>
          )}
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setExpanded(!expanded)}
        >
          {expanded ? 'Hide' : 'Show'} Comments
        </Button>
      </div>
      
      {/* Comments Panel */}
      {expanded && (
        <div className="border rounded-lg">
          <CommentsPanel
            parentType={parentType}
            parentId={parentId}
            height={400}
            enableMentions={true}
          />
        </div>
      )}
    </div>
  );
};

// Example: Comments indicator for table rows
interface CommentsIndicatorProps {
  parentType: ParentType;
  parentId: string;
  onClick?: () => void;
}

export const CommentsIndicator: React.FC<CommentsIndicatorProps> = ({
  parentType,
  parentId,
  onClick
}) => {
  const { totalComments, loading } = useComments({
    parentType,
    parentId,
    autoLoad: true
  });
  
  if (loading || totalComments === 0) {
    return null;
  }
  
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={onClick}
      className="h-6 px-2 gap-1"
    >
      <MessageSquare className="h-3 w-3" />
      <span className="text-xs">{totalComments}</span>
    </Button>
  );
};

// Example: How to integrate with existing Bills for Amortization page
export const BillsForAmortizationWithComments = () => {
  // This would be in your actual Bills for Amortization component
  const scheduleId = "example-schedule-id";
  const transactionId = "example-transaction-id";
  const entityId = "example-entity-id";
  
  return (
    <div className="space-y-6">
      {/* Your existing Bills for Amortization content */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Bills for Amortization</h1>
        
        {/* Add comments integration to the header */}
        <div className="flex items-center gap-2">
          {/* Your existing toolbar buttons */}
          
          {/* Comments integration */}
          <BillsCommentsIntegration
            scheduleId={scheduleId}
            transactionId={transactionId}
            entityId={entityId}
          />
        </div>
      </div>
      
      {/* Your existing table or content */}
      <div className="space-y-4">
        {/* Schedule rows can include comment indicators */}
        <div className="flex items-center justify-between p-4 border rounded">
          <div>Schedule Item Details</div>
          <div className="flex items-center gap-2">
            <CommentsIndicator
              parentType={ParentType.SCHEDULE}
              parentId={scheduleId}
              onClick={() => console.log('Open comments for schedule')}
            />
            {/* Other action buttons */}
          </div>
        </div>
      </div>
      
      {/* Optional: Inline comments section */}
      <InlineComments
        parentType={ParentType.SCHEDULE}
        parentId={scheduleId}
        title="Schedule Comments"
      />
    </div>
  );
};

export default {
  BillsCommentsIntegration,
  InlineComments,
  CommentsIndicator,
  BillsForAmortizationWithComments
};