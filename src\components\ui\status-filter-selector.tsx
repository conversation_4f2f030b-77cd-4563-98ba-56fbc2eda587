import React, { useState } from 'react';
import { Settings2, ChevronDown } from 'lucide-react';
import { Button } from './button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuCheckboxItem } from './dropdown-menu';
import { cn } from '@/lib/utils';
import { DEFAULT_STATUS_OPTIONS, ACTIONABLE_STATUSES, STANDARD_WORKFLOW_STATUSES } from '@/constants/status-filters';

interface StatusOption {
  value: string;
  label: string;
  count?: number;
}

interface StatusFilterSelectorProps {
  selectedStatusFilters: string[];
  onStatusFiltersChange: (filters: string[]) => void;
  statusOptions?: StatusOption[];
  isLoading?: boolean;
  disabled?: boolean;
  className?: string;
}

// Use default status options from constants

export function StatusFilterSelector({
  selectedStatusFilters,
  onStatusFiltersChange,
  statusOptions = DEFAULT_STATUS_OPTIONS,
  isLoading = false,
  disabled = false,
  className,
}: StatusFilterSelectorProps) {
  const [open, setOpen] = useState(false);

  const handleToggleStatus = (statusValue: string, checked: boolean) => {
    if (checked) {
      onStatusFiltersChange([...selectedStatusFilters.filter(s => s !== statusValue), statusValue]);
    } else {
      onStatusFiltersChange(selectedStatusFilters.filter(s => s !== statusValue));
    }
  };

  const handleSelectAll = () => {
    onStatusFiltersChange(statusOptions.map(option => option.value));
  };

  const handleClearAll = () => {
    onStatusFiltersChange([]);
  };

  const totalCount = statusOptions.reduce((sum, option) => sum + (option.count || 0), 0);

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="outline" 
          className={cn("flex items-center gap-1 h-8 text-xs", className)}
          disabled={disabled || isLoading}
          aria-label="Filter by status"
        >
          <Settings2 className="h-3 w-3" />
          Status ({selectedStatusFilters.length})
          <ChevronDown className="h-3 w-3" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>Filter by Status</span>
          {totalCount > 0 && (
            <span className="text-xs text-muted-foreground">
              Total: {totalCount}
            </span>
          )}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {/* Bulk actions */}
        <div className="flex gap-1 p-1">
          <Button
            variant="ghost"
            size="sm"
            className="h-6 px-2 text-xs flex-1"
            onClick={handleSelectAll}
            disabled={selectedStatusFilters.length === statusOptions.length}
          >
            Select All
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 px-2 text-xs flex-1"
            onClick={handleClearAll}
            disabled={selectedStatusFilters.length === 0}
          >
            Clear All
          </Button>
        </div>
        
        <DropdownMenuSeparator />
        
        {/* All Statuses option */}
        <DropdownMenuCheckboxItem
          checked={selectedStatusFilters.length === 0}
          onCheckedChange={(checked) => {
            if (checked) {
              handleClearAll();
            }
          }}
        >
          <span className="font-medium">All Statuses</span>
          {totalCount > 0 && (
            <span className="ml-auto text-xs text-muted-foreground">
              ({totalCount})
            </span>
          )}
        </DropdownMenuCheckboxItem>
        
        <DropdownMenuSeparator />
        
        {/* Actionable Items Group */}
        <div className="px-2 py-1">
          <div className="text-xs font-medium text-muted-foreground mb-1">Actionable</div>
          {statusOptions
            .filter(option => ACTIONABLE_STATUSES.includes(option.value as any))
            .map((option) => (
              <DropdownMenuCheckboxItem
                key={option.value}
                checked={selectedStatusFilters.includes(option.value)}
                onCheckedChange={(checked) => handleToggleStatus(option.value, checked)}
                className="pl-4"
              >
                {option.label}
                {option.count !== undefined && (
                  <span className="ml-auto text-xs text-muted-foreground">
                    ({option.count})
                  </span>
                )}
              </DropdownMenuCheckboxItem>
            ))}
        </div>
        
        <DropdownMenuSeparator />
        
        {/* Completed Items Group */}
        <div className="px-2 py-1">
          <div className="text-xs font-medium text-muted-foreground mb-1">Completed</div>
          {statusOptions
            .filter(option => ['confirmed', 'posted', 'partially_posted'].includes(option.value))
            .map((option) => (
              <DropdownMenuCheckboxItem
                key={option.value}
                checked={selectedStatusFilters.includes(option.value)}
                onCheckedChange={(checked) => handleToggleStatus(option.value, checked)}
                className="pl-4"
              >
                {option.label}
                {option.count !== undefined && (
                  <span className="ml-auto text-xs text-muted-foreground">
                    ({option.count})
                  </span>
                )}
              </DropdownMenuCheckboxItem>
            ))}
        </div>
        
        <DropdownMenuSeparator />
        
        {/* Other Items Group */}
        <div className="px-2 py-1">
          <div className="text-xs font-medium text-muted-foreground mb-1">Other</div>
          {statusOptions
            .filter(option => !STANDARD_WORKFLOW_STATUSES.includes(option.value as any))
            .map((option) => (
              <DropdownMenuCheckboxItem
                key={option.value}
                checked={selectedStatusFilters.includes(option.value)}
                onCheckedChange={(checked) => handleToggleStatus(option.value, checked)}
                className="pl-4"
              >
                {option.label}
                {option.count !== undefined && (
                  <span className="ml-auto text-xs text-muted-foreground">
                    ({option.count})
                  </span>
                )}
              </DropdownMenuCheckboxItem>
            ))}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}