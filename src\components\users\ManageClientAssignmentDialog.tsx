import React, { useState, useEffect } from 'react';
import { Check, Search, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

import { UsersService, type User } from '../../services/users.service';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '../ui/dialog';
import { Label } from '../ui/label';
import { Checkbox } from '../ui/checkbox';
import { Badge } from '../ui/badge';
import { ScrollArea } from '../ui/scroll-area';

interface Client {
  client_id: string;
  name: string;
  status: string;
}

interface ManageClientAssignmentDialogProps {
  user: User;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAssignmentUpdated: () => void;
}

export const ManageClientAssignmentDialog: React.FC<ManageClientAssignmentDialogProps> = ({
  user,
  open,
  onOpenChange,
  onAssignmentUpdated,
}) => {
  const [availableClients, setAvailableClients] = useState<Client[]>([]);
  const [selectedClientIds, setSelectedClientIds] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingClients, setIsLoadingClients] = useState(false);

  // Reset state when dialog opens/closes
  useEffect(() => {
    if (open) {
      setSelectedClientIds([...user.assigned_client_ids]);
      setSearchTerm('');
      fetchClients();
    } else {
      setSelectedClientIds([]);
      setSearchTerm('');
      setAvailableClients([]);
    }
  }, [open, user.assigned_client_ids]);

  const fetchClients = async () => {
    try {
      setIsLoadingClients(true);
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081'}/clients/summary`, {
        headers: {
          'Authorization': `Bearer ${await import('../../services/auth.service').then(m => m.AuthService.getCurrentUserToken())}`,
          'Content-Type': 'application/json',
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setAvailableClients(data.clients.map((c: any) => ({
          client_id: c.client_id,
          name: c.name,
          status: c.status || 'active'
        })));
      }
    } catch (error) {
      console.error('Error fetching clients:', error);
      toast.error('Failed to load clients');
    } finally {
      setIsLoadingClients(false);
    }
  };

  const handleClientToggle = (clientId: string) => {
    setSelectedClientIds(prev => 
      prev.includes(clientId) 
        ? prev.filter(id => id !== clientId)
        : [...prev, clientId]
    );
  };

  const handleSelectAll = () => {
    const filteredClientIds = filteredClients.map(c => c.client_id);
    setSelectedClientIds(filteredClientIds);
  };

  const handleSelectNone = () => {
    setSelectedClientIds([]);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setIsLoading(true);
    try {
      await UsersService.updateUserClientAssignments(
        user.user_id,
        selectedClientIds,
        user.role
      );

      toast.success('Client assignments updated successfully');
      onOpenChange(false);
      onAssignmentUpdated();
    } catch (error: any) {
      toast.error(error.message || 'Failed to update client assignments');
    } finally {
      setIsLoading(false);
    }
  };

  const filteredClients = availableClients.filter(client =>
    client.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const hasChanges = JSON.stringify(selectedClientIds.sort()) !== JSON.stringify(user.assigned_client_ids.sort());

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Manage Client Assignments</DialogTitle>
          <DialogDescription>
            Assign clients to {user.display_name || user.email}. 
            {user.role === 'firm_admin' && ' Note: Admins have access to all clients automatically.'}
          </DialogDescription>
        </DialogHeader>

        {/* Show warning for admins */}
        {user.role === 'firm_admin' && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
            <div className="flex items-center">
              <Badge variant="outline" className="text-blue-600 border-blue-600 mr-2">
                Admin
              </Badge>
              <span className="text-sm text-blue-700">
                This user has admin privileges and can access all clients regardless of assignments.
              </span>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Search */}
          <div className="space-y-2">
            <Label htmlFor="search">Search Clients</Label>
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                id="search"
                placeholder="Search clients..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>

          {/* Client List */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>Available Clients ({filteredClients.length})</Label>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAll}
                  disabled={filteredClients.length === 0}
                >
                  Select All
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleSelectNone}
                  disabled={selectedClientIds.length === 0}
                >
                  Select None
                </Button>
              </div>
            </div>

            {isLoadingClients ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="ml-2 text-sm text-muted-foreground">Loading clients...</span>
              </div>
            ) : filteredClients.length > 0 ? (
              <ScrollArea className="h-64 border rounded-md">
                <div className="p-2 space-y-2">
                  {filteredClients.map((client) => (
                    <div key={client.client_id} className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded">
                      <Checkbox
                        id={`client-${client.client_id}`}
                        checked={selectedClientIds.includes(client.client_id)}
                        onCheckedChange={() => handleClientToggle(client.client_id)}
                      />
                      <Label 
                        htmlFor={`client-${client.client_id}`}
                        className="flex-1 font-normal cursor-pointer"
                      >
                        <div className="flex items-center justify-between">
                          <span>{client.name}</span>
                          {client.status !== 'active' && (
                            <Badge variant="secondary" className="text-xs">
                              {client.status}
                            </Badge>
                          )}
                        </div>
                      </Label>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                {searchTerm ? 'No clients found matching your search.' : 'No clients available.'}
              </div>
            )}
          </div>

          {/* Selected Count */}
          <div className="text-sm text-muted-foreground">
            {selectedClientIds.length} client{selectedClientIds.length !== 1 ? 's' : ''} selected
          </div>
          
          {/* Actions */}
          <div className="flex justify-end space-x-2">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isLoading || !hasChanges}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <Check className="w-4 h-4 mr-2" />
                  Update Assignments
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};