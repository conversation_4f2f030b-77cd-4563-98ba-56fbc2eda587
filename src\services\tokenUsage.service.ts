export interface ClientTokenUsageResponse {
  client_id: string;
  total_openai_tokens: number;
  total_mistral_tokens: number;
  total_cost: number;
  total_operations: number;
  sync_jobs_count: number;
  start_date?: string;
  end_date?: string;
  sync_jobs: Array<{
    sync_job_id: string;
    entity_id?: string;
    status?: string;
    openai_tokens: number;
    mistral_tokens: number;
    total_cost: number;
    operations_count: number;
    started_at?: string;
    completed_at?: string;
    // Credit usage tracking
    credit_usage?: {
      credits_used: number;
      credits_used_openai: number;
      credits_used_mistral: number;
    };
  }>;
}

import { api } from '@/lib/api';

export class TokenUsageService {
  static async getClientTokenUsage(clientId: string, params?: { startDate?: string; endDate?: string; limit?: number }): Promise<ClientTokenUsageResponse> {
    const query = new URLSearchParams();
    if (params?.startDate) query.append('start_date', params.startDate);
    if (params?.endDate) query.append('end_date', params.endDate);
    if (params?.limit) query.append('limit', params.limit.toString());

    const endpoint = `/token-usage/client/${clientId}/tokens${query.toString() ? '?' + query.toString() : ''}`;
    return api.get<ClientTokenUsageResponse>(endpoint);
  }
} 