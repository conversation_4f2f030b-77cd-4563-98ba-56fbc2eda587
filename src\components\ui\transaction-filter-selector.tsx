import React, { useState, useMemo } from 'react';
import { Filter, ChevronDown } from 'lucide-react';
import { Button } from './button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuCheckboxItem } from './dropdown-menu';
import { cn } from '@/lib/utils';
import { 
  RECOMMENDATION_FILTER_OPTIONS, 
  CONFIDENCE_FILTER_OPTIONS, 
  type TransactionFilterOption
} from '@/constants/transaction-filters';

interface TransactionFilterSelectorProps {
  selectedFilters: string[];
  onFiltersChange: (filters: string[]) => void;
  filterOptions?: {
    recommendations?: TransactionFilterOption[];
    confidence?: TransactionFilterOption[];
  };
  transactionCounts?: Record<string, number>;
  isLoading?: boolean;
  disabled?: boolean;
  className?: string;
}

export function TransactionFilterSelector({
  selectedFilters,
  onFiltersChange,
  filterOptions = {
    recommendations: RECOMMENDATION_FILTER_OPTIONS,
    confidence: CONFIDENCE_FILTER_OPTIONS
  },
  transactionCounts = {},
  isLoading = false,
  disabled = false,
  className,
}: TransactionFilterSelectorProps) {
  const [open, setOpen] = useState(false);

  // Combine all filter options
  const allOptions = useMemo(() => [
    ...(filterOptions.recommendations || []),
    ...(filterOptions.confidence || [])
  ], [filterOptions]);

  const handleToggleFilter = (filterValue: string, checked: boolean) => {
    if (checked) {
      onFiltersChange([...selectedFilters.filter(f => f !== filterValue), filterValue]);
    } else {
      onFiltersChange(selectedFilters.filter(f => f !== filterValue));
    }
  };

  const handleSelectAll = () => {
    onFiltersChange(allOptions.map(option => option.value));
  };

  const handleClearAll = () => {
    onFiltersChange([]);
  };

  const getFilterGroup = (option: TransactionFilterOption) => {
    if (filterOptions.recommendations?.find(r => r.value === option.value)) {
      return 'recommendations';
    }
    if (filterOptions.confidence?.find(c => c.value === option.value)) {
      return 'confidence';
    }
    return 'other';
  };

  const groupedOptions = useMemo(() => {
    const groups: Record<string, TransactionFilterOption[]> = {
      recommendations: [],
      confidence: []
    };

    allOptions.forEach(option => {
      const group = getFilterGroup(option);
      if (groups[group]) {
        groups[group].push(option);
      }
    });

    return groups;
  }, [allOptions, filterOptions]);


  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="outline" 
          className={cn("flex items-center gap-1 h-8 text-xs", className)}
          disabled={disabled || isLoading}
          aria-label="Filter transactions"
        >
          <Filter className="h-3 w-3" />
          Filters ({selectedFilters.length})
          <ChevronDown className="h-3 w-3" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80 max-h-[400px] overflow-y-auto">
        <DropdownMenuLabel>
          <span>Filter Transactions</span>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {/* Bulk actions */}
        <div className="flex gap-1 p-1">
          <Button
            variant="ghost"
            size="sm"
            className="h-6 px-2 text-xs flex-1"
            onClick={handleSelectAll}
            disabled={selectedFilters.length === allOptions.length}
          >
            Select All
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 px-2 text-xs flex-1"
            onClick={handleClearAll}
            disabled={selectedFilters.length === 0}
          >
            Clear All
          </Button>
        </div>
        
        <DropdownMenuSeparator />
        
        {/* All Transactions option */}
        <DropdownMenuCheckboxItem
          checked={selectedFilters.length === 0}
          onCheckedChange={(checked) => {
            if (checked) {
              handleClearAll();
            }
          }}
        >
          <span className="font-medium">All Transactions</span>
        </DropdownMenuCheckboxItem>
        
        <DropdownMenuSeparator />
        
        {/* Recommendations Group */}
        {groupedOptions.recommendations.length > 0 && (
          <>
            <div className="px-2 py-1">
              <div className="text-xs font-medium text-muted-foreground mb-1">Recommendations</div>
              {groupedOptions.recommendations.map((option) => (
                <DropdownMenuCheckboxItem
                  key={option.value}
                  checked={selectedFilters.includes(option.value)}
                  onCheckedChange={(checked) => handleToggleFilter(option.value, checked)}
                  className="pl-6 pr-4 py-1.5"
                >
                  <div className="flex items-center justify-between w-full">
                    <span className="text-sm">{option.label}</span>
                    {transactionCounts[option.value] !== undefined && (
                      <span className="text-xs text-muted-foreground">
                        ({transactionCounts[option.value]})
                      </span>
                    )}
                  </div>
                </DropdownMenuCheckboxItem>
              ))}
            </div>
            <DropdownMenuSeparator />
          </>
        )}
        
        {/* Confidence Group */}
        {groupedOptions.confidence.length > 0 && (
          <>
            <div className="px-2 py-1">
              <div className="text-xs font-medium text-muted-foreground mb-1">Confidence Level</div>
              {groupedOptions.confidence.map((option) => (
                <DropdownMenuCheckboxItem
                  key={option.value}
                  checked={selectedFilters.includes(option.value)}
                  onCheckedChange={(checked) => handleToggleFilter(option.value, checked)}
                  className="pl-6 pr-4 py-1.5"
                >
                  <div className="flex items-center justify-between w-full">
                    <span className="text-sm">{option.label}</span>
                    {transactionCounts[option.value] !== undefined && (
                      <span className="text-xs text-muted-foreground">
                        ({transactionCounts[option.value]})
                      </span>
                    )}
                  </div>
                </DropdownMenuCheckboxItem>
              ))}
            </div>
            <DropdownMenuSeparator />
          </>
        )}
        
      </DropdownMenuContent>
    </DropdownMenu>
  );
}