import * as React from "react"
import { useNavigate, useLocation } from "react-router-dom"
import {
  Calculator,
  Settings,
  LogOut,
} from "lucide-react"

import { useAuthStore } from "../../store/auth.store"
import { NavMain } from "./NavMain"
import { NavUser } from "./NavUser"
import { toast } from "sonner"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  useSidebar,
} from "../ui/sidebar"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip"

// Simplified navigation structure for DRCR
const navMainItems = [
  {
    title: "ACCPAY",
    url: "/accpay",
    icon: Calculator,
    items: [
      {
        title: "Prepayments",
        url: "/accpay/prepayments",
      },
      {
        title: "All",
        url: "/accpay/all",
        disabled: false,
      },
    ],
  },
  {
    title: "Settings",
    url: "/settings",
    icon: Settings,
    items: [
      {
        title: "General",
        url: "/settings/general",
        disabled: true,
      },
      {
        title: "Users",
        url: "/settings/users",
      },
      {
        title: "Billing",
        url: "/billing",
      },
      {
        title: "Integrations",
        url: "/settings/integrations",
        disabled: true,
      },
    ],
  },
]

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const navigate = useNavigate()
  const location = useLocation()
  const { user, signOut } = useAuthStore()
  const { state, toggleSidebar } = useSidebar()
  const [isLoading, setIsLoading] = React.useState(true)
  const [isHoverVisible, setIsHoverVisible] = React.useState(false)
  const [isMobile, setIsMobile] = React.useState(false)
  
  // Check if device is mobile
  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768 || 'ontouchstart' in window)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    
    return () => window.removeEventListener('resize', checkMobile)
  }, [])
  
  // Simulate loading state for smoother transitions
  React.useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 150)
    return () => clearTimeout(timer)
  }, [])
  
  // Hover timeout ref
  const hoverTimeoutRef = React.useRef<NodeJS.Timeout | null>(null)
  
  // Debounced hover handlers for smoother interaction
  const handleHoverEnter = React.useCallback(() => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current)
    }
    setIsHoverVisible(true)
  }, [])
  
  const handleHoverLeave = React.useCallback((e?: React.MouseEvent) => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current)
    }
    // Don't hide if moving to dropdown menu
    if (e && e.relatedTarget) {
      const target = e.relatedTarget as Element
      if (target.closest('[data-radix-popper-content-wrapper]')) {
        return
      }
    }
    hoverTimeoutRef.current = setTimeout(() => {
      setIsHoverVisible(false)
    }, 300) // 300ms delay before hiding
  }, [])
  
  // Cleanup timeout on unmount
  React.useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current)
      }
    }
  }, [])

  const handleSignOut = async () => {
    try {
      await signOut()
      toast.success('Logged out successfully')
      navigate('/login')
    } catch (error: any) {
      toast.error(error.message || 'Failed to log out')
    }
  }
  
  // Handle navigation without auto-collapse
  const handleNavigation = (path: string) => {
    navigate(path)
    // Don't auto-collapse sidebar on navigation for better UX
    // User can manually collapse if needed
  }

  // Get user initials for avatar
  const getInitials = () => {
    if (user?.displayName) {
      return user.displayName
        .split(' ')
        .map(name => name[0])
        .join('')
        .toUpperCase()
    }
    return user?.email?.[0].toUpperCase() || 'U'
  }

  // Enhanced collapsed state with hover trigger
  if (state === 'collapsed') {
    return (
      <>
        {/* Hover trigger zone - invisible area on left edge (desktop only) */}
        {!isMobile && (
          <div
            className="fixed left-0 top-0 z-40 h-full w-3 bg-transparent hover:w-5 transition-all duration-200"
            onMouseEnter={handleHoverEnter}
            onMouseLeave={handleHoverLeave}
            aria-hidden="true"
          />
        )}
        
        {/* Backdrop overlay (desktop only) */}
        {!isMobile && (
          <div
            className={`fixed inset-0 z-40 bg-black/20 backdrop-blur-sm transition-opacity duration-300 ${
              isHoverVisible ? 'opacity-100' : 'opacity-0 pointer-events-none'
            }`}
            onClick={() => setIsHoverVisible(false)}
          />
        )}
        
        {/* Overlay sidebar that appears on hover (desktop only) */}
        {!isMobile && (
          <div
            className={`fixed left-0 top-0 z-50 h-full w-64 transform transition-all duration-300 ease-out ${
              isHoverVisible ? 'translate-x-0 shadow-2xl' : '-translate-x-full shadow-none'
            }`}
            onMouseEnter={handleHoverEnter}
            onMouseLeave={handleHoverLeave}
            style={{ zIndex: 1000 }}
          >
          <div className="h-full bg-sidebar border-r border-sidebar-border flex flex-col">
            <SidebarHeader className="border-b border-sidebar-border p-4 flex-shrink-0">
              {/* Logo - Clickable to navigate to dashboard */}
              <div className="flex justify-center items-center">
                <button
                  onClick={() => handleNavigation('/dashboard')}
                  className="h-12 w-12 rounded-lg hover:bg-sidebar-accent transition-all duration-200 flex items-center justify-center group hover:scale-105 active:scale-95"
                  title="Go to Dashboard"
                >
                  {isLoading ? (
                    <div className="h-10 w-10 rounded animate-pulse bg-sidebar-accent/20" />
                  ) : (
                    <img
                      src="/logo.png"
                      alt="DRCR Logo"
                      className="h-10 w-10 object-contain transition-transform duration-200 group-hover:scale-110"
                    />
                  )}
                </button>
              </div>
            </SidebarHeader>

            <SidebarContent className="px-3 py-4 flex-1 overflow-y-auto">
              {/* Main Navigation */}
              <div className="space-y-2">
                <NavMain items={navMainItems} />
              </div>
            </SidebarContent>

            <SidebarFooter className="border-t border-sidebar-border p-3 flex-shrink-0 mt-auto">
              {/* User Profile */}
              <NavUser user={{
                name: user?.displayName || 'User',
                email: user?.email || '<EMAIL>',
                avatar: '', // No avatar for now
              }} />
            </SidebarFooter>
          </div>
          </div>
        )}
      </>
    )
  }

  return (
    <Sidebar 
      collapsible="none" 
      className="shadow-lg md:shadow-none border-r border-sidebar-border" 
      {...props}
    >
      <SidebarHeader className="border-b border-sidebar-border p-4">
        {/* Logo - Clickable to navigate to dashboard */}
        <div className="flex justify-center items-center">
          <button
            onClick={() => handleNavigation('/dashboard')}
            className="h-12 w-12 rounded-lg hover:bg-sidebar-accent transition-all duration-200 flex items-center justify-center group hover:scale-105 active:scale-95"
            title="Go to Dashboard"
          >
            {isLoading ? (
              <div className="h-10 w-10 rounded animate-pulse bg-sidebar-accent/20" />
            ) : (
              <img
                src="/logo.png"
                alt="DRCR Logo"
                className="h-10 w-10 object-contain transition-transform duration-200 group-hover:scale-110"
              />
            )}
          </button>
        </div>
      </SidebarHeader>

      <SidebarContent className="px-3 py-4 flex-1 overflow-y-auto">
        {/* Main Navigation */}
        <div className="space-y-2">
          <NavMain items={navMainItems} />
        </div>
      </SidebarContent>

      <SidebarFooter className="border-t border-sidebar-border p-3 flex-shrink-0">
        {/* User Profile */}
        <NavUser user={{
          name: user?.displayName || 'User',
          email: user?.email || '<EMAIL>',
          avatar: '', // No avatar for now
        }} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
