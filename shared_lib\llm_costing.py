"""
LLM Token Usage and Cost Calculation Utility

Provides configurable pricing and cost calculation for LLM API usage.
Supports multiple providers (OpenAI, Mistral) with flexible pricing models.
"""

import os
import logging
from typing import Dict, Optional, Tuple, Any
from datetime import datetime, timezone
from dataclasses import dataclass, asdict
from google.cloud import firestore

logger = logging.getLogger(__name__)

@dataclass
class TokenUsage:
    """Token usage data structure"""
    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0
    
    def __add__(self, other: 'TokenUsage') -> 'TokenUsage':
        """Add two TokenUsage objects together"""
        return TokenUsage(
            prompt_tokens=self.prompt_tokens + other.prompt_tokens,
            completion_tokens=self.completion_tokens + other.completion_tokens,
            total_tokens=self.total_tokens + other.total_tokens
        )

@dataclass
class UsageCost:
    """Cost calculation result"""
    provider: str
    model: str
    token_usage: TokenUsage
    prompt_cost: float
    completion_cost: float
    total_cost: float
    timestamp: str
    pricing_missing: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage"""
        return {
            **asdict(self),
            'token_usage': asdict(self.token_usage)
        }

class LLMCostCalculator:
    """
    Configurable LLM cost calculator with support for multiple providers
    """
    
    # Model name aliases to normalize to pricing table names
    MODEL_ALIASES = {
        # OpenAI aliases
        "gpt-4o-mini": "gpt-4.1-mini",
        "gpt-4-turbo": "gpt-4o",
        "gpt-4": "gpt-4o",
        
        # Mistral aliases
        "mistral-ocr": "mistral-large-latest",
        "mistral-latest": "mistral-large-latest",
        "mistral-large": "mistral-large-latest"
    }
    
    def __init__(self, db: Optional[firestore.AsyncClient] = None):
        self.db = db
        self._pricing_cache = {}
        self._cache_timestamp = None
        
    def normalize_model_name(self, model: str) -> str:
        """Normalize model name to match pricing configuration"""
        return self.MODEL_ALIASES.get(model, model)
        
    async def get_pricing_config(self) -> Dict[str, Any]:
        """
        Get pricing configuration from environment or Firestore
        
        Priority:
        1. Environment variables (for development/testing)
        2. Firestore configuration (for production flexibility)
        3. Default fallback pricing
        """
        
        # Check cache first (cache for 5 minutes)
        now = datetime.now(timezone.utc)
        if (self._pricing_cache and self._cache_timestamp and 
            (now - self._cache_timestamp).total_seconds() < 300):
            return self._pricing_cache
        
        # Try environment variables first
        env_pricing = self._get_env_pricing()
        if env_pricing:
            self._pricing_cache = env_pricing
            self._cache_timestamp = now
            logger.debug("Using pricing from environment variables")
            return env_pricing
        
        # Try Firestore configuration
        if self.db:
            try:
                firestore_pricing = await self._get_firestore_pricing()
                if firestore_pricing:
                    self._pricing_cache = firestore_pricing
                    self._cache_timestamp = now
                    logger.debug("Using pricing from Firestore configuration")
                    return firestore_pricing
            except Exception as e:
                logger.warning(f"Failed to load pricing from Firestore: {e}")
        
        # Fallback to default pricing
        default_pricing = self._get_default_pricing()
        self._pricing_cache = default_pricing
        self._cache_timestamp = now
        logger.info("Using default fallback pricing")
        return default_pricing
    
    def _get_env_pricing(self) -> Optional[Dict[str, Any]]:
        """Get pricing from environment variables"""
        try:
            # OpenAI pricing
            openai_gpt4o_prompt = float(os.getenv('OPENAI_GPT4O_PROMPT_COST_PER_1K', '0'))
            openai_gpt4o_completion = float(os.getenv('OPENAI_GPT4O_COMPLETION_COST_PER_1K', '0'))
            openai_gpt41mini_prompt = float(os.getenv('OPENAI_GPT41MINI_PROMPT_COST_PER_1K', '0'))
            openai_gpt41mini_completion = float(os.getenv('OPENAI_GPT41MINI_COMPLETION_COST_PER_1K', '0'))
            
            # Mistral pricing
            mistral_large_prompt = float(os.getenv('MISTRAL_LARGE_PROMPT_COST_PER_1K', '0'))
            mistral_large_completion = float(os.getenv('MISTRAL_LARGE_COMPLETION_COST_PER_1K', '0'))
            
            # Only return if at least one price is configured
            if any([openai_gpt4o_prompt, openai_gpt4o_completion, openai_gpt41mini_prompt, 
                   openai_gpt41mini_completion, mistral_large_prompt, mistral_large_completion]):
                return {
                    'openai': {
                        'gpt-4o': {
                            'prompt_cost_per_1k': openai_gpt4o_prompt,
                            'completion_cost_per_1k': openai_gpt4o_completion
                        },
                        'gpt-4.1-mini': {
                            'prompt_cost_per_1k': openai_gpt41mini_prompt,
                            'completion_cost_per_1k': openai_gpt41mini_completion
                        }
                    },
                    'mistral': {
                        'mistral-large-latest': {
                            'prompt_cost_per_1k': mistral_large_prompt,
                            'completion_cost_per_1k': mistral_large_completion
                        }
                    },
                    'updated_at': datetime.now(timezone.utc).isoformat(),
                    'source': 'environment'
                }
        except (ValueError, TypeError) as e:
            logger.warning(f"Invalid pricing in environment variables: {e}")
        
        return None
    
    async def _get_firestore_pricing(self) -> Optional[Dict[str, Any]]:
        """Get pricing from Firestore configuration"""
        try:
            config_ref = self.db.collection("SYSTEM_CONFIG").document("llm_pricing")
            config_doc = await config_ref.get()
            
            if config_doc.exists:
                config_data = config_doc.to_dict()
                if self._validate_pricing_config(config_data):
                    return config_data
                else:
                    logger.warning("Invalid pricing configuration in Firestore")
        except Exception as e:
            logger.error(f"Error reading pricing from Firestore: {e}")
        
        return None
    
    def _get_default_pricing(self) -> Dict[str, Any]:
        """Get default fallback pricing (current as of July 2025)"""
        return {
            'openai': {
                'gpt-4o': {
                    'prompt_cost_per_1k': 0.0025,      # $2.50 per 1M tokens = $0.0025 per 1K tokens
                    'completion_cost_per_1k': 0.01     # $10.00 per 1M tokens = $0.01 per 1K tokens
                },
                'gpt-4.1-mini': {
                    'prompt_cost_per_1k': 0.00015,     # $0.15 per 1M tokens = $0.00015 per 1K tokens
                    'completion_cost_per_1k': 0.0006   # $0.60 per 1M tokens = $0.0006 per 1K tokens
                }
            },
            'mistral': {
                'mistral-large-latest': {
                    'prompt_cost_per_1k': 0.003,       # $3.00 per 1M tokens = $0.003 per 1K tokens
                    'completion_cost_per_1k': 0.009    # $9.00 per 1M tokens = $0.009 per 1K tokens
                }
            },
            'updated_at': '2025-07-10T00:00:00+00:00',
            'source': 'default'
        }
    
    def _validate_pricing_config(self, config: Dict[str, Any]) -> bool:
        """Validate pricing configuration structure"""
        try:
            # Check basic structure
            if not isinstance(config, dict):
                return False
            
            # Check for required providers
            for provider in ['openai', 'mistral']:
                if provider not in config:
                    continue
                
                provider_config = config[provider]
                if not isinstance(provider_config, dict):
                    return False
                
                # Check model pricing structure
                for model, pricing in provider_config.items():
                    if not isinstance(pricing, dict):
                        return False
                    
                    required_fields = ['prompt_cost_per_1k', 'completion_cost_per_1k']
                    for field in required_fields:
                        if field not in pricing:
                            return False
                        
                        if not isinstance(pricing[field], (int, float)) or pricing[field] < 0:
                            return False
            
            return True
        except Exception:
            return False
    
    async def calculate_cost(
        self, 
        provider: str, 
        model: str, 
        token_usage: TokenUsage,
        operation_type: str = "unknown"
    ) -> UsageCost:
        """
        Calculate cost for token usage
        
        Args:
            provider: LLM provider (openai, mistral)
            model: Model name (gpt-4o, gpt-4.1-mini, etc.)
            token_usage: Token usage data
            operation_type: Type of operation (ocr, analysis, validation)
            
        Returns:
            UsageCost object with detailed cost breakdown
        """
        # Normalize model name to match pricing configuration
        original_model = model
        model = self.normalize_model_name(model)
        
        if original_model != model:
            logger.debug(f"Normalized model name '{original_model}' to '{model}'")
        
        pricing_config = await self.get_pricing_config()
        
        # Get provider pricing
        if provider not in pricing_config:
            logger.warning(f"No pricing configured for provider: {provider}")
            return UsageCost(
                provider=provider,
                model=original_model,  # Keep original model name in result
                token_usage=token_usage,
                prompt_cost=0.0,
                completion_cost=0.0,
                total_cost=0.0,
                timestamp=datetime.now(timezone.utc).isoformat(),
                pricing_missing=True
            )
        
        provider_pricing = pricing_config[provider]
        
        # Get model pricing
        if model not in provider_pricing:
            logger.warning(f"No pricing configured for model: {provider}/{model}")
            return UsageCost(
                provider=provider,
                model=original_model,  # Keep original model name in result
                token_usage=token_usage,
                prompt_cost=0.0,
                completion_cost=0.0,
                total_cost=0.0,
                timestamp=datetime.now(timezone.utc).isoformat(),
                pricing_missing=True
            )
        
        model_pricing = provider_pricing[model]
        
        # Calculate costs
        # Pricing is per 1K tokens, so divide by 1000
        prompt_cost = (token_usage.prompt_tokens / 1000.0) * model_pricing['prompt_cost_per_1k']
        completion_cost = (token_usage.completion_tokens / 1000.0) * model_pricing['completion_cost_per_1k']
        total_cost = prompt_cost + completion_cost
        
        logger.debug(f"Cost calculation for {provider}/{model}: "
                    f"prompt={token_usage.prompt_tokens} tokens (${prompt_cost:.6f}), "
                    f"completion={token_usage.completion_tokens} tokens (${completion_cost:.6f}), "
                    f"total=${total_cost:.6f}")
        
        return UsageCost(
            provider=provider,
            model=model,
            token_usage=token_usage,
            prompt_cost=prompt_cost,
            completion_cost=completion_cost,
            total_cost=total_cost,
            timestamp=datetime.now(timezone.utc).isoformat()
        )
    
    async def update_pricing_config(self, new_pricing: Dict[str, Any]) -> bool:
        """
        Update pricing configuration in Firestore
        
        Args:
            new_pricing: New pricing configuration
            
        Returns:
            True if successful, False otherwise
        """
        if not self.db:
            logger.error("No Firestore client available for updating pricing")
            return False
        
        if not self._validate_pricing_config(new_pricing):
            logger.error("Invalid pricing configuration provided")
            return False
        
        try:
            # Add metadata
            new_pricing['updated_at'] = datetime.now(timezone.utc).isoformat()
            new_pricing['source'] = 'firestore'
            
            config_ref = self.db.collection("SYSTEM_CONFIG").document("llm_pricing")
            await config_ref.set(new_pricing)
            
            # Clear cache to force reload
            self._pricing_cache = {}
            self._cache_timestamp = None
            
            logger.info("Successfully updated LLM pricing configuration")
            return True
        except Exception as e:
            logger.error(f"Failed to update pricing configuration: {e}")
            return False

# Global instance for shared usage
_cost_calculator = None

async def get_cost_calculator(db: Optional[firestore.AsyncClient] = None) -> LLMCostCalculator:
    """Get or create global cost calculator instance"""
    global _cost_calculator
    if _cost_calculator is None:
        _cost_calculator = LLMCostCalculator(db)
    elif db and _cost_calculator.db is None:
        _cost_calculator.db = db
    return _cost_calculator

async def calculate_usage_cost(
    provider: str,
    model: str,
    prompt_tokens: int,
    completion_tokens: int,
    operation_type: str = "unknown",
    db: Optional[firestore.AsyncClient] = None
) -> UsageCost:
    """
    Convenience function to calculate cost for token usage
    
    Args:
        provider: LLM provider (openai, mistral)
        model: Model name
        prompt_tokens: Number of prompt tokens
        completion_tokens: Number of completion tokens
        operation_type: Type of operation
        db: Optional Firestore client
        
    Returns:
        UsageCost object
    """
    calculator = await get_cost_calculator(db)
    token_usage = TokenUsage(
        prompt_tokens=prompt_tokens,
        completion_tokens=completion_tokens,
        total_tokens=prompt_tokens + completion_tokens
    )
    return await calculator.calculate_cost(provider, model, token_usage, operation_type)