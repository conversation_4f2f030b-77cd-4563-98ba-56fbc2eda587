"""
Pydantic Settings for Xero Sync Consumer

Loads configuration from:
1. Environment variables
2. .env files 
3. Google Cloud Secret Manager (if enabled)

Usage:
    from settings import get_settings
    settings = get_settings()
    if settings.ENABLE_TWO_STAGE_SYNC:
        # use two-stage processing
"""

import logging
from typing import Optional, Dict, Any
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load .env file early
load_dotenv()

logger = logging.getLogger(__name__)


class Settings(BaseSettings):
    """Application settings with environment variable loading and validation."""
    
    # Google Cloud Configuration
    GCP_PROJECT_ID: str = Field(..., description="Google Cloud Project ID")
    GCP_REGION: str = Field(default="europe-west2", description="GCP region")
    GCS_BUCKET_NAME: str = Field(..., description="GCS bucket for file storage")
    
    # Secret Management
    LOAD_SECRETS_FROM_SECRET_MANAGER: bool = Field(default=False, description="Load secrets from Secret Manager")
    TOKEN_ENCRYPTION_KEY: Optional[str] = Field(default=None, description="Fernet encryption key for token storage")
    
    # Xero API Configuration
    XERO_CLIENT_ID: Optional[str] = Field(default=None, description="Xero OAuth client ID")
    XERO_CLIENT_SECRET: Optional[str] = Field(default=None, description="Xero OAuth client secret")
    XERO_REDIRECT_URI: str = Field(default="http://localhost:8081/xero/callback", description="Xero OAuth redirect URI")
    XERO_SCOPES: str = Field(default="accounting.transactions accounting.settings offline_access", description="Xero API scopes")
    
    # AI Services
    OPENAI_API_KEY: Optional[str] = Field(default=None, description="OpenAI API key")
    OPENAI_MODEL: str = Field(default="gpt-4.1-mini", description="OpenAI model to use")
    MISTRAL_API_KEY: Optional[str] = Field(default=None, description="Mistral API key")
    
    # PubSub Configuration
    PUBSUB_TOPIC_XERO_SYNC: str = Field(default="xero-sync-topic", description="PubSub topic for Xero sync")
    PUBSUB_SUBSCRIPTION_XERO_SYNC: str = Field(default="xero-sync-subscription", description="PubSub subscription")
    
    # FastAPI/Cloud Run Integration
    FASTAPI_BASE_URL: Optional[str] = Field(default=None, description="FastAPI base URL for two-stage processing")
    SERVICE_ACCOUNT_TOKEN: Optional[str] = Field(default=None, description="Service account token for internal auth")
    
    # Feature Flags
    ENABLE_TWO_STAGE_SYNC: bool = Field(default=True, description="Enable two-stage sync processing")
    ENABLE_ATTACHMENT_PROCESSING: bool = Field(default=True, description="Enable attachment processing")
    ENABLE_PREPAYMENT_ANALYSIS: bool = Field(default=True, description="Enable prepayment analysis")
    ENABLE_JOURNAL_GENERATION: bool = Field(default=False, description="Enable journal generation in Stage 1")
    
    # Performance Tuning
    MAX_CONCURRENT_ATTACHMENTS: int = Field(default=3, description="Max concurrent attachment processing")
    MAX_ATTACHMENT_SIZE_MB: int = Field(default=30, description="Maximum attachment size in MB")
    BATCH_SIZE: int = Field(default=50, description="Batch size for database operations")
    SCANNING_AMOUNT_THRESHOLD: float = Field(default=100.0, description="Default amount threshold for scanning")
    
    # Debugging
    DEBUG: bool = Field(default=False, description="Enable debug logging")
    IS_CLOUD_FUNCTION: str = Field(default="0", description="Running in Cloud Function environment")
    
    @field_validator('ENABLE_TWO_STAGE_SYNC', mode='before')
    @classmethod
    def parse_two_stage_sync(cls, v):
        """Parse two-stage sync setting from various formats."""
        if isinstance(v, str):
            return v.lower() in ('true', '1', 'yes', 'on')
        return bool(v)
    
    @field_validator('DEBUG', mode='before')
    @classmethod
    def parse_debug(cls, v):
        """Parse debug setting from various formats."""
        if isinstance(v, str):
            return v.lower() in ('true', '1', 'yes', 'on')
        return bool(v)
    
    @field_validator('LOAD_SECRETS_FROM_SECRET_MANAGER', mode='before')
    @classmethod
    def parse_secret_manager_flag(cls, v):
        """Parse secret manager flag from various formats."""
        if isinstance(v, str):
            return v.lower() in ('true', '1', 'yes', 'on')
        return bool(v)
    
    def get_entity_settings(self, entity_settings: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Merge global settings with entity-specific overrides.
        
        Args:
            entity_settings: Entity-specific settings from Firestore
            
        Returns:
            Merged settings dictionary with entity overrides taking precedence
        """
        base_settings = {
            "scanning_amount_threshold": self.SCANNING_AMOUNT_THRESHOLD,
            "_system_enableTwoStageSync": self.ENABLE_TWO_STAGE_SYNC,
            "_system_enableAttachmentProcessing": self.ENABLE_ATTACHMENT_PROCESSING,
            "_system_enablePrepaymentAnalysis": self.ENABLE_PREPAYMENT_ANALYSIS,
            "_system_enableJournalGeneration": self.ENABLE_JOURNAL_GENERATION,
            "max_concurrent_attachments": self.MAX_CONCURRENT_ATTACHMENTS,
            "max_attachment_size_mb": self.MAX_ATTACHMENT_SIZE_MB,
            "batch_size": self.BATCH_SIZE,
        }
        
        if entity_settings:
            # Entity settings override global settings
            base_settings.update(entity_settings)
        
        return base_settings
    
    def configure_logging(self):
        """Configure logging based on settings."""
        level = logging.DEBUG if self.DEBUG else logging.INFO
        logging.basicConfig(
            level=level,
            format="%(asctime)s - %(levelname)s - %(name)s - %(message)s"
        )
        
        if self.DEBUG:
            logger.info("Debug logging enabled")
    
    class Config:
        env_file = ".env"
        env_file_encoding = 'utf-8'
        case_sensitive = True


# Singleton pattern for settings
_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """
    Get application settings singleton.
    
    Returns:
        Settings instance loaded from environment variables and config files
    """
    global _settings
    if _settings is None:
        try:
            _settings = Settings()
            _settings.configure_logging()
            logger.info(f"Settings loaded - Two-stage sync: {_settings.ENABLE_TWO_STAGE_SYNC}")
        except Exception as e:
            logger.error(f"Failed to load settings: {e}")
            raise
    return _settings


def reload_settings() -> Settings:
    """
    Force reload of settings (useful for testing).
    
    Returns:
        Fresh Settings instance
    """
    global _settings
    _settings = None
    return get_settings()