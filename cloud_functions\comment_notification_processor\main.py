#!/usr/bin/env python3
"""
Comment Notification Processor Cloud Function

Processes comment mention notifications when comments are created or updated in Firestore.
Triggered by Firestore document writes on the COMMENTS collection.

Following DRCR patterns from existing cloud functions.
"""

import os
import sys

# Add the current and parent directories to sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)

if current_dir not in sys.path:
    sys.path.insert(0, current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

try:
    from dotenv import load_dotenv
    load_dotenv()  # Load .env file as early as possible (local dev only)
except ImportError:
    pass  # dotenv not available in Cloud Functions

import logging
import json
from datetime import datetime
import functions_framework
from google.cloud import firestore
from google.cloud.firestore import SERVER_TIMESTAMP

# Import our services
from services.notification_processor import NotificationProcessor
from utils.error_handler import ErrorHandler

# Initialize logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# GCP Project ID
GCP_PROJECT_ID = os.getenv("GCP_PROJECT_ID")


@functions_framework.cloud_event
def on_comment_write(cloud_event):
    """
    Cloud Function entry point for comment write events
    
    Triggered on create and update of COMMENTS collection documents.
    Processes mention notifications for newly mentioned users.
    
    Args:
        cloud_event: CloudEvent containing Firestore document change data
        
    Returns:
        dict: Processing results with status and metrics
    """
    start_time = datetime.utcnow()
    error_handler = ErrorHandler()  # Outside try block for error logging
    
    try:
        logger.info("Starting comment notification processing")
        
        # Parse the cloud event
        event_data = cloud_event.data
        
        # Check if this is a delete event - ignore those
        if not event_data.get("value"):
            logger.info("Ignoring delete event")
            return {"status": "ignored", "reason": "delete_event"}
        
        # Get the new document data
        new_value = event_data.get("value", {})
        old_value = event_data.get("oldValue", {})
        
        # Parse document data
        new_doc = _parse_firestore_document(new_value)
        old_doc = _parse_firestore_document(old_value) if old_value else {}
        
        # Check if comment is deleted - ignore deleted comments
        if new_doc.get("deleted", False):
            logger.info("Ignoring deleted comment")
            return {"status": "ignored", "reason": "comment_deleted"}
        
        # Calculate new mentions (mentions in new doc that weren't in old doc)
        new_mentions = set(new_doc.get("mentions", []))
        old_mentions = set(old_doc.get("mentions", []))
        added_mentions = new_mentions - old_mentions
        
        if not added_mentions:
            logger.info("No new mentions to process")
            return {"status": "success", "processed_mentions": 0}
        
        # Initialize notification processor
        db = firestore.Client(project=GCP_PROJECT_ID)
        processor = NotificationProcessor(db)
        
        # Process the notifications
        comment_id = event_data.get("document", "").split("/")[-1]
        result = await processor.process_mention_notifications(
            comment_id=comment_id,
            comment_data=new_doc,
            mentioned_uids=list(added_mentions)
        )
        
        duration = (datetime.utcnow() - start_time).total_seconds()
        
        logger.info(f"Comment notification processing completed in {duration:.2f}s")
        
        return {
            "status": "success",
            "processed_mentions": len(added_mentions),
            "notifications_created": result.get("notifications_created", 0),
            "emails_sent": result.get("emails_sent", 0),
            "duration_seconds": duration,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        duration = (datetime.utcnow() - start_time).total_seconds()
        error_message = str(e)
        
        logger.error(f"Comment notification processing failed: {error_message}", exc_info=True)
        
        # Log to error handler for monitoring
        try:
            error_handler.log_error("comment_notification_failure", error_message, {
                "duration_seconds": duration,
                "comment_id": comment_id if 'comment_id' in locals() else None
            })
        except Exception:
            pass
        
        return {
            "status": "error",
            "error": error_message,
            "duration_seconds": duration,
            "timestamp": datetime.utcnow().isoformat()
        }, 500


def _parse_firestore_document(firestore_data):
    """
    Parse Firestore document data from cloud event format to regular dict
    
    Args:
        firestore_data: Firestore document data in cloud event format
        
    Returns:
        dict: Parsed document data
    """
    if not firestore_data or not firestore_data.get("fields"):
        return {}
    
    fields = firestore_data["fields"]
    parsed = {}
    
    for field_name, field_value in fields.items():
        if "stringValue" in field_value:
            parsed[field_name] = field_value["stringValue"]
        elif "booleanValue" in field_value:
            parsed[field_name] = field_value["booleanValue"]
        elif "integerValue" in field_value:
            parsed[field_name] = int(field_value["integerValue"])
        elif "timestampValue" in field_value:
            parsed[field_name] = field_value["timestampValue"]
        elif "arrayValue" in field_value:
            # Handle arrays (like mentions)
            array_values = field_value["arrayValue"].get("values", [])
            parsed_array = []
            for item in array_values:
                if "stringValue" in item:
                    parsed_array.append(item["stringValue"])
                # Add other types as needed
            parsed[field_name] = parsed_array
        # Add other field types as needed
    
    return parsed


# For local testing
if __name__ == "__main__":
    # Create a mock cloud event for testing
    mock_event_data = {
        "data": {
            "value": {
                "fields": {
                    "parent_type": {"stringValue": "schedule"},
                    "parent_id": {"stringValue": "test-schedule-id"},
                    "client_id": {"stringValue": "test-client-id"},
                    "text": {"stringValue": "Test comment with @user mention"},
                    "mentions": {"arrayValue": {"values": [{"stringValue": "test-uid-123"}]}},
                    "created_by": {"stringValue": "author-uid"},
                    "deleted": {"booleanValue": False}
                }
            },
            "oldValue": {},
            "document": "projects/test/databases/(default)/documents/COMMENTS/test-comment-id"
        }
    }
    
    class MockCloudEvent:
        def __init__(self, data):
            self.data = data
    
    mock_event = MockCloudEvent(mock_event_data["data"])
    result = on_comment_write(mock_event)
    print(f"Test result: {result}")