#!/usr/bin/env python3
"""
Verify we're calling the Nylas API correctly by testing each step
"""

import requests
import json

NYLAS_API_KEY = "nyk_v0_BlT3sXqnvDnmP3R8nWgn9ngMiMW0RZL6WeforK3GcznWbIPwbLO4noOzHgBb3oqy"
GRANT_ID = "8ede108b-5335-47bf-9a4a-325e60f2b2b8"
API_URI = "https://api.eu.nylas.com/v3"

def test_api_step_by_step():
    """Test each API call step by step to verify we're doing it right"""
    print("🔍 VERIFYING NYLAS API CALLS STEP BY STEP")
    print("=" * 60)
    
    headers = {
        'Authorization': f'Bearer {NYLAS_API_KEY}',
        'Content-Type': 'application/json'
    }
    
    # Step 1: Test basic API connectivity
    print("\n1️⃣ Testing Basic API Connectivity...")
    try:
        response = requests.get(f"{API_URI}/applications", headers=headers, timeout=10)
        print(f"   Status: {response.status_code}")
        print(f"   Content-Type: {response.headers.get('content-type')}")
        
        if 'application/json' in response.headers.get('content-type', ''):
            data = response.json()
            print(f"   ✅ JSON Response: {data}")
        else:
            print(f"   ❌ Non-JSON Response: {response.text[:200]}...")
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
    
    # Step 2: Test grant endpoint (should return JSON)
    print("\n2️⃣ Testing Grant Endpoint...")
    try:
        response = requests.get(f"{API_URI}/grants/{GRANT_ID}", headers=headers, timeout=10)
        print(f"   Status: {response.status_code}")
        print(f"   Content-Type: {response.headers.get('content-type')}")
        
        if 'application/json' in response.headers.get('content-type', ''):
            data = response.json()
            print(f"   ✅ JSON Response: Grant status = {data.get('data', {}).get('grant_status')}")
            print(f"   ✅ Scopes: {data.get('data', {}).get('scopes')}")
        else:
            print(f"   ❌ Non-JSON Response: {response.text[:200]}...")
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
    
    # Step 3: Test messages endpoint with GET (should be 405 Method Not Allowed, but JSON)
    print("\n3️⃣ Testing Messages Endpoint with GET...")
    try:
        response = requests.get(f"{API_URI}/grants/{GRANT_ID}/messages", headers=headers, timeout=10)
        print(f"   Status: {response.status_code}")
        print(f"   Content-Type: {response.headers.get('content-type')}")
        
        if 'application/json' in response.headers.get('content-type', ''):
            try:
                data = response.json()
                print(f"   ✅ JSON Response: {data}")
            except:
                print(f"   ⚠️  JSON parse failed: {response.text[:200]}...")
        else:
            print(f"   ❌ Non-JSON Response: {response.text[:200]}...")
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
    
    # Step 4: Test exact send call with minimal payload
    print("\n4️⃣ Testing Send Call with Minimal Valid Payload...")
    
    # Use the exact payload format from Nylas docs
    minimal_payload = {
        "to": [{"email": "<EMAIL>"}],
        "subject": "Test from Nylas API",
        # Nylas expects an object for `body`, not a raw string
        "body": {
            "text": "Test message"
        }
    }
    
    print(f"   Payload: {json.dumps(minimal_payload, indent=2)}")
    print(f"   URL: {API_URI}/grants/{GRANT_ID}/messages")
    print(f"   Headers: {headers}")
    
    try:
        response = requests.post(
            f"{API_URI}/grants/{GRANT_ID}/messages",
            headers=headers,
            json=minimal_payload,
            timeout=10
        )
        
        print(f"   Status: {response.status_code}")
        print(f"   Content-Type: {response.headers.get('content-type')}")
        print(f"   Response Headers: {dict(response.headers)}")
        
        if 'application/json' in response.headers.get('content-type', ''):
            try:
                data = response.json()
                print(f"   ✅ JSON Response: {data}")
                
                # Check for request_id
                request_id = data.get('request_id')
                if request_id:
                    print(f"   🎯 REQUEST ID FOUND: {request_id}")
                else:
                    print(f"   ⚠️  No request_id in JSON response")
                    
            except:
                print(f"   ⚠️  JSON parse failed: {response.text[:200]}...")
        else:
            print(f"   ❌ Non-JSON Response (THIS IS THE PROBLEM!): {response.text[:200]}...")
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
    
    # Step 5: Test with different from address
    print("\n5️⃣ Testing with Dynamic From Address...")
    
    dynamic_payload = {
        "to": [{"email": "<EMAIL>"}],
        "from": [{"email": "<EMAIL>"}],  # Add explicit from
        "subject": "Test with From Address", 
        "body": {
            "text": "Test message with from address"
        }
    }
    
    try:
        response = requests.post(
            f"{API_URI}/grants/{GRANT_ID}/messages",
            headers=headers,
            json=dynamic_payload,
            timeout=10
        )
        
        print(f"   Status: {response.status_code}")
        print(f"   Content-Type: {response.headers.get('content-type')}")
        
        if 'application/json' in response.headers.get('content-type', ''):
            try:
                data = response.json()
                print(f"   ✅ JSON Response: {data}")
            except:
                print(f"   ⚠️  JSON parse failed: {response.text[:200]}...")
        else:
            print(f"   ❌ Non-JSON Response: {response.text[:200]}...")
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")

def main():
    """Run API verification"""
    test_api_step_by_step()
    
    print("\n" + "=" * 60)
    print("📋 ANALYSIS:")
    print("• If Steps 1-2 return JSON but Step 4-5 return HTML: API format issue")
    print("• If all steps return HTML: Authentication/endpoint issue") 
    print("• If Steps 4-5 return JSON with errors: Permission issue (but API works)")
    print("• Look for request_id in JSON responses for support")

if __name__ == "__main__":
    main()