"use client"

import { ChevronRight, type LucideIcon } from "lucide-react"
import { useNavigate, useLocation } from "react-router-dom"
import { useState, useEffect } from "react"

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "../ui/collapsible"
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "../ui/sidebar"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip"

export function NavMain({
  items,
}: {
  items: {
    title: string
    url: string
    icon?: LucideIcon
    isActive?: boolean
    items?: {
      title: string
      url: string
      disabled?: boolean
    }[]
  }[]
}) {
  const navigate = useNavigate()
  const location = useLocation()
  const { state } = useSidebar()

  // State to track which menu items are expanded
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({})

  // Load expanded state from localStorage on mount
  useEffect(() => {
    const savedState = localStorage.getItem('sidebar-expanded-items')
    if (savedState) {
      try {
        setExpandedItems(JSON.parse(savedState))
      } catch (error) {
        console.warn('Failed to parse saved sidebar state:', error)
      }
    }
  }, [])

  // Save expanded state to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('sidebar-expanded-items', JSON.stringify(expandedItems))
  }, [expandedItems])

  // Function to toggle item expansion
  const toggleItem = (itemTitle: string, isOpen: boolean) => {
    setExpandedItems(prev => ({
      ...prev,
      [itemTitle]: isOpen
    }))
  }

  // Function to get initial open state for an item
  const getInitialOpenState = (item: typeof items[0]) => {
    // First check localStorage state
    if (expandedItems.hasOwnProperty(item.title)) {
      return expandedItems[item.title]
    }
    // Fallback to route-based logic for initial load
    return item.isActive || location.pathname.startsWith(item.url)
  }

  return (
    <SidebarGroup>
      <SidebarMenu className="space-y-1">
        {items.map((item) => (
          <Collapsible
            key={item.title}
            asChild
            open={getInitialOpenState(item)}
            onOpenChange={(isOpen) => toggleItem(item.title, isOpen)}
            className="group/collapsible"
          >
            <SidebarMenuItem className="relative group/menuitem">
              <CollapsibleTrigger asChild>
                <SidebarMenuButton 
                  tooltip={item.title}
                  className="h-10 px-3 font-medium hover:bg-sidebar-accent data-[state=open]:bg-sidebar-accent/50 transition-all duration-200 hover:scale-[1.02] active:scale-[0.98]"
                >
                  {item.icon && <item.icon className="h-4 w-4" />}
                  <span className="text-sm">{item.title}</span>
                  <ChevronRight className="ml-auto h-4 w-4 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                </SidebarMenuButton>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <SidebarMenuSub className="ml-6 mt-1 space-y-1">
                  {item.items?.map((subItem) => (
                    <SidebarMenuSubItem key={subItem.title}>
                      <SidebarMenuSubButton
                        asChild={!subItem.disabled}
                        className={`h-9 px-6 text-sm font-normal ${
                          subItem.disabled
                            ? 'text-muted-foreground cursor-not-allowed opacity-50'
                            : 'hover:bg-sidebar-accent cursor-pointer'
                        }`}
                      >
                        {subItem.disabled ? (
                          <span>{subItem.title}</span>
                        ) : (
                          <button
                            onClick={(e) => {
                              e.preventDefault()
                              e.stopPropagation()
                              navigate(subItem.url)
                              // Prevent any default sidebar collapse behavior
                            }}
                            className="w-full text-left transition-colors duration-200"
                          >
                            <span>{subItem.title}</span>
                          </button>
                        )}
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                  ))}
                </SidebarMenuSub>
              </CollapsibleContent>
              {/* No submenu when collapsed - just hide it completely */}
            </SidebarMenuItem>
          </Collapsible>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  )
}
