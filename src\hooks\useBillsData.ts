import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { api } from '@/lib/api';
import { PrepaymentsService, type PrepaymentsFilters } from '../services/prepayments.service';
import { transformToHierarchicalData } from '../types/hierarchical-bills.types';
import type { HierarchicalBillsData, ExpandedState } from '../types/hierarchical-bills.types';

export interface Client {
  clientId: string;
  clientName: string;
}

export interface Entity {
  entityId: string;
  entityName: string;
  connectionStatus: string;
}

export interface PaginationState {
  currentPage: number;
  totalItems: number;
  totalPages: number;
  pageSize: number;
  hasMore: boolean;
  isLoadingMore: boolean;
}

export interface UseBillsDataReturn {
  // Data state
  hierarchicalData: HierarchicalBillsData;
  setHierarchicalData: React.Dispatch<React.SetStateAction<HierarchicalBillsData>>;
  clients: Client[];
  entities: Entity[];
  
  // Loading states
  isLoading: boolean;
  isLoadingClients: boolean;
  isLoadingEntities: boolean;
  error: string | null;
  
  // Pagination
  pagination: PaginationState;
  
  // Actions
  loadBills: (resetData?: boolean, pageOverride?: number) => Promise<void>;
  handleDataRefresh: (
    preserveSelection?: boolean, 
    disableCache?: boolean,
    getCurrentSelectionState?: () => any,
    restoreSelectionState?: (data: HierarchicalBillsData, selectionState: any) => HierarchicalBillsData
  ) => Promise<void>;
  invalidateCache: (
    getCurrentSelectionState?: () => any,
    restoreSelectionState?: (data: HierarchicalBillsData, selectionState: any) => HierarchicalBillsData
  ) => Promise<void>;
  handleLoadMore: () => Promise<void>;
  fetchClients: () => Promise<void>;
  fetchEntities: (clientId: string) => Promise<void>;
}

export function useBillsData(
  selectedClientId: string | null,
  selectedEntityId: string | null,
  selectedStatusFilters: string[],
  setClientId?: (clientId: string) => void,
  setEntityId?: (entityId: string | null) => void,
  isHydrated: boolean = true
): UseBillsDataReturn {
  // Core data state
  const [hierarchicalData, setHierarchicalData] = useState<HierarchicalBillsData>({ suppliers: [] });
  const [clients, setClients] = useState<Client[]>([]);
  const [entities, setEntities] = useState<Entity[]>([]);
  
  // Loading states
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingClients, setIsLoadingClients] = useState(false);
  const [isLoadingEntities, setIsLoadingEntities] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Pagination state
  const [pagination, setPagination] = useState<PaginationState>({
    currentPage: 1,
    totalItems: 0,
    totalPages: 0,
    pageSize: 20,
    hasMore: false,
    isLoadingMore: false,
  });

  // Fetch clients data
  const fetchClients = async () => {
    try {
      setIsLoadingClients(true);
      const response = await api.getClients();
      const clientsData = (response.clients || []).map(client => ({
        clientId: client.client_id,
        clientName: client.name,
      }));
      setClients(clientsData);
      
      // If no client selected but we have clients, select first one
      if (!selectedClientId && clientsData.length > 0 && setClientId) {
        setClientId(clientsData[0].clientId);
      }
    } catch (error) {
      console.error('Error fetching clients:', error);
      toast.error('Failed to load clients');
    } finally {
      setIsLoadingClients(false);
    }
  };

  // Fetch entities for selected client
  const fetchEntities = async (clientId: string) => {
    try {
      setIsLoadingEntities(true);
      const response = await api.getEntitiesForClient(clientId);
      const entitiesData = (response.entities || []).map(entity => ({
        entityId: entity.entity_id,
        entityName: entity.entity_name,
        connectionStatus: entity.connection_status,
      }));
      setEntities(entitiesData);
    } catch (error) {
      console.error('Error fetching entities:', error);
      toast.error('Failed to load entities');
    } finally {
      setIsLoadingEntities(false);
    }
  };

  // Load bills data (initial load and refresh)
  const loadBills = async (resetData = true, pageOverride?: number) => {
    try {
      if (resetData) {
        setIsLoading(true);
        setPagination(prev => ({ ...prev, currentPage: 1 }));
      }
      setError(null);

      // Use pageOverride if provided, otherwise use resetData logic
      const targetPage = pageOverride ?? (resetData ? 1 : pagination.currentPage);
      
      console.log('🔍 LoadBills Debug:', {
        resetData,
        pageOverride,
        paginationCurrentPage: pagination.currentPage,
        targetPage,
        selectedClientId,
        selectedEntityId
      });

      const filters: PrepaymentsFilters = {
        client_id: selectedClientId,
        entity_id: (selectedEntityId && selectedEntityId !== 'all') ? selectedEntityId : undefined,
        page: targetPage,
        limit: pagination.pageSize,
        status_filters: selectedStatusFilters && selectedStatusFilters.length > 0 ? selectedStatusFilters : undefined,
      };

      const response = await PrepaymentsService.getPrepaymentsData(filters);
      
      // Update pagination info
      // Fix: For grouped data, check if we've loaded all available data from backend
      // If currentPage >= totalPages, we've loaded everything the backend has
      const allBackendDataLoaded = (response.pagination.currentPage || 1) >= (response.pagination.totalPages || 1);
      const hasMoreValue = !allBackendDataLoaded;
      console.log('🔍 Pagination Debug:', {
        currentPage: response.pagination.currentPage,
        totalPages: response.pagination.totalPages,
        totalItems: response.pagination.totalItems,
        hasMore: hasMoreValue,
        suppliersLoaded: response.suppliers.length,
        allBackendDataLoaded: allBackendDataLoaded,
        isLoadingMore: pagination.isLoadingMore
      });
      
      setPagination(prev => {
        const newPagination = {
          ...prev,
          totalItems: response.pagination.totalItems || 0,
          totalPages: response.pagination.totalPages || 0,
          hasMore: hasMoreValue,
          isLoadingMore: false, // Reset loading state
        };
        console.log('🔍 Setting Pagination:', {
          before: prev,
          after: newPagination,
          responseCurrentPage: response.pagination.currentPage
        });
        return newPagination;
      });
      
      // Transform to hierarchical structure
      const hierarchical = transformToHierarchicalData(response.suppliers);
      
      if (resetData) {
        // Replace data for initial load or filter changes
        setHierarchicalData(hierarchical);
      } else {
        // Append data for pagination (Load More)
        setHierarchicalData(prev => ({
          suppliers: [...prev.suppliers, ...hierarchical.suppliers]
        }));
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load bills');
    } finally {
      setIsLoading(false);
      setPagination(prev => ({ ...prev, isLoadingMore: false }));
    }
  };


  // Refresh data with optional selection preservation
  const handleDataRefresh = async (
    preserveSelection = false, 
    disableCache = false,
    getCurrentSelectionState?: () => any,
    restoreSelectionState?: (data: HierarchicalBillsData, selectionState: any) => HierarchicalBillsData
  ) => {
    if (!selectedClientId) return;

    try {
      // Capture current selection state if requested and helper is provided
      const selectionState = preserveSelection && getCurrentSelectionState ? getCurrentSelectionState() : null;

      const filters: PrepaymentsFilters = {
        client_id: selectedClientId,
        entity_id: selectedEntityId || undefined,
        page: 1,
        limit: 100,
        status_filters: selectedStatusFilters && selectedStatusFilters.length > 0 ? selectedStatusFilters : undefined,
      };

      const response = await PrepaymentsService.getPrepaymentsData(filters, disableCache);
      
      // Transform to hierarchical structure
      let hierarchical = transformToHierarchicalData(response.suppliers);

      // Restore selection state if needed and helper is provided
      if (preserveSelection && selectionState && restoreSelectionState) {
        hierarchical = restoreSelectionState(hierarchical, selectionState);
      }

      setHierarchicalData(hierarchical);
    } catch (err) {
      console.error('Error refreshing data:', err);
    }
  };

  // Invalidate cache helper (hides internal disableCache flag)
  const invalidateCache = async (
    getCurrentSelectionState?: () => any,
    restoreSelectionState?: (data: HierarchicalBillsData, selectionState: any) => HierarchicalBillsData
  ) => {
    await handleDataRefresh(true, true, getCurrentSelectionState, restoreSelectionState); // Preserve selection, disable cache
  };

  // Load more data for pagination
  const handleLoadMore = async () => {
    if (pagination.isLoadingMore || !pagination.hasMore) return;
    
    const nextPage = pagination.currentPage + 1;
    console.log('🔍 Load More Debug:', {
      currentPage: pagination.currentPage,
      nextPage: nextPage,
      hasMore: pagination.hasMore,
      totalItems: pagination.totalItems
    });
    
    setPagination(prev => ({ 
      ...prev, 
      currentPage: nextPage,
      isLoadingMore: true 
    }));
    
    // Use unified loadBills function with pageOverride to avoid stale state
    await loadBills(false, nextPage);
  };

  // Load clients on mount
  useEffect(() => {
    fetchClients();
  }, []);

  // Load entities when client changes (only after hydration)
  useEffect(() => {
    if (selectedClientId && isHydrated) {
      fetchEntities(selectedClientId);
      // Don't auto-reset entity - let URL sync handle it
    } else if (!selectedClientId) {
      setEntities([]);
      if (setEntityId) {
        setEntityId(null);
      }
    }
  }, [selectedClientId, isHydrated]); // eslint-disable-line react-hooks/exhaustive-deps

  // Load bills when filters change (only after hydration)
  useEffect(() => {
    if (!selectedClientId || !isHydrated) return;
    loadBills(true); // Reset data on filter change
  }, [selectedClientId, selectedEntityId, selectedStatusFilters, isHydrated]); // eslint-disable-line react-hooks/exhaustive-deps

  return {
    // Data state
    hierarchicalData,
    setHierarchicalData,
    clients,
    entities,
    
    // Loading states
    isLoading,
    isLoadingClients,
    isLoadingEntities,
    error,
    
    // Pagination
    pagination,
    
    // Actions
    loadBills,
    handleDataRefresh,
    invalidateCache,
    handleLoadMore,
    fetchClients,
    fetchEntities,
  };
}