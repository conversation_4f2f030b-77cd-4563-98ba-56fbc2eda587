# Deploy Threading Implementation
Write-Host "Deploying Nylas Threading Implementation..." -ForegroundColor Cyan

$envVars = "GCP_PROJECT_ID=drcr-d660a," +
           "NYLAS_CLIENT_ID=672d85ca-ad40-4d7d-b6ee-fbbd1d7e1d29," +
           "NYLAS_API_KEY=nyk_v0_243L0pUklhtwpk3Sn1wW4uaSsyiyPz9ApTJxb6ewKyatPSsbIFkQG8ysfnE72D6M," +
           "NYLAS_GRANT_ID=c12ff227-31ed-4099-b70f-6fde75f02e02," +
           "NYLAS_API_URI=https://api.us.nylas.com/v3," +
           "POLLING_BATCH_SIZE=50," +
           "POLLING_TIMEOUT_SECONDS=30," +
           "LOG_LEVEL=INFO," +
           "ENABLE_FIRESTORE_LOGGING=true"

Write-Host "NEW FEATURES:" -ForegroundColor Yellow
Write-Host "  - Nylas Threads API integration" -ForegroundColor Green
Write-Host "  - EMAIL_THREADS subcollection" -ForegroundColor Green  
Write-Host "  - Proper email chain management" -ForegroundColor Green
Write-Host "  - Thread-level metadata storage" -ForegroundColor Green

gcloud functions deploy nylas_email_poller `
  --runtime python39 `
  --trigger-http `
  --allow-unauthenticated `
  --timeout 540s `
  --memory 512MB `
  --region europe-west2 `
  --set-env-vars $envVars

Write-Host "Threading implementation deployed!" -ForegroundColor Green
Write-Host "Expected new data structure:" -ForegroundColor Yellow
Write-Host "  ENTITIES/entity_id/EMAIL_THREADS/thread_id" -ForegroundColor Cyan
Write-Host "  ENTITIES/entity_id/EMAILS/email_id" -ForegroundColor Cyan