#!/usr/bin/env python3
"""
Check connector configuration for default scopes
"""

import requests
import json

NYLAS_API_KEY = "nyk_v0_BlT3sXqnvDnmP3R8nWgn9ngMiMW0RZL6WeforK3GcznWbIPwbLO4noOzHgBb3oqy"
API_URI = "https://api.eu.nylas.com/v3"

headers = {
    'Authorization': f'Bearer {NYLAS_API_KEY}',
    'Content-Type': 'application/json'
}

def check_connectors():
    """Check all connectors and their scopes"""
    print("🔍 Checking Connector Configuration...")
    
    try:
        response = requests.get(f"{API_URI}/connectors", headers=headers, timeout=10)
        
        if response.status_code == 200:
            connectors = response.json().get('data', [])
            
            for connector in connectors:
                print(f"\n📋 Connector: {connector.get('name')}")
                print(f"   ID: {connector.get('id')}")
                print(f"   Provider: {connector.get('provider')}")
                print(f"   Default Scopes: {connector.get('scope', 'NOT SET')}")
                
                # Check if this is the Google connector
                if connector.get('provider') == 'google':
                    scopes = connector.get('scope', [])
                    if not scopes:
                        print("   ❌ PROBLEM: No default scopes on Google connector!")
                        return False
                    elif 'gmail.send' in str(scopes) or 'https://www.googleapis.com/auth/gmail.send' in str(scopes):
                        print("   ✅ Gmail send scope found in connector")
                        return True
                    else:
                        print(f"   ❌ PROBLEM: No Gmail send scope in connector scopes: {scopes}")
                        return False
            
            print("\n❌ No Google connector found")
            return False
            
        else:
            print(f"❌ Error fetching connectors: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def main():
    """Check connector configuration"""
    print("🔧 Connector Scope Configuration Check")
    print("=" * 50)
    
    success = check_connectors()
    
    if not success:
        print("\n🔧 FIX: Update your Google connector with default scopes:")
        print("   1. Go to Nylas Dashboard → Integrations → Google")
        print("   2. Make sure 'Default Scopes' includes:")
        print("      - gmail.readonly")
        print("      - gmail.send")
        print("      - gmail.compose")
        print("   3. Save connector configuration")
        print("   4. Create new grant")

if __name__ == "__main__":
    main()