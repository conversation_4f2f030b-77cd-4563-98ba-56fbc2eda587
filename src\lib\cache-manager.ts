/**
 * Cache Manager - Centralized cache management for the application
 * Handles clearing of all types of cache: API cache, localStorage, sessionStorage, etc.
 */

import { api } from './api';

export class CacheManager {
  private static instance: CacheManager;
  
  private constructor() {}
  
  static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  /**
   * Clear all application caches
   */
  clearAllCaches(): void {
    console.log('🧹 Clearing all application caches...');
    
    // Clear API cache
    this.clearApiCache();
    
    // Clear localStorage
    this.clearLocalStorage();
    
    // Clear sessionStorage
    this.clearSessionStorage();
    
    // Clear browser cache (if possible)
    this.clearBrowserCache();
    
    console.log('✅ All caches cleared successfully');
  }

  /**
   * Clear all cookies for the current domain
   */
  clearCookies(): void {
    console.log('🍪 Clearing all cookies...');
    
    try {
      const cookies = document.cookie.split(';');
      
      cookies.forEach(cookie => {
        const eqPos = cookie.indexOf('=');
        const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
        
        // Delete cookie by setting it to expire in the past
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname};`;
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${window.location.hostname};`;
        
        console.log(`🍪 Removed cookie: ${name}`);
      });
      
      console.log('✅ All cookies cleared successfully');
    } catch (error) {
      console.warn('⚠️ Failed to clear cookies:', error);
    }
  }

  /**
   * Clear API client cache
   */
  private clearApiCache(): void {
    try {
      // Clear the API client cache
      if (api && typeof api.clearCache === 'function') {
        api.clearCache();
        console.log('✅ API cache cleared');
      }
    } catch (error) {
      console.warn('⚠️ Failed to clear API cache:', error);
    }
  }

  /**
   * Clear localStorage items related to the application
   */
  private clearLocalStorage(): void {
    try {
      const keysToRemove = [
        'navigation-store',
        'resizable-panel-width',
        'sidebar-expanded-items',
        'auth-store',
        'client-store',
        // Add any other localStorage keys used by your app
      ];

      keysToRemove.forEach(key => {
        if (localStorage.getItem(key)) {
          localStorage.removeItem(key);
          console.log(`✅ Removed localStorage key: ${key}`);
        }
      });

      // Also clear any keys that start with our app prefix
      const appPrefixes = ['drcr_', 'navigation-', 'auth-', 'client-'];
      const allKeys = Object.keys(localStorage);
      
      allKeys.forEach(key => {
        if (appPrefixes.some(prefix => key.startsWith(prefix))) {
          localStorage.removeItem(key);
          console.log(`✅ Removed localStorage key: ${key}`);
        }
      });
    } catch (error) {
      console.warn('⚠️ Failed to clear localStorage:', error);
    }
  }

  /**
   * Clear sessionStorage
   */
  private clearSessionStorage(): void {
    try {
      sessionStorage.clear();
      console.log('✅ SessionStorage cleared');
    } catch (error) {
      console.warn('⚠️ Failed to clear sessionStorage:', error);
    }
  }

  /**
   * Attempt to clear browser cache (limited by browser security)
   */
  private clearBrowserCache(): void {
    try {
      // Clear service worker cache if available
      if ('caches' in window) {
        caches.keys().then(cacheNames => {
          return Promise.all(
            cacheNames.map(cacheName => {
              console.log(`🗑️ Deleting cache: ${cacheName}`);
              return caches.delete(cacheName);
            })
          );
        }).then(() => {
          console.log('✅ Browser caches cleared');
        }).catch(error => {
          console.warn('⚠️ Failed to clear browser caches:', error);
        });
      }
    } catch (error) {
      console.warn('⚠️ Failed to clear browser cache:', error);
    }
  }

  /**
   * Clear specific cache by type
   */
  clearCacheByType(type: 'api' | 'localStorage' | 'sessionStorage' | 'browser'): void {
    switch (type) {
      case 'api':
        this.clearApiCache();
        break;
      case 'localStorage':
        this.clearLocalStorage();
        break;
      case 'sessionStorage':
        this.clearSessionStorage();
        break;
      case 'browser':
        this.clearBrowserCache();
        break;
    }
  }

  /**
   * Check if cache clearing is needed (for debugging)
   */
  getCacheStatus(): {
    hasLocalStorage: boolean;
    hasSessionStorage: boolean;
    localStorageKeys: string[];
    sessionStorageKeys: string[];
  } {
    const localStorageKeys = Object.keys(localStorage);
    const sessionStorageKeys = Object.keys(sessionStorage);
    
    return {
      hasLocalStorage: localStorageKeys.length > 0,
      hasSessionStorage: sessionStorageKeys.length > 0,
      localStorageKeys,
      sessionStorageKeys
    };
  }
}

// Export singleton instance
export const cacheManager = CacheManager.getInstance(); 