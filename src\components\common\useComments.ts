import { useState, useEffect, useCallback } from 'react';
import { 
  CommentsService, 
  ParentType 
} from '@/services/comments.service';
import type { 
  CommentOut, 
  CommentCreate, 
  CommentUpdate, 
  CommentsListResponse,
  CommentFilters
} from '@/services/comments.service';
import { toast } from 'sonner';

interface UseCommentsOptions {
  parentType: ParentType;
  parentId: string;
  autoLoad?: boolean;
  pageSize?: number;
  enableOptimisticUpdates?: boolean;
}

interface UseCommentsReturn {
  // Data
  comments: CommentOut[];
  loading: boolean;
  error: string | null;
  
  // Pagination
  currentPage: number;
  totalPages: number;
  totalComments: number;
  
  // Actions
  loadComments: (page?: number, filters?: CommentFilters) => Promise<void>;
  addComment: (commentData: Omit<CommentCreate, 'parent_type' | 'parent_id'>) => Promise<void>;
  updateComment: (commentId: string, updateData: CommentUpdate) => Promise<void>;
  deleteComment: (commentId: string) => Promise<void>;
  refreshComments: () => Promise<void>;
  
  // Utilities
  canEditComment: (comment: CommentOut, currentUserUid?: string) => boolean;
  canDeleteComment: (comment: CommentOut, currentUserUid?: string) => boolean;
  searchComments: (searchText: string) => Promise<void>;
  
  // State management
  setCurrentPage: (page: number) => void;
  clearError: () => void;
}

export const useComments = (options: UseCommentsOptions): UseCommentsReturn => {
  const {
    parentType,
    parentId,
    autoLoad = true,
    pageSize = 25,
    enableOptimisticUpdates = true
  } = options;
  
  // State
  const [comments, setComments] = useState<CommentOut[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalComments, setTotalComments] = useState(0);
  const [currentFilters, setCurrentFilters] = useState<CommentFilters>({});
  
  // Load comments
  const loadComments = useCallback(async (
    page: number = currentPage,
    filters: CommentFilters = currentFilters
  ) => {
    try {
      setLoading(true);
      setError(null);
      
      const response: CommentsListResponse = await CommentsService.getCommentsForRecord(
        parentType,
        parentId,
        { page, limit: pageSize, filters }
      );
      
      setComments(response.comments);
      setCurrentPage(response.page);
      setTotalPages(response.pages);
      setTotalComments(response.total_count);
      setCurrentFilters(filters);
      
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load comments';
      setError(errorMessage);
      console.error('Error loading comments:', err);
      
      // Don't show toast for permission errors (likely expected)
      if (!err.message?.includes('Access denied')) {
        toast.error(errorMessage);
      }
      
    } finally {
      setLoading(false);
    }
  }, [parentType, parentId, currentPage, currentFilters, pageSize]);
  
  // Add comment
  const addComment = useCallback(async (
    commentData: Omit<CommentCreate, 'parent_type' | 'parent_id'>
  ) => {
    try {
      setError(null);
      
      const fullCommentData: CommentCreate = {
        ...commentData,
        parent_type: parentType,
        parent_id: parentId
      };
      
      // Optimistic update
      if (enableOptimisticUpdates) {
        const optimisticComment: CommentOut = {
          comment_id: `optimistic-${Date.now()}`,
          parent_type: parentType,
          parent_id: parentId,
          client_id: '', // Will be filled by backend
          text: commentData.text,
          mentions: commentData.mention_uids || [],
          created_by: 'current-user', // Will be filled by backend
          created_at: new Date().toISOString(),
          deleted: false,
          author_display_name: 'You'
        };
        
        setComments(prev => [optimisticComment, ...prev]);
        setTotalComments(prev => prev + 1);
      }
      
      // Make API call
      const newComment = await CommentsService.createComment(fullCommentData);
      
      // Remove optimistic comment and add real one
      if (enableOptimisticUpdates) {
        setComments(prev => [
          newComment,
          ...prev.filter(c => !c.comment_id.startsWith('optimistic-'))
        ]);
      } else {
        // Reload comments if not using optimistic updates
        await loadComments(1, currentFilters);
      }
      
      toast.success('Comment added successfully');
      
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to add comment';
      setError(errorMessage);
      
      // Remove optimistic comment on error
      if (enableOptimisticUpdates) {
        setComments(prev => prev.filter(c => !c.comment_id.startsWith('optimistic-')));
        setTotalComments(prev => Math.max(0, prev - 1));
      }
      
      toast.error(errorMessage);
      throw err;
    }
  }, [parentType, parentId, enableOptimisticUpdates, loadComments, currentFilters]);
  
  // Update comment
  const updateComment = useCallback(async (
    commentId: string,
    updateData: CommentUpdate
  ) => {
    try {
      setError(null);
      
      // Optimistic update
      if (enableOptimisticUpdates) {
        const originalComment = comments.find(c => c.comment_id === commentId);
        if (originalComment) {
          setComments(prev => prev.map(c => 
            c.comment_id === commentId 
              ? { ...c, text: updateData.text, mentions: updateData.mention_uids || [] }
              : c
          ));
        
          // Make API call
          const updatedComment = await CommentsService.updateComment(commentId, updateData);
          
          // Update with real data
          setComments(prev => prev.map(c => 
            c.comment_id === commentId ? updatedComment : c
          ));
          
          toast.success('Comment updated successfully');
          
        } else {
          // No original comment found, just make API call
          const updatedComment = await CommentsService.updateComment(commentId, updateData);
          setComments(prev => prev.map(c => 
            c.comment_id === commentId ? updatedComment : c
          ));
          toast.success('Comment updated successfully');
        }
      } else {
        // No optimistic updates, just make API call
        const updatedComment = await CommentsService.updateComment(commentId, updateData);
        setComments(prev => prev.map(c => 
          c.comment_id === commentId ? updatedComment : c
        ));
        toast.success('Comment updated successfully');
      }
      
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to update comment';
      setError(errorMessage);
      
      // Revert optimistic update on error if we had made one
      if (enableOptimisticUpdates) {
        const originalComment = comments.find(c => c.comment_id === commentId);
        if (originalComment) {
          setComments(prev => prev.map(c => 
            c.comment_id === commentId ? originalComment : c
          ));
        }
      }
      
      toast.error(errorMessage);
      throw err;
    }
  }, [comments, enableOptimisticUpdates]);
  
  // Delete comment
  const deleteComment = useCallback(async (commentId: string) => {
    // Store original comment outside try-catch so it's accessible in catch block
    const originalComment = comments.find(c => c.comment_id === commentId);
    
    try {
      setError(null);
      
      // Optimistic update
      if (enableOptimisticUpdates && originalComment) {
        setComments(prev => prev.filter(c => c.comment_id !== commentId));
        setTotalComments(prev => Math.max(0, prev - 1));
      }
      
      // Make API call
      await CommentsService.deleteComment(commentId);
      
      // If not using optimistic updates, reload
      if (!enableOptimisticUpdates) {
        await loadComments(currentPage, currentFilters);
      }
      
      toast.success('Comment deleted successfully');
      
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to delete comment';
      setError(errorMessage);
      
      // Revert optimistic update on error
      if (enableOptimisticUpdates && originalComment) {
        setComments(prev => [...prev, originalComment].sort(
          (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        ));
        setTotalComments(prev => prev + 1);
      }
      
      toast.error(errorMessage);
      throw err;
    }
  }, [comments, enableOptimisticUpdates, loadComments, currentPage, currentFilters]);
  
  // Refresh comments
  const refreshComments = useCallback(async () => {
    await loadComments(currentPage, currentFilters);
  }, [loadComments, currentPage, currentFilters]);
  
  // Permission checks
  const canEditComment = useCallback((comment: CommentOut, currentUserUid?: string) => {
    return comment.created_by === currentUserUid && !comment.deleted;
  }, []);
  
  const canDeleteComment = useCallback((comment: CommentOut, currentUserUid?: string) => {
    return comment.created_by === currentUserUid && !comment.deleted;
  }, []);
  
  // Search comments
  const searchComments = useCallback(async (searchText: string) => {
    const filters: CommentFilters = {
      ...currentFilters,
      search_text: searchText || undefined
    };
    
    await loadComments(1, filters);
  }, [loadComments, currentFilters]);
  
  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);
  
  // Page setter
  const setPage = useCallback((page: number) => {
    setCurrentPage(page);
    loadComments(page, currentFilters);
  }, [loadComments, currentFilters]);
  
  // Auto-load on mount
  useEffect(() => {
    if (autoLoad && parentType && parentId) {
      loadComments(1, {});
    }
  }, [autoLoad, parentType, parentId]); // Only run when parent changes
  
  return {
    // Data
    comments,
    loading,
    error,
    
    // Pagination
    currentPage,
    totalPages,
    totalComments,
    
    // Actions
    loadComments,
    addComment,
    updateComment,
    deleteComment,
    refreshComments,
    
    // Utilities
    canEditComment,
    canDeleteComment,
    searchComments,
    
    // State management
    setCurrentPage: setPage,
    clearError
  };
};