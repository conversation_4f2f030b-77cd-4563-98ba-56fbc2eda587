"""
Comment Schemas - Request/response schemas for comment system API endpoints
Following DRCR validation patterns with client-scoped security integration
"""
from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict
from datetime import datetime
from enum import Enum


class ParentType(str, Enum):
    """Supported parent record types for comments"""
    SCHEDULE = "schedule"
    TRANSACTION = "transaction"
    INVOICE = "invoice"
    MANUAL_JOURNAL = "manual_journal"
    ENTITY = "entity"
    CLIENT = "client"


class CommentCreate(BaseModel):
    """Request model for creating a new comment"""
    parent_type: ParentType = Field(..., description="Type of record being commented on")
    parent_id: str = Field(..., min_length=2, max_length=60, description="ID of the record being commented on")
    text: str = Field(..., min_length=1, max_length=5000, description="Comment text (max 5000 characters)")
    mention_uids: Optional[List[str]] = Field(
        default_factory=list, 
        max_items=20, 
        description="Array of user UIDs mentioned in comment (max 20)"
    )
    
    @validator('text')
    def text_must_not_be_empty_after_strip(cls, v):
        """Ensure text is not just whitespace"""
        if not v.strip():
            raise ValueError('Comment text cannot be empty or only whitespace')
        return v.strip()
    
    @validator('mention_uids')
    def validate_mention_uids(cls, v):
        """Validate mention UIDs format and uniqueness"""
        if v is None:
            return []
        
        # Remove duplicates while preserving order
        seen = set()
        unique_uids = []
        for uid in v:
            if uid not in seen:
                seen.add(uid)
                unique_uids.append(uid)
        
        # Basic UID format validation (Firebase UIDs are typically 28 chars)
        for uid in unique_uids:
            if not uid or len(uid) < 10:
                raise ValueError(f'Invalid mention UID format: {uid}')
        
        return unique_uids


class CommentUpdate(BaseModel):
    """Request model for updating an existing comment"""
    text: str = Field(..., min_length=1, max_length=5000, description="Updated comment text")
    mention_uids: Optional[List[str]] = Field(
        default_factory=list, 
        max_items=20, 
        description="Updated array of user UIDs mentioned in comment"
    )
    
    @validator('text')
    def text_must_not_be_empty_after_strip(cls, v):
        """Ensure text is not just whitespace"""
        if not v.strip():
            raise ValueError('Comment text cannot be empty or only whitespace')
        return v.strip()
    
    @validator('mention_uids')
    def validate_mention_uids(cls, v):
        """Validate mention UIDs format and uniqueness"""
        if v is None:
            return []
        
        # Remove duplicates while preserving order
        seen = set()
        unique_uids = []
        for uid in v:
            if uid not in seen:
                seen.add(uid)
                unique_uids.append(uid)
        
        return unique_uids


class CommentOut(BaseModel):
    """Response model for comment data"""
    comment_id: str = Field(..., description="Unique comment identifier")
    parent_type: str = Field(..., description="Type of record being commented on")
    parent_id: str = Field(..., description="ID of the record being commented on")
    client_id: str = Field(..., description="Client ID for security scoping")
    entity_id: Optional[str] = Field(None, description="Entity ID if applicable")
    text: str = Field(..., description="Comment text")
    mentions: List[str] = Field(default_factory=list, description="Array of mentioned user UIDs")
    mention_mapping: Dict[str, str] = Field(default_factory=dict, description="Mapping of UID to display names for mentions")
    created_by: str = Field(..., description="UID of comment author")
    created_at: str = Field(..., description="Creation timestamp (ISO8601)")
    updated_at: Optional[str] = Field(None, description="Last update timestamp (ISO8601)")
    deleted: bool = Field(default=False, description="Soft delete flag")
    reply_to_comment_id: Optional[str] = Field(None, description="Parent comment ID for replies (future feature)")
    
    # Author information (populated from user lookup)
    author_display_name: Optional[str] = Field(None, description="Display name of comment author")
    author_email: Optional[str] = Field(None, description="Email of comment author")


class CommentsListResponse(BaseModel):
    """Response model for listing comments with pagination"""
    comments: List[CommentOut] = Field(..., description="List of comments")
    total_count: int = Field(..., description="Total number of comments")
    page: int = Field(..., description="Current page number")
    per_page: int = Field(..., description="Number of comments per page")
    pages: int = Field(..., description="Total number of pages")


class CommentFilters(BaseModel):
    """Filters for comment queries"""
    show_deleted: bool = Field(default=False, description="Include soft-deleted comments")
    created_by: Optional[str] = Field(None, description="Filter by author UID")
    mentions_user: Optional[str] = Field(None, description="Filter comments that mention specific user")
    date_from: Optional[datetime] = Field(None, description="Filter comments from this date")
    date_to: Optional[datetime] = Field(None, description="Filter comments to this date")
    search_text: Optional[str] = Field(None, description="Search in comment text")


class CommentStats(BaseModel):
    """Statistics for comment activity"""
    total_comments: int = Field(..., description="Total number of comments")
    comments_today: int = Field(..., description="Comments created today")
    comments_this_week: int = Field(..., description="Comments created this week")
    top_commenters: List[dict] = Field(default_factory=list, description="Most active commenters")
    most_commented_records: List[dict] = Field(default_factory=list, description="Records with most comments")


# Error response models following DRCR patterns
class CommentErrorResponse(BaseModel):
    """Error response for comment operations"""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Human-readable error message")
    details: Optional[dict] = Field(None, description="Additional error details")
    error_code: Optional[str] = Field(None, description="System error code for localization")