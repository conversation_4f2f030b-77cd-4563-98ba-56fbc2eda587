"""
Email Processing Service

Processes <PERSON><PERSON><PERSON> messages and stores them in Firestore
"""

import os
import logging
import re
from typing import List, Dict, Optional
from datetime import datetime
from google.cloud import firestore
from .nylas_service import NylasService

logger = logging.getLogger(__name__)

# Compile regex at module level for performance
ENTITY_EXTRACTION_PATTERN = re.compile(r'inbox\+([a-zA-Z0-9_-]+)@drcrlabs\.com', re.IGNORECASE)

class EmailProcessor:
    """Service for processing emails and storing in Firestore"""
    
    def __init__(self):
        project_id = os.getenv("GCP_PROJECT_ID")
        if not project_id:
            raise ValueError("GCP_PROJECT_ID must be set")
        
        self.db = firestore.Client(project=project_id)
        self.nylas_service = NylasService()
    
    def process_recent_emails(self) -> Dict:
        """
        Main entry point: Process recent emails using threads-first approach
        
        Returns:
            Dictionary with processing statistics
        """
        try:
            logger.info("Starting thread-based email processing")
            
            # 1. Fetch recent threads from <PERSON>ylas
            threads = self.nylas_service.fetch_recent_threads()
            
            if not threads:
                logger.info("No threads found in Nylas")
                return {
                    "processed_threads": 0,
                    "processed_messages": 0,
                    "skipped_threads": 0,
                    "error_count": 0,
                    "entities_affected": []
                }
            
            logger.info(f"Found {len(threads)} threads from Nylas")
            
            # 2. Process threads with entity filtering
            result = self.process_threads(threads)
            
            logger.info(f"Thread processing completed: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Error in process_recent_emails: {str(e)}")
            raise
    
    def process_threads(self, threads: List[Dict]) -> Dict:
        """
        Process threads and their messages with entity-based filtering
        
        Args:
            threads: List of thread dictionaries from Nylas API
            
        Returns:
            Dictionary with processing statistics
        """
        processed_threads = 0
        processed_messages = 0
        skipped_threads = 0
        error_count = 0
        entities_affected = set()
        
        logger.info(f"Processing {len(threads)} threads")
        
        for thread in threads:
            try:
                thread_id = thread.get('id')
                if not thread_id:
                    error_count += 1
                    continue
                
                # Check if this thread contains entity emails
                entity_id = self._extract_entity_from_thread(thread)
                if not entity_id:
                    skipped_threads += 1
                    continue
                
                # Check if entity exists
                if not self._entity_exists(entity_id):
                    skipped_threads += 1
                    continue
                
                # Process this thread
                thread_result = self._process_single_thread(thread_id, entity_id)
                
                if thread_result["status"] == "processed":
                    processed_threads += 1
                    processed_messages += thread_result["message_count"]
                    entities_affected.add(entity_id)
                elif thread_result["status"] == "skipped":
                    skipped_threads += 1
                else:
                    error_count += 1
                    
            except Exception as e:
                error_count += 1
                logger.error(f"Error processing thread {thread.get('id', 'unknown')}: {str(e)}")
        
        return {
            "processed_threads": processed_threads,
            "processed_messages": processed_messages,
            "skipped_threads": skipped_threads,
            "error_count": error_count,
            "entities_affected": list(entities_affected)
        }
    
    def process_messages(self, messages: List[Dict]) -> Dict:
        """
        Process a list of messages from Nylas with optimized batching
        
        Args:
            messages: List of message dictionaries from Nylas API
            
        Returns:
            Dictionary with processing statistics
        """
        
        processed_count = 0
        skipped_count = 0
        error_count = 0
        entities_affected = set()
        
        logger.info(f"Processing {len(messages)} messages")
        
        # 1. Extract all entity emails and IDs for batch processing
        entity_messages = []
        entity_ids = set()
        
        for message in messages:
            try:
                # Extract entity ID from all recipients
                entity_id = self._extract_entity_id_from_message(message)
                if entity_id:
                    entity_messages.append((message, entity_id))
                    entity_ids.add(entity_id)
                else:
                    skipped_count += 1
                    
            except Exception as e:
                error_count += 1
                logger.error(f"Error extracting entity from message {message.get('id', 'unknown')}: {str(e)}")
        
        if not entity_messages:
            logger.info("No entity emails found to process")
            return {
                "processed_count": 0,
                "skipped_count": skipped_count,
                "error_count": error_count,
                "entities_affected": []
            }
        
        # 2. Batch check which entities exist
        existing_entities = self._batch_check_entities_exist(list(entity_ids))
        logger.info(f"Found {len(existing_entities)} existing entities out of {len(entity_ids)}")
        
        # 3. Process messages for existing entities only
        for message, entity_id in entity_messages:
            try:
                if entity_id not in existing_entities:
                    skipped_count += 1
                    continue
                
                # Check if already processed (still individual check for now)
                if self._email_already_processed(entity_id, message.get('id')):
                    skipped_count += 1
                    continue
                
                # Store email metadata
                self._store_email_metadata(entity_id, message)
                processed_count += 1
                entities_affected.add(entity_id)
                
            except Exception as e:
                error_count += 1
                logger.error(f"Error processing message {message.get('id', 'unknown')}: {str(e)}")
        
        return {
            "processed_count": processed_count,
            "skipped_count": skipped_count, 
            "error_count": error_count,
            "entities_affected": list(entities_affected)
        }
    
    def _extract_entity_from_addresses(self, addresses: List[Dict]) -> Optional[str]:
        """Extract entity ID from list of email addresses"""
        
        for addr in addresses:
            email = addr.get('email', '')
            entity_id = self._extract_entity_id_from_email(email)
            if entity_id:
                return entity_id
        
        return None
    
    def _extract_entity_id_from_email(self, email_address: str) -> Optional[str]:
        """
        Extract entity <NAME_EMAIL> format
        
        Args:
            email_address: Email address to parse
            
        Returns:
            Entity ID string or None if no match
        """
        if not email_address:
            return None
        
        try:
            # <AUTHOR> <EMAIL>
            if "<" in email_address and ">" in email_address:
                email_address = email_address.split("<")[1].split(">")[0]
            
            # Extract entity ID using compiled regex
            match = ENTITY_EXTRACTION_PATTERN.match(email_address.strip())
            
            if match:
                entity_id = match.group(1)
                logger.debug(f"Extracted entity ID '{entity_id}' from '{email_address}'")
                return entity_id
            
            return None
            
        except Exception as e:
            logger.error(f"Error extracting entity ID from '{email_address}': {str(e)}")
            return None
    
    def _entity_exists(self, entity_id: str) -> bool:
        """Check if entity exists in ENTITIES collection"""
        
        try:
            entity_doc = self.db.collection('ENTITIES').document(entity_id).get()
            exists = entity_doc.exists
            
            if not exists:
                logger.debug(f"Entity {entity_id} not found in database")
            
            return exists
            
        except Exception as e:
            logger.error(f"Error checking entity existence for {entity_id}: {str(e)}")
            return False
    
    def _batch_check_entities_exist(self, entity_ids: List[str]) -> set:
        """
        Batch check which entities exist in ENTITIES collection
        
        Args:
            entity_ids: List of entity IDs to check
            
        Returns:
            Set of entity IDs that exist
        """
        if not entity_ids:
            return set()
        
        try:
            # Create document references for batch get
            doc_refs = [self.db.collection('ENTITIES').document(entity_id) for entity_id in entity_ids]
            
            # Batch get all documents
            docs = self.db.get_all(doc_refs)
            
            # Return set of existing entity IDs
            existing_entities = {doc.id for doc in docs if doc.exists}
            
            logger.debug(f"Batch entity check: {len(existing_entities)}/{len(entity_ids)} entities exist")
            return existing_entities
            
        except Exception as e:
            logger.error(f"Error in batch entity check: {str(e)}")
            # Fallback to individual checks
            existing_entities = set()
            for entity_id in entity_ids:
                if self._entity_exists(entity_id):
                    existing_entities.add(entity_id)
            return existing_entities
    
    def _extract_entity_id_from_message(self, message: Dict) -> Optional[str]:
        """
        Extract entity ID from message recipients
        
        Args:
            message: Nylas message dictionary
            
        Returns:
            Entity ID if found, None otherwise
        """
        # Check all recipient fields
        for field in ['to', 'cc', 'bcc']:
            recipients = message.get(field, [])
            for recipient in recipients:
                email_address = recipient.get('email', '')
                entity_id = self._extract_entity_id_from_email(email_address)
                if entity_id:
                    return entity_id
        return None
    
    def _extract_entity_from_thread(self, thread: Dict) -> Optional[str]:
        """
        Extract entity ID from thread data (checking thread participants)
        
        Args:
            thread: Nylas thread dictionary
            
        Returns:
            Entity ID if found, None otherwise
        """
        # Check participants in the thread for entity emails
        participants = thread.get('participants', [])
        for participant in participants:
            email_address = participant.get('email', '')
            entity_id = self._extract_entity_id_from_email(email_address)
            if entity_id:
                return entity_id
        return None
    
    def _process_single_thread(self, thread_id: str, entity_id: str) -> Dict:
        """
        Process a single thread: store thread metadata and all its messages
        
        Args:
            thread_id: Nylas thread ID
            entity_id: Entity ID extracted from thread
            
        Returns:
            Processing result dictionary
        """
        try:
            # Check if thread already processed
            if self._thread_already_processed(entity_id, thread_id):
                return {"status": "skipped", "reason": "already_processed", "message_count": 0}
            
            # Get all messages in this thread
            messages = self.nylas_service.get_thread_messages(thread_id)
            
            if not messages:
                logger.warning(f"No messages found for thread {thread_id}")
                return {"status": "skipped", "reason": "no_messages", "message_count": 0}
            
            # Store thread metadata
            self._store_thread_metadata(entity_id, thread_id, messages)
            
            # Store individual messages
            message_count = 0
            for message in messages:
                if not self._email_already_processed(entity_id, message.get('id')):
                    self._store_email_metadata(entity_id, message)
                    message_count += 1
            
            return {
                "status": "processed",
                "thread_id": thread_id,
                "message_count": message_count
            }
            
        except Exception as e:
            logger.error(f"Error processing thread {thread_id}: {str(e)}")
            return {"status": "error", "error": str(e), "message_count": 0}
    
    def _thread_already_processed(self, entity_id: str, thread_id: str) -> bool:
        """Check if thread already exists in Firestore"""
        try:
            thread_ref = self.db.collection('ENTITIES').document(entity_id).collection('EMAIL_THREADS').document(thread_id)
            return thread_ref.get().exists
        except Exception as e:
            logger.error(f"Error checking if thread exists: {str(e)}")
            return False
    
    def _store_thread_metadata(self, entity_id: str, thread_id: str, messages: List[Dict]) -> None:
        """Store thread-level metadata in Firestore"""
        try:
            if not messages:
                return
            
            # Analyze thread from messages
            first_message = min(messages, key=lambda m: m.get('date', float('inf')))
            latest_message = max(messages, key=lambda m: m.get('date', 0))
            
            # Extract thread participants
            participants = set()
            has_attachments = False
            total_attachments = 0
            
            for message in messages:
                # Add all participants
                for field in ['from', 'to', 'cc']:
                    addresses = message.get(field, [])
                    for addr in addresses:
                        if addr.get('email'):
                            participants.add(addr['email'])
                
                # Check for attachments
                attachments_list = message.get('attachments', [])
                if attachments_list:
                    has_attachments = True
                    total_attachments += len(attachments_list)
            
            # Process timestamps
            first_message_date = first_message.get('date', 0)
            latest_message_date = latest_message.get('date', 0)
            
            # Convert timestamps properly
            if first_message_date:
                first_message_at = datetime.utcfromtimestamp(first_message_date / 1000)
            else:
                first_message_at = datetime.utcnow()
                
            if latest_message_date:
                latest_message_at = datetime.utcfromtimestamp(latest_message_date / 1000)
            else:
                latest_message_at = datetime.utcnow()
            
            thread_data = {
                'thread_id': thread_id,
                'entity_id': entity_id,
                'subject': first_message.get('subject', ''),
                'participant_emails': list(participants),
                'message_count': len(messages),
                'has_attachments': has_attachments,
                'total_attachment_count': total_attachments,
                'first_message_at': first_message_at,
                'latest_message_at': latest_message_at,
                'thread_status': 'active',
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow(),
                'created_by': 'polling_system'
            }
            
            # Store in EMAIL_THREADS subcollection using create() for race safety
            thread_ref = self.db.collection('ENTITIES').document(entity_id).collection('EMAIL_THREADS').document(thread_id)
            try:
                thread_ref.create(thread_data)
            except Exception as e:
                if 'already exists' in str(e).lower():
                    logger.debug(f"Thread {thread_id} already exists, skipping")
                else:
                    raise
            
            logger.debug(f"Stored thread metadata for {thread_id} with {len(messages)} messages")
            
        except Exception as e:
            logger.error(f"Error storing thread metadata for {thread_id}: {str(e)}")
            raise
    
    def _email_already_processed(self, entity_id: str, email_id: str) -> bool:
        """Check if email already exists in Firestore"""
        
        try:
            email_ref = self.db.collection('ENTITIES').document(entity_id).collection('EMAILS').document(email_id)
            return email_ref.get().exists
            
        except Exception as e:
            logger.error(f"Error checking if email exists: {str(e)}")
            return False
    
    def _store_email_metadata(self, entity_id: str, message: Dict) -> None:
        """Store email metadata in Firestore subcollection"""
        
        try:
            email_id = message.get('id')
            
            # Extract sender information
            from_addresses = message.get('from', [])
            sender_email = from_addresses[0].get('email', '') if from_addresses else ''
            sender_name = from_addresses[0].get('name', sender_email) if from_addresses else 'Unknown'
            
            # Process date - Nylas returns milliseconds since epoch
            received_timestamp = message.get('date', 0)
            if received_timestamp:
                # Convert milliseconds to seconds for datetime.fromtimestamp()
                received_at = datetime.utcfromtimestamp(received_timestamp / 1000)
            else:
                received_at = datetime.utcnow()
            
            # Process attachments - trim metadata to avoid large documents
            attachments = message.get('attachments', [])
            has_attachments = bool(attachments)
            attachment_count = len(attachments)
            
            # Store only essential attachment metadata
            trimmed_attachments = []
            for attachment in attachments:
                trimmed_attachments.append({
                    'id': attachment.get('id'),
                    'filename': attachment.get('filename'),
                    'content_type': attachment.get('content_type'),
                    'size': attachment.get('size', 0)
                })
            
            # Prepare email document
            email_data = {
                'email_id': email_id,
                'entity_id': entity_id,
                'thread_id': message.get('thread_id'),
                'subject': message.get('subject', ''),
                'from_address': [addr.get('email', '') for addr in message.get('from', [])],
                'to_addresses': [addr.get('email', '') for addr in message.get('to', [])],
                'cc_addresses': [addr.get('email', '') for addr in message.get('cc', [])],
                'sender_name': sender_name,
                'received_at': received_at,
                'has_attachments': has_attachments,
                'attachment_count': attachment_count,
                'attachments': trimmed_attachments,
                'processing_status': 'pending',
                'metadata_sync_status': 'completed',
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow(),
                'created_by': 'polling_system',
                'error_message': None
            }
            
            # Store in Firestore using create() for race safety
            email_ref = self.db.collection('ENTITIES').document(entity_id).collection('EMAILS').document(email_id)
            try:
                email_ref.create(email_data)
            except Exception as e:
                if 'already exists' in str(e).lower():
                    logger.debug(f"Email {email_id} already exists, skipping")
                else:
                    raise
            
            logger.info(f"Stored email {email_id} for entity {entity_id}")
            
        except Exception as e:
            logger.error(f"Error storing email metadata: {str(e)}")
            raise