import React, { useMemo } from 'react';
import { AutocompletePopover, type AutocompleteItem } from './autocomplete-popover';
import { StatusIcon } from './status-icon';
import { EntityStatus } from '@/types/entity.types';

interface Entity {
  entityId: string;
  entityName: string;
  connectionStatus: string;
  connection_details?: {
    status: EntityStatus;
    error_message?: string;
  };
}

interface EntitySelectorProps {
  entities: Entity[];
  selectedEntityId: string | 'all' | null;
  onEntitySelect: (entityId: string) => void;
  isLoading?: boolean;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  showAllOption?: boolean;
  clientName?: string;
  isHydrated?: boolean;
}

// Helper function to get entity status with fallback
function getEntityStatus(entity: Entity): EntityStatus | 'unknown' {
  // Prioritize connection_details.status, fallback to connectionStatus
  const status = entity.connection_details?.status || entity.connectionStatus;
  return (status && Object.values(EntityStatus).includes(status as EntityStatus)) 
    ? status as EntityStatus 
    : 'unknown';
}

export function EntitySelector({
  entities,
  selectedEntityId,
  onEntitySelect,
  isLoading = false,
  disabled = false,
  placeholder = 'Select entity...',
  className,
  showAllOption = true,
  clientName,
  isHydrated = true,
}: EntitySelectorProps) {
  
  // Transform entities into AutocompleteItem format
  const items = useMemo(() => {
    const result: AutocompleteItem[] = [];
    
    // Add "All Entities" option if enabled
    if (showAllOption) {
      result.push({
        value: 'all',
        label: 'All Entities',
        metadata: 'Show all'
      });
    }
    
    // Add sorted entities with status indicators
    const sortedEntities = [...entities].sort((a, b) => a.entityName.localeCompare(b.entityName));
    
    sortedEntities.forEach(entity => {
      const status = getEntityStatus(entity);
      result.push({
        value: entity.entityId,
        label: entity.entityName,
        icon: <StatusIcon status={status} showTooltip={true} />,
        metadata: entity.connection_details?.status || entity.connectionStatus || 'Unknown',
        disabled: false,
      });
    });
    
    return result;
  }, [entities, showAllOption]);

  // Handle disabled state items
  const finalItems = disabled && entities.length === 0
    ? [{ value: 'disabled', label: 'Select a client first', disabled: true }]
    : items;

  return (
    <AutocompletePopover
      items={finalItems}
      selectedValue={isHydrated ? selectedEntityId : null}
      onSelect={onEntitySelect}
      placeholder={isHydrated ? placeholder : 'Loading...'}
      searchPlaceholder="Search entities..."
      emptyMessage="No entities found."
      groupHeading={clientName ? `Entities for ${clientName}` : 'Entities'}
      isLoading={isLoading || !isHydrated}
      disabled={disabled || !isHydrated}
      className={className}
      aria-label="Select entity"
      aria-busy={isLoading || !isHydrated}
      screenReaderLabel="Search for entity"
      buttonClassName="min-w-[150px]"
      showCheckmarks={true}
    />
  );
}