import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  MoreHorizontal, 
  Settings, 
  Trash2, 
  RefreshCw, 
  Wifi, 
  WifiOff, 
  AlertCircle,
  Building2,
  Eye,
  Link,
  Unlink,
  Loader2,
  Mail,
  Copy,
  Check
} from 'lucide-react';
import { EntityStatusBadge } from '@/components/ui/entity-status-badge';
import { useSyncStatusPolling } from '@/hooks/useSyncStatusPolling';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { CreateEntityModal } from '@/components/entities/CreateEntityModal';
import { OAuthCallback, useOAuthCallback } from '@/components/entities/OAuthCallback';
import { EntitySettingsManagement } from '@/components/entities/EntitySettingsManagement';
import { EntityDetailView } from '@/components/entities/EntityDetailView';
import { 
  DraggableDialog, 
  DraggableDialogContent, 
  DraggableDialogHeader, 
  DraggableDialogTitle, 
  DraggableDialogDescription,
  DraggableDialogFooter 
} from '@/components/ui/draggable-dialog';
import { EntitiesService } from '@/services/entities.service';
import { useClientStore } from '@/store/client.store';
import { useFirmName } from '@/hooks/useFirmName';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarTrigger } from '@/components/ui/sidebar';
import type { 
  EntitySummary, 
  EntityListResponse, 
  EntityType,
  EntityStatus
} from '@/types/entity.types';

export function EntityManagement() {
  const { clientId, entityId } = useParams();
  const navigate = useNavigate();
  const { isOAuthCallback } = useOAuthCallback();
  const { firmName, isLoading: firmNameLoading } = useFirmName();
  
  // State
  const [entities, setEntities] = useState<EntitySummary[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Extract entity IDs for sync polling
  const entityIds = entities.map(entity => entity.entity_id);
  
  // Use sync status polling hook - enabled but with graceful error handling
  const { syncStatuses, refreshSyncStatus } = useSyncStatusPolling(entityIds, {
    enabled: entityIds.length > 0 && !isLoading, // Re-enabled with error handling
    pollingInterval: 30000 // 30 seconds
  });

  // Console log sync statuses for debugging
  useEffect(() => {
    if (syncStatuses.size > 0) {
      console.log('=== SYNC STATUS DATA ===');
      syncStatuses.forEach((syncData, entityId) => {
        const entity = entities.find(e => e.entity_id === entityId);
        console.log(`Entity: ${entity?.entity_name || entityId} (${entity?.type || 'unknown'})`, {
          entityId,
          syncStatus: syncData.syncStatus,
          lastSync: syncData.lastSync,
          connectionStatus: syncData.connectionStatus
        });
      });
      console.log('========================');
    }
  }, [syncStatuses, entities]);

  // Helper function to check if entity is syncing
  const isEntitySyncing = useCallback((entity: EntitySummary) => {
    const entitySyncStatus = syncStatuses.get(entity.entity_id);
    return entity.connection_status === 'syncing' || 
           entity.sync_status?.is_syncing || 
           entitySyncStatus?.syncStatus?.is_syncing;
  }, [syncStatuses]);
  const [creditInfo, setCreditInfo] = useState<{
    credit_balance: number;
    credits_used_total: number;
    credits_used_openai: number;
    credits_used_mistral: number;
    last_credit_usage?: string;
  } | null>(null);
  
  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [selectedEntityForSettings, setSelectedEntityForSettings] = useState<EntitySummary | null>(null);
  
  // Entity settings save state
  const [entitySaveHandler, setEntitySaveHandler] = useState<(() => Promise<void>) | null>(null);
  const [canSaveEntity, setCanSaveEntity] = useState(false);
  const [isSavingEntity, setIsSavingEntity] = useState(false);
  
  
  // Copy to clipboard state
  const [copiedEmails, setCopiedEmails] = useState<Set<string>>(new Set());

  // Client store
  const { currentClient, fetchClient } = useClientStore();

  // Load client details when clientId changes
  useEffect(() => {
    if (clientId && (!currentClient || currentClient.client_id !== clientId)) {
      fetchClient(clientId);
    }
  }, [clientId, currentClient, fetchClient]);

  const loadEntities = useCallback(async () => {
    if (!clientId) return;
    
    try {
      setIsLoading(true);
      setError(null);
      const response = await EntitiesService.getEntitiesForClient(clientId, {});
      setEntities(response.entities);
      
      // Extract credit info if available
      if ((response as any).credit_info) {
        console.log('Credit info found:', (response as any).credit_info);
        setCreditInfo((response as any).credit_info);
      } else {
        console.log('No credit_info in response');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load entities');
    } finally {
      setIsLoading(false);
    }
  }, [clientId]);

  // Load entities when component mounts or clientId changes
  useEffect(() => {
    loadEntities();
  }, [loadEntities]);

  // No filtering needed - just show all entities for this client
  const filteredEntities = entities;

  // Handle OAuth callback
  if (isOAuthCallback) {
    return (
      <OAuthCallback
        entityId={entityId}
        onSuccess={(entityId, response) => {
          navigate(`/clients/${clientId}/entities/${entityId}`);
        }}
        onError={(error) => {
          setError(error);
          navigate(`/clients/${clientId}/entities`);
        }}
        onCancel={() => {
          navigate(`/clients/${clientId}/entities`);
        }}
      />
    );
  }


  const handleCreateEntity = () => {
    setShowCreateModal(true);
  };

  const handleEntityCreated = (entityId: string) => {
    loadEntities();
    // Optionally navigate to the new entity's data page
    // navigate(`/clients/${clientId}/entities/${entityId}`);
  };

  const handleViewEntity = (entity: EntitySummary) => {
    // Navigate to entity data explorer
    navigate(`/clients/${clientId}/entities/${entity.entity_id}`);
  };

  const handleEditEntity = (entity: EntitySummary) => {
    console.log('handleEditEntity called with entity:', entity);
    
    if (isEntitySyncing(entity)) {
      setError('Cannot open entity settings while sync is in progress. Please wait for sync to complete.');
      return;
    }
    
    setSelectedEntityForSettings(entity);
    setShowSettingsModal(true);
    console.log('Modal state set to true, selectedEntity:', entity.entity_name);
  };

  const handleDeleteEntity = async (entity: EntitySummary) => {
    if (!confirm(`Are you sure you want to delete "${entity.entity_name}"?`)) {
      return;
    }

    try {
      await EntitiesService.deleteEntity(entity.entity_id);
      loadEntities();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete entity');
    }
  };

  const handleSyncEntity = async (entity: EntitySummary) => {
    try {
      if (isEntitySyncing(entity)) {
        setError('Sync is already in progress for this entity. Please wait for it to complete.');
        return;
      }
      
      // Optimistically update the entity to show syncing status
      setEntities(prev => prev.map(e => 
        e.entity_id === entity.entity_id 
          ? { ...e, connection_status: 'syncing' as any }
          : e
      ));
      
      await EntitiesService.triggerSync(entity.entity_id);
      
      // Refresh sync status immediately after triggering sync
      await refreshSyncStatus(entity.entity_id);
      
      // Refresh the entities list after a delay to allow sync to complete
      setTimeout(() => {
        loadEntities();
      }, 2000);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to trigger sync');
      // Revert optimistic update on error
      loadEntities();
    }
  };

  const handleDisconnectEntity = async (entity: EntitySummary) => {
    if (!confirm(`Are you sure you want to disconnect "${entity.entity_name}"?`)) {
      return;
    }

    try {
      await EntitiesService.disconnectEntity(entity.entity_id);
      loadEntities();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to disconnect entity');
    }
  };

  const handleEntitySaveAction = (saveHandler: () => Promise<void>, canSave: boolean, isSaving: boolean) => {
    setEntitySaveHandler(() => saveHandler);
    setCanSaveEntity(canSave);
    setIsSavingEntity(isSaving);
  };

  const handleCopyInboxEmail = async (entityId: string) => {
    const inboxEmail = `inbox+${entityId}@drcrlabs.com`;
    try {
      await navigator.clipboard.writeText(inboxEmail);
      setCopiedEmails(prev => new Set([...prev, entityId]));
      toast.success('Email address copied to clipboard!');
      
      // Reset the copied state after 2 seconds
      setTimeout(() => {
        setCopiedEmails(prev => {
          const newSet = new Set(prev);
          newSet.delete(entityId);
          return newSet;
        });
      }, 2000);
    } catch (err) {
      toast.error('Failed to copy email address');
    }
  };

  // Memoize the settings fetch functions to prevent repeated API calls
  const handleFetchSettings = useCallback(async (entityId: string) => {
    const entity = await EntitiesService.getEntity(entityId, true);

    // Derive base currency: preference order → settings, entity.base_currency, country_code mapping, fallback USD
    const countryToCurrency: Record<string, string> = {
      'GB': 'GBP',
      'US': 'USD',
      'CA': 'CAD',
      'AU': 'AUD',
      'NZ': 'NZD',
      'IE': 'EUR',
      'DE': 'EUR',
      'FR': 'EUR',
      // add more as needed
    };

    // Settings may default to 'USD' when the field is absent; treat that as undefined
    const settingsCurrency = entity.settings?.base_currency_code;

    const derivedBaseCurrency =
      (settingsCurrency && settingsCurrency !== 'USD' ? settingsCurrency : undefined) ||
      (entity as any).base_currency ||
      countryToCurrency[(entity as any).country_code || ''] ||
      'GBP';
 
    return {
      entityId: entity.entity_id,
      entityName: entity.entity_name,
      prepaymentAssetAccountCodes: entity.settings?.prepayment_asset_account_codes || [],
      defaultExpenseAccountCode: entity.settings?.default_expense_account_code || null,
      defaultAmortizationMonths: entity.settings?.default_amortization_months || 12,
      amortizationMaterialityThreshold: entity.settings?.amortization_materiality_threshold || 1000,
      amortizationApply50PercentRule: entity.settings?.amortization_apply_50_percent_rule ?? true,
      autoSyncEnabled: entity.settings?.auto_sync_enabled ?? true,
      syncFrequency: entity.settings?.sync_frequency || 'daily',
      syncSpendMoney: entity.settings?.sync_spend_money ?? true,
      excludedPnlAccountCodes: entity.settings?.excluded_pnl_account_codes || [],
      transactionSyncStartDate: entity.settings?.transaction_sync_start_date || '',
      syncInvoices: entity.settings?.sync_invoices ?? true,
      syncBills: entity.settings?.sync_bills ?? true,
      syncPayments: entity.settings?.sync_payments ?? true,
      syncBankTransactions: entity.settings?.sync_bank_transactions ?? true,
      syncJournalEntries: entity.settings?.sync_journal_entries ?? true,
      autoPostProposedJournals: entity.settings?.auto_post_proposed_journals ?? false,
      aiScanningEnabled: entity.settings?.ai_scanning_enabled ?? false,
      baseCurrencyCode: derivedBaseCurrency
    };
  }, []);

  const handleFetchChartOfAccounts = useCallback(async (entityId: string) => {
    const accounts = await EntitiesService.getChartOfAccounts(entityId);
    return accounts;
  }, []);

  const handleSaveSettings = useCallback(async (entityId: string, settings: any) => {
    await EntitiesService.updateEntitySettings(entityId, {
      prepayment_asset_account_codes: settings.prepaymentAssetAccountCodes,
      default_expense_account_code: settings.defaultExpenseAccountCode,
      default_amortization_months: settings.defaultAmortizationMonths,
      amortization_materiality_threshold: settings.amortizationMaterialityThreshold,
      amortization_apply_50_percent_rule: settings.amortizationApply50PercentRule,
      auto_sync_enabled: settings.autoSyncEnabled,
      sync_frequency: settings.syncFrequency,
      sync_spend_money: settings.syncSpendMoney,
      excluded_pnl_account_codes: settings.excludedPnlAccountCodes,
      transaction_sync_start_date: settings.transactionSyncStartDate,
      sync_invoices: settings.syncInvoices,
      sync_bills: settings.syncBills,
      sync_payments: settings.syncPayments,
      sync_bank_transactions: settings.syncBankTransactions,
      sync_journal_entries: settings.syncJournalEntries,
      auto_post_proposed_journals: settings.autoPostProposedJournals,
      ai_scanning_enabled: settings.aiScanningEnabled
      // base_currency_code removed - should not be editable, comes from accounting system
    });
  }, []);

  // Helper function to get enhanced entity with sync status
  const getEntityWithSyncStatus = (entity: EntitySummary) => {
    const syncStatusData = syncStatuses.get(entity.entity_id);
    
    // If we have sync status data, use it. Otherwise, check if entity shows syncing connection status
    const enhancedEntity = {
      ...entity,
      sync_status: syncStatusData?.syncStatus || ('sync_status' in entity ? entity.sync_status : null)
    };
    
    // If connection_status is 'syncing', create a synthetic sync status
    if (entity.connection_status === 'syncing' && !enhancedEntity.sync_status) {
      enhancedEntity.sync_status = {
        is_syncing: true,
        current_step: 'Data synchronization in progress',
        progress_percentage: undefined,
        estimated_remaining: undefined,
        user_message: 'Sync in progress...'
      };
    }
    
    return enhancedEntity;
  };

  const renderEntityList = () => (
    <div className="space-y-4">

      {/* Credit Balance - Redesigned */}
      {creditInfo && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="flex flex-col">
                  <h4 className="font-medium text-gray-900">Document Processing Credits</h4>
                  <p className="text-xs text-gray-500">1 credit per document processed</p>
                </div>
              </div>
              <div className="flex items-center gap-4">
                {/* Progress Bar */}
                <div className="flex flex-col items-end">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-sm font-medium text-gray-700">
                      {creditInfo.credit_balance.toLocaleString()} of {(creditInfo.credit_balance + creditInfo.credits_used_total).toLocaleString()} remaining
                    </span>
                    <span className="text-xs text-gray-500">
                      ({Math.round((creditInfo.credits_used_total / (creditInfo.credit_balance + creditInfo.credits_used_total)) * 100)}% used)
                    </span>
                  </div>
                  <div className="w-40 h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div 
                      className={`h-full transition-all duration-300 ${
                        creditInfo.credit_balance > 1000 ? 'bg-green-500' :
                        creditInfo.credit_balance > 500 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ 
                        width: `${100 - (creditInfo.credits_used_total / (creditInfo.credit_balance + creditInfo.credits_used_total)) * 100}%` 
                      }}
                    />
                  </div>
                </div>
                {/* Status Indicator */}
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${
                    creditInfo.credit_balance > 1000 ? 'bg-green-500' :
                    creditInfo.credit_balance > 500 ? 'bg-yellow-500' : 'bg-red-500'
                  }`} />
                  <span className={`text-xs font-medium ${
                    creditInfo.credit_balance > 1000 ? 'text-green-700' :
                    creditInfo.credit_balance > 500 ? 'text-yellow-700' : 'text-red-700'
                  }`}>
                    {creditInfo.credit_balance > 1000 ? 'Good (1,000+ remaining)' :
                     creditInfo.credit_balance > 500 ? 'Low (under 1,000)' : 'Critical (under 500)'}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Entity List */}
      <div className="grid gap-4">
        {isLoading ? (
          [...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                  <div className="h-6 bg-gray-200 rounded w-1/2 mb-4"></div>
                  <div className="flex gap-2">
                    <div className="h-6 bg-gray-200 rounded w-20"></div>
                    <div className="h-6 bg-gray-200 rounded w-24"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : filteredEntities.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No entities found</h3>
              <p className="text-gray-500 mb-4">
                This client doesn't have any entities yet.
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredEntities.map((entity) => (
            <Card key={entity.entity_id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                {/* Header with Entity Name and Status */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <h3 className="text-lg font-semibold">{entity.entity_name}</h3>
                    <Badge variant="outline" className="text-xs">{entity.type ? entity.type.toUpperCase() : 'UNKNOWN TYPE'}</Badge>
                    <EntityStatusBadge 
                      entity={getEntityWithSyncStatus(entity)} 
                      syncStatus={syncStatuses.get(entity.entity_id)?.syncStatus}
                    />
                  </div>
                  <div className="flex items-center gap-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewEntity(entity)}
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      View
                    </Button>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span>
                          <Button
                            variant="outline"
                            size="sm"
                            disabled={isEntitySyncing(entity)}
                            onClick={() => handleEditEntity(entity)}
                          >
                            <Settings className="h-3 w-3 mr-1" />
                            Settings
                          </Button>
                        </span>
                      </TooltipTrigger>
                      <TooltipContent>
                        {isEntitySyncing(entity)
                          ? 'Settings unavailable during sync'
                          : 'Configure entity settings'
                        }
                      </TooltipContent>
                    </Tooltip>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm" className="px-2">
                          <MoreHorizontal className="h-3 w-3" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem 
                          disabled={isEntitySyncing(entity)}
                          onClick={() => handleSyncEntity(entity)}
                        >
                          <RefreshCw className={`h-4 w-4 mr-2 ${isEntitySyncing(entity) ? 'animate-spin' : ''}`} />
                          {isEntitySyncing(entity) ? 'Syncing...' : 'Sync Now'}
                        </DropdownMenuItem>
                        {entity.connection_status === 'active' && (
                          <DropdownMenuItem onClick={() => handleDisconnectEntity(entity)}>
                            <Unlink className="h-4 w-4 mr-2" />
                            Disconnect
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem 
                          onClick={() => handleDeleteEntity(entity)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>

                {/* Virtual Inbox - Simplified */}
                <div className="mb-4 p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-gray-600" />
                      <h4 className="font-medium text-gray-900">Email Documents for Processing</h4>
                    </div>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 text-gray-400"
                        >
                          <AlertCircle className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent className="max-w-xs">
                        <p className="text-sm">Send invoices, receipts, and bills (PDF, PNG, JPG) to this email. Documents will be processed automatically and appear in your prepayments dashboard within 2-5 minutes.</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                  <div className="flex items-center gap-2">
                    <code className="text-sm font-mono text-gray-700 bg-gray-50 px-2 py-1 rounded flex-1 select-all">
                      inbox+{entity.entity_id}@drcrlabs.com
                    </code>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleCopyInboxEmail(entity.entity_id)}
                      className="shrink-0"
                    >
                      {copiedEmails.has(entity.entity_id) ? (
                        <>
                          <Check className="h-3 w-3 mr-1 text-green-600" />
                          Copied!
                        </>
                      ) : (
                        <>
                          <Copy className="h-3 w-3 mr-1" />
                          Copy
                        </>
                      )}
                    </Button>
                  </div>
                </div>

                {/* Entity Status Info */}
                {(entity.pending_items_count !== undefined && entity.pending_items_count > 0) ||
                 (entity.error_count !== undefined && entity.error_count > 0) ? (
                  <div className="flex items-center gap-4 text-sm">
                    {entity.pending_items_count !== undefined && entity.pending_items_count > 0 && (
                      <span className="text-yellow-600 font-medium">{entity.pending_items_count} pending items</span>
                    )}
                    {entity.error_count !== undefined && entity.error_count > 0 && (
                      <span className="text-red-600 font-medium">{entity.error_count} errors</span>
                    )}
                  </div>
                ) : null}
                
                {entity.error_message && (
                  <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                    <strong>Error:</strong> {entity.error_message}
                  </div>
                )}
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );


  return (
    <>
      <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem className="hidden md:block">
              <BreadcrumbLink
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  navigate('/dashboard');
                }}
                className="cursor-pointer"
              >
                {firmNameLoading ? 'Loading...' : firmName}
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="hidden md:block" />
            <BreadcrumbItem>
              {entityId ? (
                <BreadcrumbLink
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    navigate(`/clients/${clientId}/entities`);
                  }}
                  className="cursor-pointer"
                >
                  {currentClient?.name ? `${currentClient.name} - Entities` : 'Entities'}
                </BreadcrumbLink>
              ) : (
                <BreadcrumbPage>
                  {currentClient?.name ? `${currentClient.name} - Entities` : 'Entity Management'}
                </BreadcrumbPage>
              )}
            </BreadcrumbItem>
            {entityId && (
              <>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Entity Details</BreadcrumbPage>
                </BreadcrumbItem>
              </>
            )}
          </BreadcrumbList>
        </Breadcrumb>
      </header>
      <div className="flex-1 overflow-auto">
        <div className="p-6">
          {entityId ? <EntityDetailView /> : renderEntityList()}
        </div>
      </div>

      {/* Create Entity Modal */}
      <CreateEntityModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        clientId={clientId || ''}
        clientName={currentClient?.name}
        onSuccess={handleEntityCreated}
      />

      {/* Entity Settings Modal */}
      {selectedEntityForSettings && (
        <DraggableDialog open={showSettingsModal} onOpenChange={setShowSettingsModal}>
          <DraggableDialogContent className="flex flex-col p-0 gap-0">
            <DraggableDialogHeader className="p-6 border-b flex-shrink-0">
              <DraggableDialogTitle>Entity Settings - {selectedEntityForSettings.entity_name}</DraggableDialogTitle>
              <DraggableDialogDescription>
                Configure prepayment and expense accounts for this entity.
              </DraggableDialogDescription>
            </DraggableDialogHeader>
            <div className="flex-1 overflow-y-auto p-6">
              <EntitySettingsManagement
                entityId={selectedEntityForSettings.entity_id}
                fetchSettings={handleFetchSettings}
                fetchChartOfAccounts={handleFetchChartOfAccounts}
                saveSettings={handleSaveSettings}
                onClose={() => setShowSettingsModal(false)}
                hideFooter={true}
                onSaveAction={handleEntitySaveAction}
              />
            </div>
            <DraggableDialogFooter className="p-6 border-t flex-shrink-0">
              <Button variant="outline" onClick={() => setShowSettingsModal(false)} disabled={isSavingEntity}>
                Cancel
              </Button>
              <Button
                onClick={entitySaveHandler || (() => {})}
                disabled={isSavingEntity || !canSaveEntity || !entitySaveHandler}
              >
                {isSavingEntity && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Save Settings
              </Button>
            </DraggableDialogFooter>
          </DraggableDialogContent>
        </DraggableDialog>
      )}
    </>
  );
}