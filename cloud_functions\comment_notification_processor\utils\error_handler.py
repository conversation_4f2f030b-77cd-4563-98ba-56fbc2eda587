"""
<PERSON>rro<PERSON> Handler Utility

Provides centralized error logging and monitoring for the comment notification processor.
Following DRCR patterns from existing cloud functions.
"""
import logging
from typing import Dict, Any, Optional
from datetime import datetime
from google.cloud import firestore

logger = logging.getLogger(__name__)


class ErrorHandler:
    """Centralized error handling and logging for comment notifications"""
    
    def __init__(self):
        self.db = None
        try:
            self.db = firestore.Client()
        except Exception as e:
            logger.warning(f"Could not initialize Firestore client for error logging: {str(e)}")
    
    def log_error(
        self,
        error_type: str,
        error_message: str,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Log error with context information
        
        Args:
            error_type: Type of error (e.g., 'comment_notification_failure')
            error_message: Error message or description
            context: Additional context information
        """
        try:
            # Log to console/Cloud Logging
            logger.error(f"[{error_type}] {error_message}", extra={
                "error_type": error_type,
                "context": context or {}
            })
            
            # Also log to Firestore for centralized monitoring if available
            if self.db:
                self._log_to_firestore(error_type, error_message, context)
                
        except Exception as e:
            # Don't let error logging itself cause failures
            logger.error(f"Failed to log error: {str(e)}")
    
    def _log_to_firestore(
        self,
        error_type: str,
        error_message: str,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        """Log error to Firestore ERROR_LOGS collection"""
        try:
            error_log_data = {
                "error_type": error_type,
                "error_message": error_message,
                "context": context or {},
                "timestamp": firestore.SERVER_TIMESTAMP,
                "source": "comment_notification_processor",
                "severity": "error"
            }
            
            # Add to ERROR_LOGS collection
            error_ref = self.db.collection("ERROR_LOGS").document()
            error_ref.set(error_log_data)
            
        except Exception as e:
            logger.warning(f"Failed to log error to Firestore: {str(e)}")
    
    def log_warning(
        self,
        warning_type: str,
        warning_message: str,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        """Log warning with context information"""
        try:
            logger.warning(f"[{warning_type}] {warning_message}", extra={
                "warning_type": warning_type,
                "context": context or {}
            })
            
        except Exception as e:
            logger.error(f"Failed to log warning: {str(e)}")
    
    def log_info(
        self,
        info_type: str,
        info_message: str,
        context: Optional[Dict[str, Any]] = None
    ) -> None:
        """Log informational message with context"""
        try:
            logger.info(f"[{info_type}] {info_message}", extra={
                "info_type": info_type,
                "context": context or {}
            })
            
        except Exception as e:
            logger.error(f"Failed to log info: {str(e)}")