from fastapi import APIRouter, Depends, HTTPException, status, Body, Request, BackgroundTasks, Query
from fastapi.security import OAuth2P<PERSON>word<PERSON>earer, OAuth2PasswordRequestForm
from typing import List, Dict, Any, Optional
from firebase_admin import auth as firebase_auth
from firebase_admin.exceptions import FirebaseError
import uuid
from google.cloud import firestore
from google.cloud.firestore import SERVER_TIMESTAMP
from datetime import timedelta
import os
import logging

from ..core.firebase_auth import get_current_user, get_firm_admin, AuthUser
from ..models.error_codes import AppErrorCode
from ..dependencies import get_db
from ..schemas.password_reset import (
    ForgotPasswordRequest, 
    ForgotPasswordResponse, 
    ResetPasswordRequest, 
    ResetPasswordResponse, 
    VerifyTokenResponse, 
    ErrorResponse
)
from ..services.password_reset_service import PasswordResetService
from ..services.email_service import EmailService

router = APIRouter(tags=["Authentication"])

logger = logging.getLogger(__name__)

# Initialize password reset service
def get_password_reset_service(db = Depends(get_db)) -> PasswordResetService:
    """Get password reset service instance"""
    return PasswordResetService(db)

@router.post("/forgot-password", response_model=ForgotPasswordResponse)
async def forgot_password(
    request: ForgotPasswordRequest,
    http_request: Request,
    background_tasks: BackgroundTasks,
    password_reset_service: PasswordResetService = Depends(get_password_reset_service)
):
    """
    Request password reset email
    
    This endpoint will:
    1. Validate the email exists in Firebase Auth
    2. Generate a secure reset token
    3. Store the token in Firestore
    4. Send an email with a reset link that redirects to the appropriate frontend URL
    """
    try:
        # Get client IP and user agent for logging
        client_ip = http_request.client.host if http_request.client else "unknown"
        user_agent = http_request.headers.get("user-agent", "unknown")
        
        # Detect the frontend URL from the request origin or referer
        frontend_url = detect_frontend_url(http_request)
        
        logger.info(f"Password reset requested for {request.email} from {client_ip} (User-Agent: {user_agent[:100]})")
        logger.info(f"Detected frontend URL: {frontend_url}")
        
        # Request password reset (this handles all validation and email sending)
        success = await password_reset_service.request_password_reset(
            email=request.email,
            client_ip=client_ip,
            user_agent=user_agent,
            frontend_url=frontend_url
        )
        
        if success:
            logger.info(f"Password reset email sent successfully to {request.email}")
            return ForgotPasswordResponse(
                message="If an account with this email exists, you will receive a password reset link shortly.",
                success=True
            )
        else:
            logger.warning(f"Password reset failed for {request.email}")
            # Still return success message for security (don't reveal if email exists)
            return ForgotPasswordResponse(
                message="If an account with this email exists, you will receive a password reset link shortly.",
                success=True
            )
            
    except Exception as e:
        logger.error(f"Error in forgot password endpoint for {request.email}: {e}")
        raise HTTPException(
            status_code=500,
            detail="An error occurred while processing your request. Please try again later."
        )

def detect_frontend_url(request: Request) -> str:
    """
    Detect the appropriate frontend URL based on the request origin
    
    Priority:
    1. Origin header (for CORS requests)
    2. Referer header (for form submissions)
    3. Host header with protocol detection
    4. Environment variable fallback
    """
    
    # Check Origin header first (most reliable for CORS requests)
    origin = request.headers.get("origin")
    if origin:
        logger.info(f"Using Origin header: {origin}")
        return origin
    
    # Check Referer header
    referer = request.headers.get("referer")
    if referer:
        # Extract base URL from referer
        from urllib.parse import urlparse
        parsed = urlparse(referer)
        base_url = f"{parsed.scheme}://{parsed.netloc}"
        logger.info(f"Using Referer header: {base_url}")
        return base_url
    
    # Check Host header and detect protocol
    host = request.headers.get("host")
    if host:
        # Detect if it's localhost or development
        if "localhost" in host or "127.0.0.1" in host or host.startswith("192.168."):
            protocol = "http"  # Local development typically uses HTTP
        else:
            protocol = "https"  # Production should use HTTPS
        
        base_url = f"{protocol}://{host}"
        logger.info(f"Using Host header: {base_url}")
        return base_url
    
    # Fallback to environment variable
    fallback_url = os.getenv("FRONTEND_BASE_URL", "https://app.drcrlabs.com")
    logger.info(f"Using fallback URL: {fallback_url}")
    return fallback_url

@router.post("/reset-password", response_model=ResetPasswordResponse, responses={
    400: {"model": ErrorResponse, "description": "Invalid token or password"},
    500: {"model": ErrorResponse, "description": "Internal server error"}
})
async def reset_password(
    reset_request: ResetPasswordRequest,
    db = Depends(get_db)
):
    """Reset password using a valid token"""
    # Initialize password reset service
    reset_service = PasswordResetService(db)
    
    # Reset password
    success, message = await reset_service.reset_password(
        token=reset_request.token,
        new_password=reset_request.new_password
    )
    
    if not success:
        # Determine appropriate error code based on message
        if "invalid or expired" in message.lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={"error": "invalid_token", "message": message}
            )
        elif "8 characters" in message.lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={"error": "invalid_password", "message": message}
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={"error": "password_reset_failed", "message": message}
            )
    
    return ResetPasswordResponse(message=message)

@router.get("/verify-reset-token/{token}", response_model=VerifyTokenResponse)
async def verify_reset_token(
    token: str,
    db = Depends(get_db)
):
    """Verify if a reset token is valid"""
    logger.info(f"API endpoint called: /verify-reset-token/{token[:10]}...")
    
    # Initialize password reset service
    reset_service = PasswordResetService(db)
    
    # Verify token
    is_valid, token_data = await reset_service.verify_reset_token(token)
    
    if is_valid and token_data:
        logger.info(f"Token verification successful. Returning valid response for email: {token_data.get('email')}")
        return VerifyTokenResponse(
            valid=True,
            email=token_data.get("email"),
            expires_at=token_data.get("expires_at")
        )
    else:
        logger.warning(f"Token verification failed. Returning invalid response for token: {token[:10]}...")
        return VerifyTokenResponse(
            valid=False,
            error="Token is invalid or expired"
        )

@router.post("/register-firm")
async def register_firm(
    firm_data: Dict[str, Any] = Body(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Register a new firm and set the current user as firm_admin"""
    # Check if user already has a firm association
    if current_user.firm_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User already associated with a firm"
        )

    # Create new firm ID
    firm_id = str(uuid.uuid4())

    # Create firm document
    firm = {
        "firm_id": firm_id,
        "name": firm_data.get("name", "New Firm"),
        "status": "active",
        "created_at": SERVER_TIMESTAMP,
        "updated_at": SERVER_TIMESTAMP,
        **{k: v for k, v in firm_data.items() if k not in ["firm_id", "created_at", "updated_at"]}
    }

    # Create firm_user document for the admin
    firm_user = {
        "user_id": current_user.uid,
        "firm_id": firm_id,
        "email": current_user.email,
        "display_name": current_user.display_name or current_user.email.split("@")[0],
        "role": "firm_admin",
        "assigned_client_ids": [],
        "status": "active",
        "created_at": SERVER_TIMESTAMP,
        "updated_at": SERVER_TIMESTAMP,
    }

    # Get references to Firestore documents
    firm_ref = db.collection("FIRMS").document(firm_id)
    firm_user_ref = db.collection("FIRM_USERS").document()

    # Check if firm already exists (by name) before starting transaction
    existing_firms_query = db.collection("FIRMS").where(filter=firestore.FieldFilter("name", "==", firm["name"])).limit(1)
    existing_firms = await existing_firms_query.get()
    if len(existing_firms) > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="A firm with this name already exists"
        )

    # Create documents directly (without transaction for now)
    try:
        # Create firm document
        await firm_ref.set(firm)

        # Create firm user document
        await firm_user_ref.set(firm_user)
    except Exception as e:
        # If there's an error, try to clean up
        try:
            await firm_ref.delete()
        except:
            pass
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create firm: {str(e)}"
        )

    return {
        "message": "Firm registered successfully",
        "firm_id": firm_id,
        "user_role": "firm_admin"
    }

@router.post("/invite-user")
async def invite_user(
    request: Request,
    user_data: Dict[str, Any] = Body(...),
    current_user: AuthUser = Depends(get_firm_admin),
    db = Depends(get_db)
):
    """Invite a user to the firm (firm_admin only)"""
    email = user_data.get("email")
    role = user_data.get("role", "firm_staff")
    assigned_client_ids = user_data.get("assigned_client_ids", [])

    if not email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email is required"
        )

    if role not in ["firm_admin", "firm_staff"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid role. Must be firm_admin or firm_staff."
        )

    # Check if clients exist and user has access to them
    if assigned_client_ids:
        for client_id in assigned_client_ids:
            client_ref = db.collection("CLIENTS").document(client_id)
            client_doc = await client_ref.get()
            if not client_doc.exists or client_doc.to_dict()["firm_id"] != current_user.firm_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid client ID: {client_id}"
                )

    # Check if user already exists in Firebase
    try:
        firebase_user = firebase_auth.get_user_by_email(email)
        user_id = firebase_user.uid
    except FirebaseError:
        # Create user in Firebase if they don't exist
        try:
            firebase_user = firebase_auth.create_user(
                email=email,
                email_verified=False,
                password=str(uuid.uuid4()),  # Random password which user will reset
                display_name=user_data.get("display_name", email.split("@")[0])
            )
            user_id = firebase_user.uid

        except FirebaseError as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create user: {str(e)}"
            )

    # Send invitation email with proper token
    try:
        email_service = EmailService()
        password_reset_service = PasswordResetService(db)
        frontend_url = detect_frontend_url(request)
        
        # Generate invitation token (without sending email)
        invitation_token = await password_reset_service.generate_invitation_token(
            email=email,
            client_ip="system",  # System-generated invitation
            user_agent="DRCR-Backend-Invitation",
            frontend_url=frontend_url
        )
        
        if invitation_token:
            # Get the firm name for the invitation email
            firm_ref = db.collection("FIRMS").document(current_user.firm_id)
            firm_doc = await firm_ref.get()
            firm_name = "Your Firm"
            if firm_doc.exists:
                firm_data = firm_doc.to_dict()
                firm_name = firm_data.get("name", "Your Firm")
            
            # Create proper setup link with token
            setup_link = f"{frontend_url}/reset-password?token={invitation_token}"
            
            # Send invitation email with token
            invitation_sent = await email_service.send_invitation_email(
                to_email=email,
                user_name=user_data.get("display_name", email.split("@")[0]),
                inviter_name=current_user.display_name or current_user.email,
                firm_name=firm_name,
                setup_link=setup_link,
                expiry_time="24 hours"
            )
            
            if invitation_sent:
                logger.info(f"Invitation email sent successfully to {email} with token")
            else:
                logger.warning(f"Failed to send invitation email to {email}")
        else:
            logger.warning(f"Failed to generate invitation token for {email}")
        
        logger.info(f"Invitation email process completed for {email}")
            
    except Exception as e:
        logger.error(f"Error sending invitation email to {email}: {e}")
        # Continue anyway, user was created successfully

    # Create or update FIRM_USERS document
    firm_user_ref = db.collection("FIRM_USERS").where(filter=firestore.FieldFilter("user_id", "==", user_id)).limit(1)
    firm_user_docs = await firm_user_ref.get()

    firm_user_data = {
        "user_id": user_id,
        "firm_id": current_user.firm_id,
        "email": email,
        "display_name": user_data.get("display_name", email.split("@")[0]),
        "role": role,
        "assigned_client_ids": assigned_client_ids,
        "status": "invited",
        "updated_at": SERVER_TIMESTAMP
    }

    if len(firm_user_docs) > 0:
        # Update existing user
        firm_user_doc = firm_user_docs[0]
        await firm_user_doc.reference.update(firm_user_data)
    else:
        # Create new user
        firm_user_data["created_at"] = SERVER_TIMESTAMP
        await db.collection("FIRM_USERS").add(firm_user_data)

    return {
        "message": "User invited successfully",
        "user_id": user_id,
        "email": email,
        "role": role
    }

@router.post("/users/{user_id}/resend-invite")
async def resend_user_invite(
    user_id: str,
    request: Request,
    current_user: AuthUser = Depends(get_firm_admin),
    db = Depends(get_db)
):
    """Resend invitation email to a user (firm_admin only)"""
    
    # Get the user from FIRM_USERS collection
    firm_user_ref = db.collection("FIRM_USERS").where(filter=firestore.FieldFilter("user_id", "==", user_id)).where(filter=firestore.FieldFilter("firm_id", "==", current_user.firm_id)).limit(1)
    firm_user_docs = await firm_user_ref.get()
    
    if len(firm_user_docs) == 0:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found in your firm"
        )
    
    firm_user_doc = firm_user_docs[0]
    firm_user_data = firm_user_doc.to_dict()
    
    # Check if user is in invited status
    if firm_user_data.get("status") != "invited":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Can only resend invites to users with 'invited' status"
        )
    
    email = firm_user_data.get("email")
    if not email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User email not found"
        )
    
    # Send invitation email with proper token
    try:
        email_service = EmailService()
        password_reset_service = PasswordResetService(db)
        frontend_url = detect_frontend_url(request)
        
        # Generate invitation token (without sending email)
        invitation_token = await password_reset_service.generate_invitation_token(
            email=email,
            client_ip="system",  # System-generated invitation
            user_agent="DRCR-Backend-Resend-Invitation",
            frontend_url=frontend_url
        )
        
        if invitation_token:
            # Get the firm name for the invitation email
            firm_ref = db.collection("FIRMS").document(current_user.firm_id)
            firm_doc = await firm_ref.get()
            firm_name = "Your Firm"
            if firm_doc.exists:
                firm_data = firm_doc.to_dict()
                firm_name = firm_data.get("name", "Your Firm")
            
            # Create proper setup link with token
            setup_link = f"{frontend_url}/reset-password?token={invitation_token}"
            
            # Send invitation email with token
            invitation_sent = await email_service.send_invitation_email(
                to_email=email,
                user_name=firm_user_data.get("display_name", email.split("@")[0]),
                inviter_name=current_user.display_name or current_user.email,
                firm_name=firm_name,
                setup_link=setup_link,
                expiry_time="24 hours"
            )
            
            if invitation_sent:
                # Update the updated_at timestamp to track when invite was resent
                await firm_user_doc.reference.update({
                    "updated_at": SERVER_TIMESTAMP,
                    "last_invite_sent": SERVER_TIMESTAMP
                })
                
                logger.info(f"Invitation email resent to {email} with token")
                
                return {
                    "message": "Invitation resent successfully",
                    "email": email
                }
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to send invitation email"
                )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate invitation token"
            )
            
    except Exception as e:
        logger.error(f"Error resending invitation email to {email}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to resend invitation"
        )

@router.get("/me")
async def get_user_profile(
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Get current user profile with firm details"""
    # Base user profile
    profile = {
        "uid": current_user.uid,
        "email": current_user.email,
        "display_name": current_user.display_name,
        "firm_id": current_user.firm_id,
        "client_id": current_user.client_id,
        "role": current_user.role,
        "assigned_client_ids": current_user.assigned_client_ids
    }

    # Add firm name if user has a firm association
    if current_user.firm_id:
        try:
            firm_ref = db.collection("FIRMS").document(current_user.firm_id)
            firm_doc = await firm_ref.get()
            if firm_doc.exists:
                firm_data = firm_doc.to_dict()
                profile["firm_name"] = firm_data.get("name")
        except Exception as e:
            logger.warning(f"Could not fetch firm name for user {current_user.uid}: {e}")
            # Don't fail the request if firm name fetch fails

    return profile

@router.put("/me")
async def update_user_profile(
    profile_data: Dict[str, Any] = Body(...),
    current_user: AuthUser = Depends(get_current_user),
    db = Depends(get_db)
):
    """Update current user profile"""
    display_name = profile_data.get("display_name")

    if not display_name:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Display name is required"
        )

    try:
        # Update display name in Firebase Auth
        firebase_auth.update_user(
            current_user.uid,
            display_name=display_name
        )

        # Update display name in FIRM_USERS collection if user is a firm user
        if current_user.firm_id:
            firm_user_ref = db.collection("FIRM_USERS").where(filter=firestore.FieldFilter("user_id", "==", current_user.uid)).limit(1)
            firm_user_docs = await firm_user_ref.get()

            if len(firm_user_docs) > 0:
                firm_user_doc = firm_user_docs[0]
                await firm_user_doc.reference.update({
                    "display_name": display_name,
                    "updated_at": SERVER_TIMESTAMP
                })

        # Update display name in CLIENT_USERS collection if user is a client user
        if current_user.client_id:
            client_user_ref = db.collection("CLIENT_USERS").where(filter=firestore.FieldFilter("user_id", "==", current_user.uid)).limit(1)
            client_user_docs = await client_user_ref.get()

            if len(client_user_docs) > 0:
                client_user_doc = client_user_docs[0]
                await client_user_doc.reference.update({
                    "display_name": display_name,
                    "updated_at": SERVER_TIMESTAMP
                })

        return {
            "message": "Profile updated successfully",
            "display_name": display_name
        }

    except FirebaseError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update profile: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update profile"
        )

@router.put("/change-password")
async def change_password(
    password_data: Dict[str, Any] = Body(...),
    current_user: AuthUser = Depends(get_current_user)
):
    """Change user password"""
    new_password = password_data.get("new_password")

    if not new_password:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="New password is required"
        )

    if len(new_password) < 8:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password must be at least 8 characters long"
        )

    try:
        # Update password in Firebase Auth
        firebase_auth.update_user(
            current_user.uid,
            password=new_password
        )

        return {
            "message": "Password updated successfully"
        }

    except FirebaseError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update password: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update password"
        )

# ============================================================================
# USER MANAGEMENT ENDPOINTS (Firm Admin Only)
# ============================================================================

@router.get("/users")
async def list_firm_users(
    search: Optional[str] = Query(None, description="Search query for filtering users by name or email"),
    limit: Optional[int] = Query(None, description="Maximum number of users to return"),
    current_user: AuthUser = Depends(get_firm_admin),
    db = Depends(get_db)
):
    """List all users in the current firm (firm_admin only) with optional search and limit"""
    try:
        # Query all users in the current firm
        firm_users_ref = db.collection("FIRM_USERS").where(filter=firestore.FieldFilter("firm_id", "==", current_user.firm_id))
        firm_users_docs = await firm_users_ref.get()
        
        users = []
        for doc in firm_users_docs:
            user_data = doc.to_dict()
            
            # Get additional user info from Firebase Auth if available
            try:
                firebase_user = firebase_auth.get_user(user_data["user_id"])
                last_sign_in = firebase_user.user_metadata.last_sign_in_timestamp
                email_verified = firebase_user.email_verified
            except FirebaseError:
                last_sign_in = None
                email_verified = False
            
            user = {
                "user_id": user_data["user_id"],
                "firebase_uid": user_data["user_id"],  # Add firebase_uid for mention compatibility
                "uid": user_data["user_id"],  # Add uid alias for mention compatibility
                "email": user_data["email"],
                "display_name": user_data.get("display_name"),
                "role": user_data["role"],
                "status": user_data.get("status", "active"),
                "assigned_client_ids": user_data.get("assigned_client_ids", []),
                "created_at": user_data.get("created_at"),
                "updated_at": user_data.get("updated_at"),
                "last_login": user_data.get("last_login"),
                "last_sign_in": last_sign_in,
                "email_verified": email_verified,
                "document_id": doc.id
            }
            
            # Apply search filter if provided
            if search:
                search_lower = search.lower()
                display_name = user.get("display_name", "").lower()
                email = user.get("email", "").lower()
                
                # Skip if doesn't match search query
                if search_lower not in display_name and search_lower not in email:
                    continue
            
            users.append(user)
        
        # Sort by creation date (newest first)
        users.sort(key=lambda x: x.get("created_at") or datetime.min, reverse=True)
        
        # Apply limit if provided
        if limit and limit > 0:
            users = users[:limit]
        
        return {
            "users": users,
            "total_count": len(users),
            "firm_id": current_user.firm_id
        }
        
    except Exception as e:
        logger.error(f"Error listing firm users: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve users"
        )

@router.get("/users/{user_id}")
async def get_user_details(
    user_id: str,
    current_user: AuthUser = Depends(get_firm_admin),
    db = Depends(get_db)
):
    """Get detailed information about a specific user (firm_admin only)"""
    try:
        # Find the user in the current firm
        firm_user_ref = db.collection("FIRM_USERS").where(filter=firestore.FieldFilter("user_id", "==", user_id)).where(filter=firestore.FieldFilter("firm_id", "==", current_user.firm_id)).limit(1)
        firm_user_docs = await firm_user_ref.get()
        
        if not firm_user_docs:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found in your firm"
            )
        
        user_doc = firm_user_docs[0]
        user_data = user_doc.to_dict()
        
        # Get additional user info from Firebase Auth
        try:
            firebase_user = firebase_auth.get_user(user_id)
            firebase_data = {
                "email_verified": firebase_user.email_verified,
                "disabled": firebase_user.disabled,
                "creation_time": firebase_user.user_metadata.creation_time,
                "last_sign_in_time": firebase_user.user_metadata.last_sign_in_timestamp,
                "last_refresh_time": firebase_user.user_metadata.last_refresh_time,
                "provider_data": [
                    {
                        "provider_id": provider.provider_id,
                        "uid": provider.uid,
                        "email": provider.email
                    } for provider in firebase_user.provider_data
                ]
            }
        except FirebaseError as e:
            logger.warning(f"Could not fetch Firebase user data for {user_id}: {e}")
            firebase_data = {}
        
        # Get assigned client details if any
        assigned_clients = []
        if user_data.get("assigned_client_ids"):
            for client_id in user_data["assigned_client_ids"]:
                try:
                    client_doc = await db.collection("CLIENTS").document(client_id).get()
                    if client_doc.exists:
                        client_data = client_doc.to_dict()
                        assigned_clients.append({
                            "client_id": client_id,
                            "name": client_data.get("name"),
                            "status": client_data.get("status")
                        })
                except Exception as e:
                    logger.warning(f"Could not fetch client {client_id}: {e}")
        
        return {
            "user_id": user_data["user_id"],
            "email": user_data["email"],
            "display_name": user_data.get("display_name"),
            "role": user_data["role"],
            "status": user_data.get("status", "active"),
            "assigned_client_ids": user_data.get("assigned_client_ids", []),
            "assigned_clients": assigned_clients,
            "created_at": user_data.get("created_at"),
            "updated_at": user_data.get("updated_at"),
            "last_login": user_data.get("last_login"),
            "firebase_data": firebase_data,
            "document_id": user_doc.id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user details for {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user details"
        )

@router.put("/users/{user_id}/role")
async def update_user_role(
    user_id: str,
    role_data: Dict[str, Any] = Body(...),
    current_user: AuthUser = Depends(get_firm_admin),
    db = Depends(get_db)
):
    """Update a user's role and client assignments (firm_admin only)"""
    new_role = role_data.get("role")
    assigned_client_ids = role_data.get("assigned_client_ids", [])
    
    if not new_role:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Role is required"
        )
    
    if new_role not in ["firm_admin", "firm_staff"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid role. Must be firm_admin or firm_staff."
        )
    
    # Prevent user from removing their own admin role if they're the only admin
    if user_id == current_user.uid and new_role != "firm_admin":
        # Check if there are other firm admins
        admin_users_ref = db.collection("FIRM_USERS").where(filter=firestore.FieldFilter("firm_id", "==", current_user.firm_id)).where(filter=firestore.FieldFilter("role", "==", "firm_admin"))
        admin_users_docs = await admin_users_ref.get()
        
        if len(admin_users_docs) <= 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot remove admin role from the last firm admin"
            )
    
    try:
        # Find the user in the current firm
        firm_user_ref = db.collection("FIRM_USERS").where(filter=firestore.FieldFilter("user_id", "==", user_id)).where(filter=firestore.FieldFilter("firm_id", "==", current_user.firm_id)).limit(1)
        firm_user_docs = await firm_user_ref.get()
        
        if not firm_user_docs:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found in your firm"
            )
        
        user_doc = firm_user_docs[0]
        
        # Validate assigned client IDs if provided
        if assigned_client_ids:
            for client_id in assigned_client_ids:
                client_ref = db.collection("CLIENTS").document(client_id)
                client_doc = await client_ref.get()
                if not client_doc.exists or client_doc.to_dict()["firm_id"] != current_user.firm_id:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Invalid client ID: {client_id}"
                    )
        
        # Update the user's role and assignments
        update_data = {
            "role": new_role,
            "assigned_client_ids": assigned_client_ids,
            "updated_at": SERVER_TIMESTAMP
        }
        
        await user_doc.reference.update(update_data)
        
        return {
            "message": "User role updated successfully",
            "user_id": user_id,
            "role": new_role,
            "assigned_client_ids": assigned_client_ids
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user role for {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user role"
        )

@router.put("/users/{user_id}/status")
async def update_user_status(
    user_id: str,
    status_data: Dict[str, Any] = Body(...),
    current_user: AuthUser = Depends(get_firm_admin),
    db = Depends(get_db)
):
    """Update a user's status (activate/deactivate) (firm_admin only)"""
    new_status = status_data.get("status")
    
    if not new_status:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Status is required"
        )
    
    if new_status not in ["active", "inactive", "invited", "suspended"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid status. Must be active, inactive, invited, or suspended."
        )
    
    # Prevent user from deactivating themselves if they're the only admin
    if user_id == current_user.uid and new_status != "active":
        # Check if there are other active firm admins
        admin_users_ref = db.collection("FIRM_USERS").where(filter=firestore.FieldFilter("firm_id", "==", current_user.firm_id)).where(filter=firestore.FieldFilter("role", "==", "firm_admin")).where(filter=firestore.FieldFilter("status", "==", "active"))
        admin_users_docs = await admin_users_ref.get()
        
        active_admins = [doc for doc in admin_users_docs if doc.to_dict()["user_id"] != user_id]
        if len(active_admins) == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot deactivate the last active firm admin"
            )
    
    try:
        # Find the user in the current firm
        firm_user_ref = db.collection("FIRM_USERS").where(filter=firestore.FieldFilter("user_id", "==", user_id)).where(filter=firestore.FieldFilter("firm_id", "==", current_user.firm_id)).limit(1)
        firm_user_docs = await firm_user_ref.get()
        
        if not firm_user_docs:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found in your firm"
            )
        
        user_doc = firm_user_docs[0]
        
        # Update the user's status
        update_data = {
            "status": new_status,
            "updated_at": SERVER_TIMESTAMP
        }
        
        await user_doc.reference.update(update_data)
        
        # Also disable/enable the user in Firebase Auth if deactivating/activating
        try:
            if new_status == "inactive" or new_status == "suspended":
                firebase_auth.update_user(user_id, disabled=True)
            elif new_status == "active":
                firebase_auth.update_user(user_id, disabled=False)
        except FirebaseError as e:
            logger.warning(f"Could not update Firebase Auth status for {user_id}: {e}")
            # Continue anyway, as the Firestore update succeeded
        
        return {
            "message": "User status updated successfully",
            "user_id": user_id,
            "status": new_status
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user status for {user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user status"
        )

@router.delete("/users/{user_id}")
async def remove_user_from_firm(
    user_id: str,
    current_user: AuthUser = Depends(get_firm_admin),
    db = Depends(get_db)
):
    """Remove a user from the firm (firm_admin only)"""
    
    # Prevent user from removing themselves if they're the only admin
    if user_id == current_user.uid:
        # Check if there are other firm admins
        admin_users_ref = db.collection("FIRM_USERS").where(filter=firestore.FieldFilter("firm_id", "==", current_user.firm_id)).where(filter=firestore.FieldFilter("role", "==", "firm_admin"))
        admin_users_docs = await admin_users_ref.get()
        
        active_admins = [doc for doc in admin_users_docs if doc.to_dict()["user_id"] != user_id]
        if len(active_admins) == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot remove the last firm admin"
            )
    
    try:
        # Find the user in the current firm
        firm_user_ref = db.collection("FIRM_USERS").where(filter=firestore.FieldFilter("user_id", "==", user_id)).where(filter=firestore.FieldFilter("firm_id", "==", current_user.firm_id)).limit(1)
        firm_user_docs = await firm_user_ref.get()
        
        if not firm_user_docs:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found in your firm"
            )
        
        user_doc = firm_user_docs[0]
        user_data = user_doc.to_dict()
        
        # Delete the FIRM_USERS document
        await user_doc.reference.delete()
        
        # Note: We don't delete the Firebase Auth user as they might be used in other firms
        # or have other data associated with them
        
        return {
            "message": "User removed from firm successfully",
            "user_id": user_id,
            "email": user_data.get("email")
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error removing user {user_id} from firm: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove user from firm"
        )