import React, { useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Search, PlusCircle } from 'lucide-react';
import { DashboardFilterSelector } from '@/components/ui/dashboard-filter-selector';
import { CLIENT_STATUS_OPTIONS, CONNECTION_STATUS_OPTIONS, ENTITY_TYPE_OPTIONS } from '@/constants/dashboard-filters';
import type { DashboardFilters } from '../types';
import type { ClientSummary } from '@/lib/api';

interface DashboardFiltersProps {
  filters: DashboardFilters;
  onFiltersChange: (filters: Partial<DashboardFilters>) => void;
  onAddNewClient: () => void;
  isAdmin?: boolean;
  clientSummaries?: ClientSummary[];
}

export function DashboardFiltersComponent({ 
  filters, 
  onFiltersChange, 
  onAddNewClient, 
  isAdmin = true,
  clientSummaries = []
}: DashboardFiltersProps) {
  
  const getFilterOptionsWithCounts = useMemo(() => {
    // Count client statuses
    const statusCounts: Record<string, number> = {};
    const connectionCounts: Record<string, number> = {};
    const entityTypeCounts: Record<string, number> = {};
    
    clientSummaries.forEach(client => {
      // Count client statuses (based on client.status or derived from entities)
      const clientStatus = client.status || 'ok'; // Default to 'ok' if no status
      statusCounts[clientStatus] = (statusCounts[clientStatus] || 0) + 1;
      
      // Count connection statuses and entity types from entities
      client.entities?.forEach(entity => {
        // Connection status
        const connectionStatus = entity.connection_status || 'disconnected';
        connectionCounts[connectionStatus] = (connectionCounts[connectionStatus] || 0) + 1;
        
        // Entity type
        const entityType = entity.type || 'manual';
        entityTypeCounts[entityType] = (entityTypeCounts[entityType] || 0) + 1;
      });
    });
    
    const statusOptions = CLIENT_STATUS_OPTIONS.map(option => ({
      ...option,
      count: statusCounts[option.value] || 0
    }));

    const connectionOptions = CONNECTION_STATUS_OPTIONS.map(option => ({
      ...option,
      count: connectionCounts[option.value] || 0
    }));

    const entityTypeOptions = ENTITY_TYPE_OPTIONS.map(option => ({
      ...option,
      count: entityTypeCounts[option.value] || 0
    }));

    return [...statusOptions, ...connectionOptions, ...entityTypeOptions];
  }, [clientSummaries]);

  return (
    <div className="flex flex-row gap-2 items-center p-2 border-b bg-white flex-shrink-0 rounded-lg shadow-sm">
      {/* New multi-select filter */}
      <DashboardFilterSelector
        selectedFilters={filters.statusFilters}
        onFiltersChange={(statusFilters) => onFiltersChange({ statusFilters })}
        filterOptions={getFilterOptionsWithCounts}
      />

      <div className="flex-grow"></div>

      <div className="relative">
        <Search className="absolute left-2 top-2 h-3 w-3 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search clients..."
          value={filters.clientFilter}
          onChange={(e) => onFiltersChange({ clientFilter: e.target.value })}
          className="pl-6 w-[300px] h-8 text-xs"
        />
      </div>
      <div className="flex items-center space-x-2">
        {isAdmin && (
          <Button onClick={onAddNewClient} className="h-8 text-xs">
            <PlusCircle className="mr-1 h-3 w-3" /> Add Client
          </Button>
        )}
      </div>
    </div>
  );
} 