import React, { useState, useEffect } from 'react';
import { api } from '@/lib/api';
import type { Account } from '@/lib/api';

// --- Shadcn/UI & Lucide Imports ---
import { Button } from '@/components/ui/button';
import {
    DraggableDialog,
    DraggableDialogContent,
    DraggableDialogDescription,
    DraggableDialogFooter,
    DraggableDialogHeader,
    DraggableDialogTitle,
    DraggableDialogClose,
} from '@/components/ui/draggable-dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import {
    Loader2,
    X,
    Check,
    AlertCircle,
    Info,
    ExternalLink,
} from 'lucide-react';

// (Amortization schedule display removed)

// --- Type Definitions ---
type MonthlyBreakdownItem = { 
    status: 'proposed' | 'posted' | 'posting_error' | 'matched_manual' | 'skipped' | 'posting'; 
    amount: number; 
    journalId?: string; 
    error?: string; 
};

type ScheduleSummary = {
    status: 'proposed' | 'partially_posted' | 'fully_posted' | 'skipped' | 'error_posting';
    originalAmount: number;
    amortizationStartDate: string;
    amortizationEndDate: string;
    numberOfPeriods: number;
    amortizationAccountCode: string;
    expenseAccountCode: string | null;
};

type LineItem = {
    lineItemId: string;
    itemCode?: string;
    description: string;
    quantity?: number;
    unitPrice?: number;
    accountCode?: string;
    taxType?: string;
    lineAmount: number;
};

type TransactionDetails = {
    transactionId: string;
    reference: string;
    counterpartyName: string;
    hasAttachment: boolean;
    attachmentId?: string;
    invoiceDate?: string;
    dueDate?: string;
    subtotal?: number;
    taxTotal?: number;
    totalAmount?: number;
    xeroUrl?: string;
    lineItems: LineItem[];
    currencyCode: string;
};

// --- Component Props ---
interface SideBySideReviewProps {
    isOpen: boolean;
    onClose: () => void;
    transactionId: string | null;
    transactionData: TransactionDetails | null;
    scheduleData: ScheduleSummary | null; // kept for possible future use
    attachmentUrl: string | null;
    isLoading: boolean;
    error: string | null;
    entityId?: string; // Added to fetch chart of accounts
}

// --- Side-by-Side Review Component ---
export function SideBySideReview({
    isOpen,
    onClose,
    transactionId,
    transactionData,
    scheduleData,
    attachmentUrl,
    isLoading,
    error,
    entityId
}: SideBySideReviewProps) {

    const [blobUrl, setBlobUrl] = useState<string | null>(null);
    const [attachmentLoading, setAttachmentLoading] = useState(false);
    const [chartOfAccounts, setChartOfAccounts] = useState<Account[]>([]);
    const [accountsLoading, setAccountsLoading] = useState(false);

    // Helper function to get account name from code
    const getAccountName = (accountCode: string): string => {
        if (!accountCode) return '-';
        const account = chartOfAccounts.find(acc => acc.code === accountCode);
        return account ? `${accountCode} - ${account.name}` : accountCode;
    };

    // Helper function to get readable tax rate
    const getTaxDescription = (taxType: string): string => {
        if (!taxType) return '-';
        // Add common tax type mappings
        const taxMappings: { [key: string]: string } = {
            'NONE': 'No Tax',
            'OUTPUT2': 'Standard Rate (20%)',
            'OUTPUT': 'Standard Rate',
            'EXEMPTINPUT': 'Exempt',
            'EXEMPTOUTPUT': 'Exempt',
            'INPUT2': 'Input VAT (20%)',
            'INPUT': 'Input VAT',
            'ZERORATED': 'Zero Rated',
            'RRINPUT': 'Reverse Charge',
            'RROUTPUT': 'Reverse Charge Output'
        };
        return taxMappings[taxType] || taxType;
    };

    // Fetch chart of accounts when modal opens
    useEffect(() => {
        if (isOpen && entityId && chartOfAccounts.length === 0) {
            const fetchAccounts = async () => {
                setAccountsLoading(true);
                try {
                    const { accounts } = await api.getEntityAccounts(entityId);
                    setChartOfAccounts(accounts);
                } catch (err) {
                    console.error('Failed to fetch chart of accounts:', err);
                    // Don't show error to user, just log it
                } finally {
                    setAccountsLoading(false);
                }
            };
            fetchAccounts();
        }
    }, [isOpen, entityId, chartOfAccounts.length]);

    // Format dates for display
    const formatDate = (dateString: string | undefined | null) => {
        if (!dateString) return 'N/A';
        try {
            return new Date(dateString).toLocaleDateString();
        } catch (e) {
            return 'Invalid Date';
        }
    };

    // Determine and load the attachment preview URL.
    // Priority:
    // 1. If a pre-signed `attachmentUrl` prop is provided, use it directly.
    // 2. Otherwise, try to fetch a blob URL from the server via `attachmentId`.
    useEffect(() => {
        // Exit early if the dialog isn't open – ensures we don't waste work when hidden.
        if (!isOpen) {
            return;
        }

        // Clear any existing preview first.
        setBlobUrl(null);

        const loadAttachment = async () => {
            // If the parent already supplied a pre-signed URL we can use it immediately.
            if (attachmentUrl) {
                setBlobUrl(attachmentUrl);
                return; // Nothing else to do.
            }

            // Fall back to fetching via attachmentId.
            if (!transactionData?.attachmentId) {
                return; // Neither URL nor ID available – nothing to preview.
            }

            setAttachmentLoading(true);
            try {
                const url = await api.getAttachmentBlob(transactionData.attachmentId);
                setBlobUrl(url);
                console.log('Attachment blob URL created:', url);
            } catch (error) {
                console.error('Error fetching attachment:', error);
                setBlobUrl(null);
            } finally {
                setAttachmentLoading(false);
            }
        };

        loadAttachment();

        // Cleanup blob URL when component unmounts or when dependencies change.
        return () => {
            if (blobUrl && blobUrl.startsWith('blob:')) {
                URL.revokeObjectURL(blobUrl);
            }
        };
    }, [attachmentUrl, transactionData?.attachmentId, isOpen]);

    // Cleanup blob URL when modal closes
    useEffect(() => {
        if (!isOpen && blobUrl) {
            URL.revokeObjectURL(blobUrl);
            setBlobUrl(null);
        }
    }, [isOpen, blobUrl]);

    return (
        <DraggableDialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
            <DraggableDialogContent className="flex flex-col p-0 gap-0">
                <DraggableDialogHeader className="p-0 h-0 border-b-0">
                    {/* Visually hidden title & description for screen-reader accessibility */}
                    <DraggableDialogTitle className="sr-only">Invoice side-by-side review</DraggableDialogTitle>
                    <DraggableDialogDescription className="sr-only">PDF preview on the left and original invoice details on the right.</DraggableDialogDescription>
                </DraggableDialogHeader>

                {/* Main Content Area */}
                <div className="flex-grow grid grid-cols-1 md:grid-cols-2 gap-0 overflow-y-auto">

                    {/* Loading State */}
                    {isLoading && (
                        <div className="col-span-1 md:col-span-2 p-6 flex flex-col items-center justify-center space-y-4">
                            <Loader2 className="h-12 w-12 animate-spin text-blue-600" />
                            <p className="text-muted-foreground">Loading review data...</p>
                        </div>
                    )}

                    {/* Error State */}
                    {!isLoading && error && (
                        <div className="col-span-1 md:col-span-2 p-4">
                            <Alert variant="destructive">
                                <AlertCircle className="h-4 w-4" />
                                <AlertTitle>Error Loading Data</AlertTitle>
                                <AlertDescription>{error}</AlertDescription>
                            </Alert>
                        </div>
                    )}

                    {/* Data Display State */}
                    {!isLoading && !error && transactionData && (
                        <>
                            {/* Left Column: Attachment Preview */}
                            <div className="border-r flex flex-col">
                                {/* Removed 'Source Document' heading */}
                                <div className="flex-grow p-1 bg-gray-200">
                                    {attachmentLoading ? (
                                        <div className="flex items-center justify-center h-full text-gray-500 min-h-[400px] md:min-h-[600px]">
                                            <div className="text-center space-y-2">
                                                <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                                                <span>Loading attachment...</span>
                                            </div>
                                        </div>
                                    ) : blobUrl ? (
                                        <div className="w-full h-full min-h-[400px] md:min-h-[600px] relative">
                                            {/* Try iframe first for PDF display */}
                                            <iframe
                                                src={blobUrl}
                                                className="w-full h-full border-0"
                                                title="Attachment Preview"
                                                onLoad={() => console.log('Iframe loaded successfully')}
                                                onError={(e) => {
                                                    console.log('Iframe failed, trying embed');
                                                    const target = e.target as HTMLIFrameElement;
                                                    target.style.display = 'none';
                                                    const embedElement = target.nextElementSibling as HTMLEmbedElement;
                                                    if (embedElement) {
                                                        embedElement.style.display = 'block';
                                                    }
                                                }}
                                            />
                                            {/* Fallback to embed */}
                                            <embed
                                                src={blobUrl}
                                                type="application/pdf"
                                                className="w-full h-full absolute top-0 left-0"
                                                style={{ display: 'none' }}
                                                onLoad={() => console.log('PDF embed loaded successfully')}
                                                onError={(e) => {
                                                    console.log('PDF embed failed, showing fallback');
                                                    const target = e.target as HTMLEmbedElement;
                                                    target.style.display = 'none';
                                                    const fallback = target.nextElementSibling as HTMLElement;
                                                    if (fallback) {
                                                        fallback.style.display = 'flex';
                                                    }
                                                }}
                                            />
                                            {/* Final fallback */}
                                            <div className="attachment-fallback absolute inset-0 flex items-center justify-center bg-gray-200" style={{ display: 'none' }}>
                                                <div className="text-center space-y-4">
                                                    <div className="text-6xl">📄</div>
                                                    <div>
                                                        <p className="text-lg font-medium text-gray-700 mb-2">Preview Not Available</p>
                                                        <Button 
                                                            onClick={() => window.open(blobUrl, '_blank')}
                                                            className="bg-blue-600 hover:bg-blue-700 text-white"
                                                        >
                                                            Open in New Tab
                                                        </Button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="flex items-center justify-center h-full text-gray-500 min-h-[400px] md:min-h-[600px]">
                                            {transactionData.hasAttachment ? (
                                                <div className="text-center space-y-2">
                                                    <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                                                    <span>Loading attachment...</span>
                                                </div>
                                            ): (
                                                <div className="text-center space-y-2">
                                                    <Info className="h-6 w-6 mx-auto" />
                                                    <span>No attachment available.</span>
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Right Column: Line Items & Schedule Summary */}
                            <ScrollArea className="h-full">
                                <div className="p-3 space-y-4">
                                    {/* Invoice Meta (now on right side) */}
                                    {transactionData && (
                                        <div className="text-xs space-y-1">
                                            {/* We will insert button on top flex */}
                                        </div>
                                    )}
                                    {transactionData && (
                                        <div className="flex justify-between items-start mb-2">
                                            <div className="text-xs space-y-1">
                                                <div><span className="font-medium">Supplier:</span> {transactionData.counterpartyName}</div>
                                                {transactionData.reference && <div><span className="font-medium">Reference:</span> {transactionData.reference}</div>}
                                                {transactionData.invoiceDate && <div><span className="font-medium">Date:</span> {formatDate(transactionData.invoiceDate)}</div>}
                                                {transactionData.dueDate && <div><span className="font-medium">Due:</span> {formatDate(transactionData.dueDate)}</div>}
                                            </div>
                                            {transactionData.xeroUrl && (
                                                <Button
                                                    variant="link"
                                                    className="p-0 text-blue-600 hover:underline inline-flex items-center gap-1 text-xs"
                                                    onClick={() => window.open(transactionData.xeroUrl, '_blank')}
                                                >
                                                    <ExternalLink className="h-3 w-3" /> View in Xero
                                                </Button>
                                            )}
                                        </div>
                                    )}
                                    {transactionData && (
                                        <div className="text-xs space-y-1">
                                            {/* Included vs Excluded Lines */}
                                            <div>
                                                <h4 className="font-medium mb-2 text-sm">Original Line Items:</h4>
                                                <div className="border rounded-md">
                                                    <Table>
                                                        <TableHeader>
                                                            <TableRow>
                                                                <TableHead>Item Code</TableHead>
                                                                <TableHead>Description</TableHead>
                                                                <TableHead className="text-right">Qty</TableHead>
                                                                <TableHead className="text-right">Unit Price</TableHead>
                                                                <TableHead>Chart of Accounts</TableHead>
                                                                <TableHead>Tax Rate</TableHead>
                                                                <TableHead className="text-right">Amount</TableHead>
                                                            </TableRow>
                                                        </TableHeader>
                                                        <TableBody>
                                                            {transactionData.lineItems.length > 0 ? (
                                                                transactionData.lineItems.map((line) => (
                                                                    <TableRow key={line.lineItemId}>
                                                                        <TableCell className="align-top whitespace-nowrap">{line.itemCode || '-'}</TableCell>
                                                                        <TableCell className="align-top">{line.description || '(No description)'}</TableCell>
                                                                        <TableCell className="text-right align-top">{line.quantity ?? '-'}</TableCell>
                                                                        <TableCell className="text-right align-top">{line.unitPrice !== undefined ? line.unitPrice.toFixed(2) : '-'}</TableCell>
                                                                        <TableCell className="align-top" title={line.accountCode}>{getAccountName(line.accountCode || '')}</TableCell>
                                                                        <TableCell className="align-top" title={line.taxType}>{getTaxDescription(line.taxType || '')}</TableCell>
                                                                        <TableCell className="text-right align-top">{line.lineAmount.toFixed(2)}</TableCell>
                                                                    </TableRow>
                                                                ))
                                                            ) : (
                                                                <TableRow>
                                                                    <TableCell colSpan={7} className="text-center text-muted-foreground">No line items found.</TableCell>
                                                                </TableRow>
                                                            )}
                                                        </TableBody>
                                                    </Table>
                                                </div>
                                            </div>

                                            {/* Bill totals under table */}
                                            {transactionData && (
                                                <div className="text-xs text-right space-y-1 pt-2 border-t">
                                                    {transactionData.subtotal !== undefined && <div><span className="font-medium">Subtotal:</span> {transactionData.subtotal.toFixed(2)} {transactionData.currencyCode}</div>}
                                                    {transactionData.taxTotal !== undefined && <div><span className="font-medium">Tax:</span> {transactionData.taxTotal.toFixed(2)} {transactionData.currencyCode}</div>}
                                                    {transactionData.totalAmount !== undefined && <div><span className="font-medium">Total:</span> {transactionData.totalAmount.toFixed(2)} {transactionData.currencyCode}</div>}
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </div>
                            </ScrollArea>
                        </>
                    )}
                </div>

                {/* Footer with Close Button */}
                <DraggableDialogFooter className="p-4 border-t">
                    <DraggableDialogClose asChild>
                        <Button variant="outline" onClick={onClose}>Close</Button>
                    </DraggableDialogClose>
                </DraggableDialogFooter>
            </DraggableDialogContent>
        </DraggableDialog>
    );
} 