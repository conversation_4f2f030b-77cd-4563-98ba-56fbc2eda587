import React from 'react';
import { Card, CardContent, Card<PERSON>eader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Calendar, DollarSign, Clock, AlertCircle, CheckCircle2 } from 'lucide-react';
import { ScheduleStatus } from '../../types/schedule.types';
import { getScheduleStatusConfig, isTerminalStatus, isActionNeededStatus } from '../../types/schedule.types';

interface AmortizationSummaryProps {
  totalAmount: number;
  monthlyAmount: number;
  numberOfPeriods: number;
  startDate: string;
  endDate: string;
  status: ScheduleStatus;
  selectedBillsCount: number;
  currencyCode?: string;
  selectedCount?: number;
  method?: string;
}

export function AmortizationSummary({
  totalAmount,
  monthlyAmount,
  numberOfPeriods,
  startDate,
  endDate,
  status,
  selectedBillsCount,
  currencyCode = 'GBP',
  selectedCount = 1,
  method = 'equal_monthly',
}: AmortizationSummaryProps) {
  const isBulkMode = selectedCount > 1;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: currencyCode,
    }).format(amount);
  };

  // Helper functions to get method-specific text
  const getPaymentMethodText = () => {
    switch (method) {
      case 'day_based':
        return 'Prorated payments';
      case 'equal_monthly':
      default:
        return 'Equal payments';
    }
  };

  const getAmortizationMethodText = () => {
    switch (method) {
      case 'day_based':
        return `Day-based amortization over ${numberOfPeriods} periods`;
      case 'equal_monthly':
      default:
        return `Straight line amortization over ${numberOfPeriods} periods`;
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const getStatusBadge = () => {
    const statusConfig = getScheduleStatusConfig(status);
    
    return (
      <Badge 
        variant={statusConfig.variant}
        className={statusConfig.className}
      >
        {statusConfig.label}
      </Badge>
    );
  };

  const getDateRange = () => {
    if (!startDate || !endDate) return 'No dates selected';
    const start = formatDate(startDate);
    const end = formatDate(endDate);
    return `${start} - ${end}`;
  };

  if (selectedBillsCount === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Schedule Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <AlertCircle className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p className="text-lg font-medium mb-2">No Bills Selected</p>
            <p className="text-sm">Select bills from the left sidebar to see amortization summary</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="h-5 w-5" />
          Schedule Summary
          {isBulkMode && (
            <span className="ml-2 text-blue-600 text-sm font-normal">
              (Bulk mode - {selectedCount} bills)
            </span>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Amortized Amount */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <DollarSign className="h-4 w-4" />
              Amortized Amount
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {formatCurrency(totalAmount)}
            </div>
            <div className="text-xs text-gray-500">
              {selectedBillsCount} bill{selectedBillsCount !== 1 ? 's' : ''} selected
            </div>
          </div>

          {/* Period */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Calendar className="h-4 w-4" />
              Period
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {numberOfPeriods} months
            </div>
            <div className="text-xs text-gray-500">
              {getDateRange()}
            </div>
          </div>

          {/* Monthly Amount */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Clock className="h-4 w-4" />
              Monthly Amount
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {formatCurrency(monthlyAmount)}
            </div>
            <div className="text-xs text-gray-500">
              {getPaymentMethodText()}
            </div>
          </div>

          {/* Status */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <CheckCircle2 className="h-4 w-4" />
              Status
            </div>
            <div className="flex items-center">
              {getStatusBadge()}
            </div>
            <div className="text-xs text-gray-500">
              {status === ScheduleStatus.PENDING_CONFIGURATION
                ? 'Needs configuration'
                : status === ScheduleStatus.PROPOSED
                ? 'Ready for review'
                : status === ScheduleStatus.CONFIRMED
                ? 'Configuration complete'
                : status === ScheduleStatus.POSTED || status === ScheduleStatus.PARTIALLY_POSTED
                ? 'Schedule completed'
                : status === ScheduleStatus.CANCELLED || status === ScheduleStatus.SKIPPED || status === ScheduleStatus.ERROR
                ? 'Schedule terminated'
                : 'In progress'
              }
            </div>
          </div>
        </div>

        {/* Additional Summary Info */}
        {totalAmount > 0 && (
          <div className="mt-6 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">
                {getAmortizationMethodText()}
              </span>
              <span className="font-medium">
                {formatCurrency(monthlyAmount)} per month
              </span>
            </div>
            {isBulkMode && (
              <div className="mt-2 text-xs text-blue-600 bg-blue-50 p-2 rounded">
                <strong>Bulk Mode:</strong> Combined view of {selectedCount} bills. Individual amounts cannot be edited.
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}