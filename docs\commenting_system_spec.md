# DRCR Commenting System – Detailed Specification

## 1. Purpose
Provide a reusable, client-scoped commenting feature that can be attached to **any record** in the DRCR platform (amortization schedules, invoices, manual journals, rules, etc.).  The same infrastructure will later power internal discussions, audit notes, and support threads.

## 2. High-Level Design
```
┌──────────────┐    REST/HTTPS     ┌────────────────────┐
│ Front-end UI │ ────────────────▶│   FastAPI Service   │
└──────────────┘                  └──────────┬─────────┘
                                              │
                                              │ create / read / update / delete
                                              ▼
                                      🗄️ Firestore – COMMENTS
                                              │ (Pub/Sub trigger)
                                              ▼
                                      🔔 Cloud Functions – Mentions ➞ USER_NOTIFICATIONS / email / Slack
```

## 3. Firestore Model (Option A)
Collection: **COMMENTS**
| Field                | Type      | Notes                                                     |
|----------------------|-----------|-----------------------------------------------------------|
| `comment_id` (docId) | string    | UUID v4                                                   |
| `parent_type`        | string    | e.g. `"schedule"`, `"invoice"`, `"transaction"`         |
| `parent_id`          | string    | ID of the record being commented on                       |
| `client_id`          | string    |  🔒  used in security rules                                |
| `entity_id`          | string    |  (optional)                                               |
| `text`               | string    | raw Markdown/plain-text (max 5 000 chars)                 |
| `mentions`           | string[]  | array of **UIDs** extracted from comment body (`≤20`)     |
| `created_by`         | string    | author UID                                                |
| `created_at`         | timestamp | serverTimestamp                                           |
| `updated_at`         | timestamp | nullable, set on edit                                     |
| `deleted`            | bool      | soft-delete flag                                          |
| `reply_to_comment_id`| string    | nullable → threaded replies (phase 2)                     |

### Composite Indexes
```
(parent_type ASC, parent_id ASC, created_at DESC)
(client_id ASC, created_at DESC)            // for "my client" streams
(mentions ARRAY_CONTAINS, created_at DESC)  // mentions inbox
```

## 4. Security Rules (draft)
```firebase
match /COMMENTS/{commentId} {
  allow read:   if isUserInClient(resource.data.client_id);

  allow create: if request.auth != null
                && request.resource.data.client_id == getUserClientId()
                && request.resource.data.text.size() <= 5000
                && request.resource.data.mentions.size() <= 20;

  allow update: if request.auth != null
                && resource.data.created_by == request.auth.uid
                && request.resource.data.text.size() <= 5000;

  allow delete: if request.auth != null
                && resource.data.created_by == request.auth.uid;
}
```

## 5. REST API
Base path: `/comments`
| Verb | Path                                   | Description                            |
|------|----------------------------------------|----------------------------------------|
| POST | `/`                                    | Create a comment                       |
| GET  | `/{parent_type}/{parent_id}`           | List comments for a record             |
| PUT  | `/{comment_id}`                        | Edit own comment                       |
| DELETE | `/{comment_id}`                      | Soft-delete (sets `deleted=true`)      |

### 5.1 Create Comment – Request
```jsonc
POST /comments
{
  "parent_type": "schedule",
  "parent_id":   "8e2…",
  "text":        "Great catch @alex.patel — I fixed the date range.",
  "mention_uids": ["uid_1234"],    // optional, supplied by UI autocomplete
}
```
FastAPI schema will:
1. Verify parent record exists (optional, best-effort).
2. Verify caller has access to the same `client_id`.
3. Derive `mentions` from `mention_uids` (or parse `@` tokens as fallback).
4. Persist document.
5. Publish *mention* notifications.

### 5.2 List Comments – Response (truncated)
```jsonc
[
  {
    "comment_id": "abc-123",
    "text": "…",
    "mentions": ["uid_1234"],
    "created_by": "uid_me",
    "created_at": "2024-06-22T12:34:56Z"
  }
]
```

## 6. Mention Notification Flow
1. Cloud Function `onCommentWrite` triggers on `create` and `update`.
2. Compute **newly added** mentions `(new.mentions − old.mentions)`.
3. For each UID: write a doc in `/USERS/{uid}/NOTIFICATIONS`, send email/Slack if that channel is enabled.

Notification payload example:
```jsonc
{
  "type": "mention",
  "comment_id": "abc-123",
  "parent_type": "schedule",
  "parent_id": "8e2…",
  "snippet": "Great catch …",
  "read": false,
  "created_at": "2024-06-22T12:34:57Z"
}
```

## 7. Front-End Components
### 7.1 useComments Hook
```ts
const {
  comments,
  addComment,
  editComment,
  deleteComment
} = useComments(parentType, parentId);
```
– Implemented with React-Query + optimistic updates.

### 7.2 CommentsPanel
Reusable panel: list, composer, @mention autocomplete.  Drop-in usage:
```tsx
<CommentsPanel parentType="schedule" parentId={scheduleId} />
```

### 7.3 Mention Autocomplete
• On “@” trigger, call `/users/search?q=alex` (existing endpoint) and show up to 10 matches.  
• Insert `@displayName` in textarea and track `{displayName, uid}` pair in local state.  
• `mention_uids` array sent alongside `text`.

## 8. Road-Map / Future Enhancements
| Phase | Feature                         | Notes |
|-------|---------------------------------|-------|
| 2     | Threaded replies                | utilise `reply_to_comment_id` field |
| 2     | Edit history / audit trail      | store revisions sub-collection      |
| 3     | Reactions (👍, ✅, …)           | separate `COMMENT_REACTIONS` coll.  |
| 3     | Markdown rendering (bold, code) | sanitize server-side to avoid XSS   |
| 4     | File attachments                | GCS signed-URL workflow             |
| 4     | Cross-record search             | BigQuery export or Elastic index    |

## 9. Implementation Timeline (est.)
| Task                                   | Dev-days |
|----------------------------------------|----------|
| Firestore rules + indexes              | 0.5      |
| FastAPI routes + unit tests            | 1        |
| Cloud Function for mentions            | 0.5      |
| Front-end CommentsPanel w/ autocomplete| 1        |
| Integration on Bills screen            | 0.5      |
| **Total MVP**                          | **3.5**  |

---
*Last updated – 2025-07-22* 

## 10. Detailed Backend Contracts
### 10.1 Pydantic Schemas (FastAPI)
```python
# rest_api/schemas/comment_schemas.py
from pydantic import BaseModel, Field, constr
from typing import List, Optional

class CommentCreate(BaseModel):
    parent_type: constr(strip_whitespace=True, min_length=2, max_length=40)
    parent_id:   constr(strip_whitespace=True, min_length=2, max_length=60)
    text:        constr(strip_whitespace=True, min_length=1, max_length=5000)
    mention_uids: Optional[List[str]] = Field(default_factory=list, max_items=20)

class CommentOut(BaseModel):
    comment_id: str
    parent_type: str
    parent_id: str
    client_id: str
    text: str
    mentions: List[str]
    created_by: str
    created_at: str  # ISO8601
    updated_at: Optional[str]
    deleted: bool
```
All endpoints return `CommentOut` or List[`CommentOut`].  Date fields are always ISO-8601 strings to keep the API JSON-serialisation symmetrical with Firestore timestamps.

### 10.2 REST Error Codes
| HTTP | Code  | When                                            |
|------|-------|-------------------------------------------------|
| 400  | C001  | `parent_type` unsupported                       |
| 400  | C002  | `mention_uids` contains foreign-client user     |
| 403  | C003  | Caller lacks access to parent record            |
| 404  | C004  | Parent record not found (optional validation)   |
| 404  | C005  | Comment not found (edit / delete)               |
| 409  | C006  | Text identical to latest revision (edit)        |

> These numeric codes are surfaced in the `detail` field of the JSON error body so the front-end can localise messages.

## 11. Firestore Index Config (firebase.json excerpt)
```jsonc
{
  "indexes": [
    {
      "collectionGroup": "COMMENTS",
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "parent_type", "order": "ASCENDING"},
        {"fieldPath": "parent_id",   "order": "ASCENDING"},
        {"fieldPath": "created_at",  "order": "DESCENDING"}
      ]
    },
    {
      "collectionGroup": "COMMENTS",
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "mentions",    "arrayConfig": "CONTAINS"},
        {"fieldPath": "created_at",  "order": "DESCENDING"}
      ]
    }
  ]
}
```
Deploy via `firebase deploy --only firestore:indexes`.

## 12. Cloud Function Skeleton (Python 3.11)
```python
from google.cloud import firestore
from google.cloud import pubsub_v1
import functions_framework

@functions_framework.cloud_event
def on_comment_write(event):
    """Triggered on create & update of /COMMENTS/* documents."""
    payload = event.data["value"]
    if payload.get("deleted", False):
        return  # ignore soft-deletes

    old = event.data.get("oldValue", {}).get("fields", {})
    new = payload.get("fields", {})

    new_mentions = set(new.get("mentions", {}).get("arrayValue", {}).get("values", []))
    old_mentions = set(old.get("mentions", {}).get("arrayValue", {}).get("values", []))
    delta = new_mentions - old_mentions
    if not delta:
        return

    db = firestore.Client()
    for uid_value in delta:
        uid = uid_value.get("stringValue")
        notif_ref = db.collection("USERS").document(uid).collection("NOTIFICATIONS").document()
        notif_ref.set({
            "type": "mention",
            "comment_id": event.id,
            "parent_type": new["parent_type"]["stringValue"],
            "parent_id": new["parent_id"]["stringValue"],
            "snippet": new["text"]["stringValue"][:120],
            "read": False,
            "created_at": firestore.SERVER_TIMESTAMP,
        })
```
This is intentionally naïve; production implementation should batch writes and honour user notification-preference documents.

## 13. React Component API
```ts
interface CommentsPanelProps {
  parentType: "schedule" | "invoice" | "transaction" | string;
  parentId:   string;
  currentUser: { uid: string; displayName: string };
  onClose?: () => void;            // optional for modal placements
  height?: number | string;        // allow embed in sidebars
  enableMentions?: boolean;        // default true
}
```
The component exposes an imperative `scrollToEnd()` via `forwardRef` so host pages can focus the composer after a user action (e.g., clicking "Add comment").

## 14. Validation Matrix
| Rule                                    | Method(s) | Test ID |
|-----------------------------------------|-----------|---------|
| Text empty → 400 C001                   | POST      | T01     |
| >5 000 chars → 422 Pydantic length error| POST      | T02     |
| >20 mentions → 400 C002                 | POST      | T03     |
| Unknown parent_type → 400 C001          | POST      | T04     |
| Edit others comment → 403 C003          | PUT       | T05     |
| Delete others comment → 403             | DELETE    | T06     |

## 15. End-to-End Test Plan (Cypress)
1. Create comment with mention → UI shows immediately (optimistic) and notification badge increments for mentioned user.  
2. Edit comment → text updates, no duplicate notification.  
3. Delete comment → comment greyed out with "deleted" label; refresh page hides it.  
4. Permissions: User from another client hits comments endpoint → 403.

## 16. Deployment Checklist
- [ ] Firestore indexes deployed.  
- [ ] Security rules updated and unit-tested with `firestore-emulator`.  
- [ ] FastAPI routes registered in main `api_router`.  
- [ ] Cloud Function deployed & Pub/Sub topic IAM granted.  
- [ ] Front-end bundle gated behind feature flag `enableComments`.  
- [ ] Release notes & admin docs updated.

---
*Appendix A – Regex for mention extraction*
```
/@([\w\.\-]{2,30})/g
```
(map resulting handle → UID lookup using `/users/resolve/{handle}`). 