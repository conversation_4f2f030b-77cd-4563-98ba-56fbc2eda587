import os
import json
import argparse
from types import SimpleNamespace
import google.cloud.firestore_async as firestore

def build_context_from_env():
    parser = argparse.ArgumentParser()
    parser.add_argument("--entity_id")
    parser.add_argument("--client_id")
    parser.add_argument("--sync_job_id")
    parser.add_argument("--bills")
    parser.add_argument("--entity_settings")
    parser.add_argument("--base_currency", default=os.getenv("BASE_CURRENCY", "USD"))
    parser.add_argument("--run_prepayment_detector", action="store_true")
    args, _ = parser.parse_known_args()

    bills = json.loads(args.bills or os.getenv("BILLS_JSON", "[]"))
    settings = json.loads(args.entity_settings or os.getenv("ENTITY_SETTINGS_JSON", "{}"))

    return SimpleNamespace(
        db=firestore.AsyncClient(project=os.getenv("FIRESTORE_PROJECT_ID")),
        entity_id=args.entity_id or os.getenv("ENTITY_ID"),
        client_id=args.client_id or os.getenv("CLIENT_ID"),
        sync_job_id=args.sync_job_id or os.getenv("SYNC_JOB_ID"),
        bills_to_process=bills,
        entity_settings=settings,
        base_currency=args.base_currency,
        run_prepayment_detector=args.run_prepayment_detector or os.getenv("RUN_PREPAYMENT_DETECTOR", "true").lower() == "true",
        settings=os
    ) 