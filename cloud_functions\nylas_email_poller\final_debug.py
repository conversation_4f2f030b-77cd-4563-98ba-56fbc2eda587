#!/usr/bin/env python3
"""
Final debug - Let's see exactly what's going on
"""

import requests
import json

# Your current credentials
NYLAS_API_KEY = "nyk_v0_BlT3sXqnvDnmP3R8nWgn9ngMiMW0RZL6WeforK3GcznWbIPwbLO4noOzHgBb3oqy"
GRANT_ID = "45fe69de-7b6f-4237-b526-c5ce4a549427"
API_URI = "https://api.eu.nylas.com/v3"

headers = {
    'Authorization': f'Bearer {NYLAS_API_KEY}',
    'Content-Type': 'application/json'
}

def check_everything():
    """Check every aspect of the setup"""
    print("🔍 FINAL DEBUG - Complete System Check")
    print("=" * 60)
    
    # 1. Check API key validity
    print("\n1️⃣ Testing API Key...")
    try:
        response = requests.get(f"{API_URI}/applications", headers=headers, timeout=10)
        if response.status_code == 200:
            print("✅ API Key is valid")
            app_data = response.json().get('data', [])
            if app_data:
                print(f"   App ID: {app_data[0].get('id')}")
                print(f"   App Name: {app_data[0].get('name')}")
        else:
            print(f"❌ API Key issue: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ API Key error: {str(e)}")
        return False
    
    # 2. Check grant details
    print("\n2️⃣ Checking Grant Details...")
    try:
        response = requests.get(f"{API_URI}/grants/{GRANT_ID}", headers=headers, timeout=10)
        if response.status_code == 200:
            grant_data = response.json().get('data', {})
            print("✅ Grant found:")
            print(f"   Grant ID: {grant_data.get('id')}")
            print(f"   Status: {grant_data.get('grant_status')}")
            print(f"   Email: {grant_data.get('email')}")
            print(f"   Provider: {grant_data.get('provider')}")
            print(f"   Scopes: {grant_data.get('scopes', [])}")
            print(f"   Created: {grant_data.get('created_at')}")
            
            # Check if scopes are actually populated
            scopes = grant_data.get('scopes', [])
            if not scopes:
                print("❌ PROBLEM: Empty scopes array")
                return False
            elif 'gmail.send' in str(scopes) or 'https://www.googleapis.com/auth/gmail.send' in str(scopes):
                print("✅ Send permissions found in scopes")
            else:
                print("❌ PROBLEM: No send permissions in scopes")
                print(f"   Available scopes: {scopes}")
                return False
        else:
            print(f"❌ Grant error: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Grant check error: {str(e)}")
        return False
    
    # 3. Test minimal read operation
    print("\n3️⃣ Testing Read Permission...")
    try:
        response = requests.get(f"{API_URI}/grants/{GRANT_ID}/messages?limit=1", headers=headers, timeout=10)
        if response.status_code == 200:
            print("✅ Read permission works")
        else:
            print(f"❌ Read permission failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Read test error: {str(e)}")
    
    # 4. Test send operation
    print("\n4️⃣ Testing Send Permission...")
    test_message = {
        "to": [{"email": "<EMAIL>"}],
        "from": [{"email": "<EMAIL>"}],
        "subject": "Test Send Permission",
        "body": "This is a test - DO NOT SEND"
    }
    
    try:
        response = requests.post(f"{API_URI}/grants/{GRANT_ID}/messages", 
                               headers=headers, json=test_message, timeout=10)
        print(f"   Send Response: {response.status_code}")
        print(f"   Send Body: {response.text}")
        
        if response.status_code in [200, 201]:
            print("✅ SEND PERMISSION WORKS!")
            return True
        elif response.status_code == 403:
            print("❌ PROBLEM: 403 Forbidden - Missing send permissions")
            return False
        elif response.status_code == 400:
            print("⚠️  400 Bad Request - Message format issue (but permissions might be OK)")
            return True
        else:
            print(f"❓ Unexpected response: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Send test error: {str(e)}")
        return False

def main():
    """Run complete diagnostic"""
    success = check_everything()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 SEND PERMISSIONS ARE WORKING!")
        print("   Your reply system should work now.")
    else:
        print("❌ SEND PERMISSIONS STILL NOT WORKING")
        print("\n🔧 Next steps:")
        print("1. Double-check Google Cloud Console OAuth consent screen has gmail.send scope")
        print("2. Make sure you granted ALL permissions when authenticating")
        print("3. Try creating one more fresh grant in Nylas Dashboard")
        print("4. Contact Nylas support if issue persists")

if __name__ == "__main__":
    main()