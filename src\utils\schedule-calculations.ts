import type { MonthlyScheduleEntry } from '../types/schedule.types';

/**
 * Utility function to calculate running balances for schedule entries
 * Used by both preview calculations and initial data hydration
 */
export const withRunningBalance = (
  entries: Omit<MonthlyScheduleEntry, 'runningBalance'>[],
  totalAmount: number
): MonthlyScheduleEntry[] => {
  let balance = totalAmount;
  return entries.map(entry => {
    balance -= entry.amount;
    return {
      ...entry,
      runningBalance: Math.max(0, balance)
    };
  });
};

/**
 * Helper function to restore original amounts from a schedule
 * Used when reverting configuration changes
 */
export const restoreOriginalAmounts = (
  entries: MonthlyScheduleEntry[]
): MonthlyScheduleEntry[] => {
  return entries.map(entry => ({
    ...entry,
    amount: entry.originalAmount ?? entry.amount
  }));
};

/**
 * Helper function to check if configuration represents a revert to original
 */
export const isRevertingToOriginal = (
  newConfig: any,
  originalConfig: any
): boolean => {
  return (
    newConfig.method === originalConfig.method &&
    newConfig.startDate === originalConfig.startDate &&
    newConfig.numberOfPeriods === originalConfig.numberOfPeriods
  );
};