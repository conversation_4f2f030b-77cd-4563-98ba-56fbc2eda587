/**
 * Shared constants for status filters across the application
 * Prevents duplication and ensures consistency
 */

// Default status filters for actionable items
export const DEFAULT_STATUS_FILTERS = ['pending_configuration', 'proposed'] as const;

// Actionable statuses that require user attention
export const ACTIONABLE_STATUSES = ['pending_configuration', 'proposed'] as const;

// All possible schedule statuses
export const ALL_SCHEDULE_STATUSES = [
  'pending_configuration',
  'proposed', 
  'confirmed',
  'posted',
  'partially_posted',
  'skipped',
  'cancelled',
  'error',
  'excluded'
] as const;

// Type-safe status filter union
export type StatusFilter = typeof ALL_SCHEDULE_STATUSES[number];

// Standard workflow statuses (not "other")
export const STANDARD_WORKFLOW_STATUSES = [
  'pending_configuration',
  'proposed', 
  'confirmed',
  'posted',
  'partially_posted'
] as const;

// Status option definitions with metadata
export interface StatusOption {
  value: string;
  label: string;
}

export const DEFAULT_STATUS_OPTIONS: StatusOption[] = [
  { value: 'pending_configuration', label: 'Pending Configuration' },
  { value: 'proposed', label: 'Pending Review' },
  { value: 'confirmed', label: 'Confirmed' },
  { value: 'posted', label: 'Posted' },
  { value: 'partially_posted', label: 'Partially Posted' },
  { value: 'skipped', label: 'Skipped' },
  { value: 'error', label: 'Error' },
  { value: 'excluded', label: 'Excluded' },
];

// URL parameter constants
export const URL_PARAM_NAMES = {
  CLIENT_ID: 'clientId',
  ENTITY_ID: 'entityId', 
  STATUS: 'status'
} as const;

// Legacy parameter name mappings (for backward compatibility)
export const LEGACY_PARAM_MAPPINGS = {
  'client_id': URL_PARAM_NAMES.CLIENT_ID,
  'entity_id': URL_PARAM_NAMES.ENTITY_ID,
  'status_filter': URL_PARAM_NAMES.STATUS,
  'status_filters': URL_PARAM_NAMES.STATUS
} as const;

// Helper functions
export const isActionableStatus = (status: string): boolean => {
  return ACTIONABLE_STATUSES.includes(status as typeof ACTIONABLE_STATUSES[number]);
};

export const getStatusOption = (value: string): StatusOption | undefined => {
  return DEFAULT_STATUS_OPTIONS.find(option => option.value === value);
};

// Normalization utilities
export const normalizeParamName = (paramName: string): string => {
  return LEGACY_PARAM_MAPPINGS[paramName as keyof typeof LEGACY_PARAM_MAPPINGS] || paramName;
};

export const splitStatusFilters = (statusParam: string | null): string[] => {
  if (!statusParam) return [];
  return statusParam.split(',').map(s => s.trim()).filter(Boolean);
};