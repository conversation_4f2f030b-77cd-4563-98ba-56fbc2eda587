import { useMemo } from 'react';
import type { HierarchicalBillsData, SupplierNode } from '../types/hierarchical-bills.types';
import { 
  getSelectedTotalAmount, 
  getSelectedLineItemIds,
  calculateSupplierSelectionState,
  calculateInvoiceSelectionState 
} from '../types/hierarchical-bills.types';

export interface SelectedSummary {
  count: number;
  totalAmount: number;
  lineItemIds: string[];
  hasSelection: boolean;
}

export interface UseSelectionReturn {
  selectedSummary: SelectedSummary;
  handleToggleSelection: (type: 'supplier' | 'invoice' | 'lineItem', id: string) => void;
  handleSelectAll: (selected: boolean) => void;
  getCurrentSelectionState: () => {
    selectedLineItemIds: Set<string>;
    selectedInvoiceIds: Set<string>;
    selectedSupplierIds: Set<string>;
  };
  restoreSelectionState: (
    data: HierarchicalBillsData, 
    selectionState: {
      selectedLineItemIds: Set<string>;
      selectedInvoiceIds: Set<string>;
      selectedSupplierIds: Set<string>;
    }
  ) => HierarchicalBillsData;
}

export function useSelection(
  hierarchicalData: HierarchicalBillsData,
  setHierarchicalData: React.Dispatch<React.SetStateAction<HierarchicalBillsData>>
): UseSelectionReturn {

  // Calculate selectedSummary derived value to avoid recomputation
  const selectedSummary = useMemo<SelectedSummary>(() => {
    const lineItemIds = getSelectedLineItemIds(hierarchicalData.suppliers);
    const totalAmount = getSelectedTotalAmount(hierarchicalData.suppliers);
    
    return {
      count: lineItemIds.length,
      totalAmount,
      lineItemIds,
      hasSelection: lineItemIds.length > 0,
    };
  }, [hierarchicalData.suppliers]);

  // Helper function to extract current selection state
  const getCurrentSelectionState = () => {
    const selectedLineItemIds = new Set<string>();
    const selectedInvoiceIds = new Set<string>();
    const selectedSupplierIds = new Set<string>();

    hierarchicalData.suppliers.forEach(supplier => {
      if (supplier.isSelected) selectedSupplierIds.add(supplier.supplierId);
      
      supplier.invoices.forEach(invoice => {
        if (invoice.isSelected) selectedInvoiceIds.add(invoice.invoiceId);
        
        invoice.lineItems.forEach(lineItem => {
          if (lineItem.isSelected) selectedLineItemIds.add(lineItem.lineItemId);
        });
      });
    });

    return { selectedLineItemIds, selectedInvoiceIds, selectedSupplierIds };
  };

  // Helper function to restore selection state on fresh data
  const restoreSelectionState = (
    data: HierarchicalBillsData, 
    selectionState: {
      selectedLineItemIds: Set<string>;
      selectedInvoiceIds: Set<string>;
      selectedSupplierIds: Set<string>;
    }
  ) => {
    return {
      ...data,
      suppliers: data.suppliers.map(supplier => ({
        ...supplier,
        isSelected: selectionState.selectedSupplierIds.has(supplier.supplierId),
        invoices: supplier.invoices.map(invoice => ({
          ...invoice,
          isSelected: selectionState.selectedInvoiceIds.has(invoice.invoiceId),
          lineItems: invoice.lineItems.map(lineItem => ({
            ...lineItem,
            isSelected: selectionState.selectedLineItemIds.has(lineItem.lineItemId)
          }))
        }))
      }))
    };
  };

  // Handle selection toggle for supplier, invoice, or line item
  const handleToggleSelection = (type: 'supplier' | 'invoice' | 'lineItem', id: string) => {
    setHierarchicalData(prev => {
      const newData = { ...prev };
      
      // Find which supplier contains the target
      const targetSupplierIndex = prev.suppliers.findIndex(supplier => {
        if (type === 'supplier') return supplier.supplierId === id;
        if (type === 'invoice') return supplier.invoices.some(inv => inv.invoiceId === id);
        if (type === 'lineItem') return supplier.invoices.some(inv => inv.lineItems.some(item => item.lineItemId === id));
        return false;
      });
      
      newData.suppliers = prev.suppliers.map((supplier, index) => {
        // Only process the target supplier, unselect all others
        if (index !== targetSupplierIndex) {
          return {
            ...supplier,
            isSelected: false,
            invoices: supplier.invoices.map(invoice => ({
              ...invoice,
              isSelected: false,
              lineItems: invoice.lineItems.map(lineItem => ({
                ...lineItem,
                isSelected: false,
              }))
            }))
          };
        }
        if (type === 'supplier' && supplier.supplierId === id) {
          const newSelected = !supplier.isSelected;
          return {
            ...supplier,
            isSelected: newSelected,
            invoices: supplier.invoices.map(invoice => ({
              ...invoice,
              isSelected: newSelected,
              lineItems: invoice.lineItems.map(lineItem => ({
                ...lineItem,
                isSelected: newSelected,
              }))
            }))
          };
        }
        
        if (type === 'invoice') {
          // Only one invoice can be selected at a time
          const updatedInvoices = supplier.invoices.map(invoice => {
            if (invoice.invoiceId === id) {
              const newSelected = !invoice.isSelected;
              return {
                ...invoice,
                isSelected: newSelected,
                lineItems: invoice.lineItems.map(lineItem => ({
                  ...lineItem,
                  isSelected: newSelected,
                }))
              };
            } else {
              // Unselect all other invoices in this supplier
              return {
                ...invoice,
                isSelected: false,
                lineItems: invoice.lineItems.map(lineItem => ({
                  ...lineItem,
                  isSelected: false,
                }))
              };
            }
          });
          
          return {
            ...supplier,
            isSelected: false, // Never select supplier when individual invoice is selected
            invoices: updatedInvoices,
          };
        }
        
        if (type === 'lineItem') {
          const updatedInvoices = supplier.invoices.map(invoice => {
            const hasTargetLineItem = invoice.lineItems.some(item => item.lineItemId === id);
            
            if (hasTargetLineItem) {
              // This invoice contains the target line item
              const updatedLineItems = invoice.lineItems.map(lineItem => {
                if (lineItem.lineItemId === id) {
                  return { ...lineItem, isSelected: !lineItem.isSelected };
                }
                return lineItem;
              });
              
              // Update invoice selection based on line item selections
              const allLineItemsSelected = updatedLineItems.every(item => item.isSelected);
              
              return {
                ...invoice,
                isSelected: allLineItemsSelected,
                lineItems: updatedLineItems,
              };
            } else {
              // This invoice doesn't contain the target - unselect it
              return {
                ...invoice,
                isSelected: false,
                lineItems: invoice.lineItems.map(lineItem => ({
                  ...lineItem,
                  isSelected: false,
                }))
              };
            }
          });
          
          return {
            ...supplier,
            isSelected: false, // Never select supplier when individual line item is selected
            invoices: updatedInvoices,
          };
        }
        
        return supplier;
      });
      
      return newData;
    });
  };

  // Handle select all/none
  const handleSelectAll = (selected: boolean) => {
    setHierarchicalData(prev => ({
      ...prev,
      suppliers: prev.suppliers.map(supplier => ({
        ...supplier,
        isSelected: selected,
        invoices: supplier.invoices.map(invoice => ({
          ...invoice,
          isSelected: selected,
          lineItems: invoice.lineItems.map(lineItem => ({
            ...lineItem,
            isSelected: selected,
          }))
        }))
      }))
    }));
  };

  return {
    selectedSummary,
    handleToggleSelection,
    handleSelectAll,
    getCurrentSelectionState,
    restoreSelectionState,
  };
}