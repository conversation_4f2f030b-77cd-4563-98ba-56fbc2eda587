#!/bin/bash

# Deploy Cloud Tasks queue for email replies
# Run this script to set up the reply processing queue

echo "Deploying Cloud Tasks queue for email replies..."

# Set project and location
PROJECT_ID=${GCP_PROJECT_ID:-"drcr-d660a"}
LOCATION=${CLOUD_TASKS_LOCATION:-"europe-west2"}
QUEUE_NAME="reply-queue"

echo "Project: $PROJECT_ID"
echo "Location: $LOCATION" 
echo "Queue: $QUEUE_NAME"

# Create the queue with rate limiting
gcloud tasks queues create $QUEUE_NAME \
  --location=$LOCATION \
  --max-dispatches-per-second=1 \
  --max-burst-size=5 \
  --max-concurrent-dispatches=10 \
  --max-attempts=5 \
  --max-retry-duration=3600s \
  --min-backoff=10s \
  --max-backoff=300s \
  --max-doublings=3 \
  --project=$PROJECT_ID

if [ $? -eq 0 ]; then
    echo "✅ Reply queue created successfully"
    echo ""
    echo "Queue details:"
    gcloud tasks queues describe $QUEUE_NAME --location=$LOCATION --project=$PROJECT_ID
else
    echo "❌ Failed to create reply queue"
    echo "Queue may already exist. Checking..."
    gcloud tasks queues describe $QUEUE_NAME --location=$LOCATION --project=$PROJECT_ID
fi

echo ""
echo "To update queue configuration later, use:"
echo "gcloud tasks queues update $QUEUE_NAME --location=$LOCATION --max-dispatches-per-second=1"