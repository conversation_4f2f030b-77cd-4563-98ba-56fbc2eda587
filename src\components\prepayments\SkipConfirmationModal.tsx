import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Loader2, AlertCircle } from 'lucide-react';

interface SkipConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (reason: string) => void;
  isLoading: boolean;
  selectedCount: number;
  scheduleType?: string;
}

export function SkipConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  isLoading,
  selectedCount,
  scheduleType = 'schedule'
}: SkipConfirmationModalProps) {
  const [reason, setReason] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (reason.trim()) {
      onConfirm(reason.trim());
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setReason('');
      onClose();
    }
  };

  const isBulk = selectedCount > 1;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-amber-500" />
            Skip {isBulk ? `${selectedCount} ${scheduleType}s` : scheduleType}
          </DialogTitle>
          <DialogDescription>
            {isBulk 
              ? `You are about to skip ${selectedCount} ${scheduleType}s. This action cannot be undone.`
              : `You are about to skip this ${scheduleType}. This action cannot be undone.`
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="reason">
                Reason for skipping <span className="text-red-500">*</span>
              </Label>
              <Textarea
                id="reason"
                placeholder="Enter the reason for skipping..."
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                disabled={isLoading}
                required
                className="min-h-[80px]"
              />
              <p className="text-sm text-muted-foreground">
                This reason will be recorded in the audit log.
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="destructive"
              disabled={!reason.trim() || isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Skipping...
                </>
              ) : (
                `Skip ${isBulk ? `${selectedCount} ${scheduleType}s` : scheduleType}`
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}