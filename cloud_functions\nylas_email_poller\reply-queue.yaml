# Cloud Tasks Queue Configuration for Email Replies
# This queue handles async email reply processing with rate limiting

queue:
  name: "reply-queue"
  
# Rate limiting to respect Gmail's 100 messages per minute limit
rate_limits:
  max_dispatches_per_second: 1
  max_burst_size: 5
  max_concurrent_dispatches: 10

# Retry configuration for failed tasks
retry_config:
  max_attempts: 5
  max_retry_duration: "3600s"  # 1 hour
  min_backoff: "10s"
  max_backoff: "300s"  # 5 minutes
  max_doublings: 3

# Dead letter queue for permanently failed tasks
purge_time: "2592000s"  # 30 days

# Target configuration
target:
  type: "http"
  
# Logging
stackdriver_logging_config:
  sampling_ratio: 1.0