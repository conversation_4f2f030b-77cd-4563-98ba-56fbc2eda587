import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  ChevronDown,
  ChevronRight,
  ExternalLink,
  CircleSlash,
  PlusCircle,
  Unlink,
  Wrench,
  Link as LinkIcon,
  Settings,
  Loader2,
  FileText,
  CheckSquare,
  AlertCircle,
  Bell,
} from 'lucide-react';

import { getClientStatusInfo, getEntityStatusInfo, StatusBadge, ClientDetails, normalizeEntitySummary } from './StatusUtils';
import type { DashboardState, DashboardActions } from '../types';
import { XeroOperationStatus } from '@/components/ui/xero-operation-status';
import { EntityStatusBadge } from '@/components/ui/entity-status-badge';

interface ClientsTableProps extends DashboardState, DashboardActions {}

export function ClientsTable({
  clientSummaries,
  isLoading,
  error,
  expandedClients,
  handleClientClick,
  handleEntityClick,
  handleDisconnectEntity,
  handleFixConnection,
  handleConnectNewEntity,
  handleEntitySettings,
  toggleClientExpansion,
  handleClientSettings,
  operatingEntityId,
  connectState,
  disconnectState,
}: ClientsTableProps) {
  return (
    <div className="border rounded-lg overflow-auto bg-white shadow-sm flex-grow">
      <Table>
        <TableHeader className="sticky top-0 z-10 bg-gray-50">
          <TableRow>
            <TableHead className="text-xs uppercase text-muted-foreground">Client</TableHead>
            <TableHead className="text-xs uppercase text-muted-foreground">Status</TableHead>
            <TableHead className="text-xs uppercase text-muted-foreground">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading && (
            // Loading Skeleton Rows
            [...Array(5)].map((_, i) => (
              <TableRow key={`skel-${i}`} className="border-b border-slate-200">
                <TableCell className="py-4 px-3"><Skeleton className="h-5 w-3/4" /></TableCell>
                <TableCell className="py-4 px-2"><Skeleton className="h-5 w-20" /></TableCell>
                <TableCell className="text-right py-4 px-2"><Skeleton className="h-8 w-24 ml-auto" /></TableCell>
              </TableRow>
            ))
          )}
          {!isLoading && !error && clientSummaries.length === 0 && (
            <TableRow>
              <TableCell colSpan={3} className="h-24 text-center text-muted-foreground p-4">
                <CircleSlash className="mx-auto h-12 w-12 mb-4 text-gray-400"/>
                <p className="text-lg font-medium">No clients found matching criteria.</p>
              </TableCell>
            </TableRow>
          )}
          {!isLoading && !error && clientSummaries.map((client) => {
            const statusInfo = getClientStatusInfo(client.overall_status);
            const isExpanded = expandedClients[client.client_id];
            return (
              <React.Fragment key={client.client_id}>
                {/* Client Row */}
                <TableRow
                  className="hover:bg-slate-50 border-b border-slate-200 cursor-pointer transition-colors bg-white"
                  onClick={() => toggleClientExpansion(client.client_id)}
                >
                  <TableCell className="font-semibold py-4 px-3">
                    <span className="inline-flex items-center">
                      {isExpanded ?
                        <ChevronDown className="h-4 w-4 mr-2 opacity-50" /> :
                        <ChevronRight className="h-4 w-4 mr-2 opacity-50" />
                      }
                      <span>{client.name}</span>
                    </span>
                  </TableCell>
                  <TableCell className="py-4 px-2">
                    {statusInfo && (
                      <StatusBadge statusInfo={statusInfo} />
                    )}
                  </TableCell>
                  <TableCell className="text-right py-4 px-2">
                    <div className="flex gap-2 justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        disabled={!client.entities || client.entities.length === 0}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleClientClick(client.client_id);
                        }}
                      >
                        Manage Entities <ExternalLink className="ml-2 h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleClientSettings(client.client_id);
                        }}
                        title="Client Settings"
                      >
                        <Settings className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>

                {/* Entity Sub-Rows */}
                {isExpanded && client.entities.map((entity) => {
                  const entityStatusInfo = getEntityStatusInfo(normalizeEntitySummary(entity));
                  const isOperating = operatingEntityId === entity.entity_id;
                  const showDisconnectStatus = isOperating && disconnectState.status !== 'idle';
                  const showConnectStatus = isOperating && connectState.status !== 'idle';

                  return (
                    <React.Fragment key={entity.entity_id}>
                      <TableRow className="bg-slate-50/50 hover:bg-slate-100/70 border-b border-slate-200/50 transition-colors">
                        <TableCell className="pl-10 pr-3 py-3 font-normal text-sm">
                          <div className="flex items-center">
                            <div className="h-4 w-0.5 bg-slate-300 mr-3 flex-shrink-0"></div>
                            <div className="flex-1">
                              <div className="flex items-center">
                                <button
                                  className="text-blue-600 font-medium hover:text-blue-800 hover:underline cursor-pointer text-left transition-colors underline-offset-2"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleEntityClick(client.client_id, entity.entity_id);
                                  }}
                                >
                                  {entity.entity_name}
                                </button>
                                <EntityStatusBadge
                                  entity={normalizeEntitySummary(entity)}
                                  className="ml-2"
                                />
                                <span className="text-slate-500 text-xs ml-2 bg-slate-200 px-2 py-0.5 rounded-full">
                                  {entity.type}
                                </span>
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="py-3 px-2">
                          <div className="space-y-1">
                            {entity.pending_schedules_count > 0 && (
                              <button
                                className="text-xs text-amber-600 hover:text-amber-700 hover:underline cursor-pointer flex items-center justify-start"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  window.location.href = `/accpay/prepayments?clientId=${client.client_id}&entityId=${entity.entity_id}&status=action_needed`;
                                }}
                              >
                                <Bell className="h-3 w-3 mr-1" />
                                {entity.pending_schedules_count} schedule{entity.pending_schedules_count === 1 ? '' : 's'} need attention
                              </button>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-right py-3 px-2">
                          <div className="flex flex-wrap gap-1 justify-end items-center">
                            {entity.connection_status === 'error' && (
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleFixConnection(client.client_id, entity.entity_id);
                                }}
                                disabled={operatingEntityId === entity.entity_id && connectState.isLoading}
                              >
                                {operatingEntityId === entity.entity_id && connectState.isLoading ? (
                                  <>
                                    <Loader2 className="mr-1 h-3 w-3 animate-spin" /> Fixing...
                                  </>
                                ) : (
                                  <>
                                    <Wrench className="mr-1 h-3 w-3" /> Fix
                                  </>
                                )}
                              </Button>
                            )}
                            {entity.connection_status === 'active' && entity.type !== 'manual' && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDisconnectEntity(client.client_id, entity.entity_id);
                                }}
                                disabled={operatingEntityId === entity.entity_id && disconnectState.isLoading}
                              >
                                {operatingEntityId === entity.entity_id && disconnectState.isLoading ? (
                                  <>
                                    <Loader2 className="mr-1 h-3 w-3 animate-spin" /> Disconnecting...
                                  </>
                                ) : (
                                  <>
                                    <Unlink className="mr-1 h-3 w-3" /> Disconnect
                                  </>
                                )}
                              </Button>
                            )}
                            {entity.connection_status === 'disconnected' && (
                              <Button
                                variant="default"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleFixConnection(client.client_id, entity.entity_id);
                                }}
                                disabled={operatingEntityId === entity.entity_id && connectState.isLoading}
                              >
                                {operatingEntityId === entity.entity_id && connectState.isLoading ? (
                                  <>
                                    <Loader2 className="mr-1 h-3 w-3 animate-spin" /> Reconnecting...
                                  </>
                                ) : (
                                  <>
                                    <LinkIcon className="mr-1 h-3 w-3" /> Reconnect
                                  </>
                                )}
                              </Button>
                            )}
                            
                            {/* Schedule Action Buttons */}
                            {(entity.schedule_status_counts?.proposed || 0) > 0 && (
                              <Button
                                variant="outline"
                                size="sm"
                                className="bg-yellow-50 border-yellow-300 text-yellow-700 hover:bg-yellow-100"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  window.location.href = `/accpay/prepayments?clientId=${client.client_id}&entityId=${entity.entity_id}&status=proposed`;
                                }}
                                title={`${entity.schedule_status_counts.proposed} schedule(s) need review`}
                              >
                                <FileText className="mr-1 h-3 w-3" /> Review
                              </Button>
                            )}
                            

                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    disabled={entity.connection_status === 'syncing' || entity.sync_status?.is_syncing}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleEntitySettings(client.client_id, entity.entity_id);
                                    }}
                                  >
                                    <Settings className="h-3 w-3" />
                                  </Button>
                                </span>
                              </TooltipTrigger>
                              <TooltipContent>
                                {entity.connection_status === 'syncing' || entity.sync_status?.is_syncing 
                                  ? 'Settings unavailable during sync' 
                                  : 'Entity Settings'
                                }
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableCell>
                      </TableRow>

                      {/* Inline Operation Status */}
                      {(showDisconnectStatus || showConnectStatus) && (
                        <TableRow className="bg-blue-50/50 border-b border-blue-200">
                          <TableCell colSpan={3} className="pl-14 pr-6 py-4">
                            {showDisconnectStatus && (
                              <XeroOperationStatus
                                operation="disconnect"
                                status={disconnectState.status}
                                variant="compact"
                                showProgress={true}
                                showElapsedTime={true}
                                timeoutSeconds={30}
                                elapsedSeconds={disconnectState.elapsedSeconds}
                                size="sm"
                                className="w-full"
                              />
                            )}
                            {showConnectStatus && (
                              <XeroOperationStatus
                                operation="reconnect"
                                status={connectState.status}
                                variant="compact"
                                showProgress={true}
                                showElapsedTime={true}
                                timeoutSeconds={45}
                                elapsedSeconds={connectState.elapsedSeconds}
                                size="sm"
                                className="w-full"
                              />
                            )}
                          </TableCell>
                        </TableRow>
                      )}
                    </React.Fragment>
                  );
                })}

                {/* Add Entity Row */}
                {isExpanded && (
                  <TableRow className="bg-slate-50/30 hover:bg-slate-100/50 border-b border-slate-200/30 transition-colors">
                    <TableCell colSpan={3} className="pl-10 pr-6 py-2">
                      <div className="flex items-center">
                        <div className="h-3 w-0.5 bg-slate-300 mr-3 flex-shrink-0"></div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 p-1 h-auto font-normal text-sm border border-dashed border-blue-300 hover:border-blue-400 rounded-md transition-all"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleConnectNewEntity(client.client_id);
                          }}
                        >
                          <PlusCircle className="mr-2 h-3 w-3"/> Connect New Entity
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </React.Fragment>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}