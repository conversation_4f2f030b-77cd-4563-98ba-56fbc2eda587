# Heavy Sync Migration to Google Cloud Run Jobs

> **Goal**: Move the long-running `heavy_sync_processor` workload out of the FastAPI service and run it as an isolated, fully managed Cloud Run **Job**.

---

## 1&nbsp;· Inventory of Responsibilities

| Area | Requirement |
|------|-------------|
| Core logic | `heavy_sync_processor(…)` in `rest_api/routes/sync.py` |
| Inputs | `entity_id`, `client_id`, `sync_job_id`, `bills_to_process`, `entity_settings`, `base_currency`, `run_prepayment_detector` |
| Dependencies | Firestore, Xero SDK, OpenAI / Mistral, shared libs |
| Side-effects | Writes to Firestore collections `SYNC_JOBS`, `TRANSACTIONS`, audit logs |
| AuthZ | Currently enforced with header `X-Sync-Source: cloud-function`; Cloud-Run job will instead rely on service-account IAM |

---

## 2&nbsp;· Extract a Stand-Alone Entry-Point

```python
# jobs/heavy_sync_job.py
if __name__ == "__main__":
    ctx = build_context_from_env()  # helper that reads ENV / CLI args
    asyncio.run(
        heavy_sync_processor(
            db=ctx.db,
            entity_id=ctx.entity_id,
            client_id=ctx.client_id,
            sync_job_id=ctx.sync_job_id,
            bills_to_process=ctx.bills_to_process,
            entity_settings=ctx.entity_settings,
            base_currency=ctx.base_currency,
            user_id="cloud-run-job",
            run_prepayment_detector=ctx.run_prepayment_detector,
        )
    )
```

* Re-use existing helper utilities so there is **no duplicated business logic**.

---

## 3&nbsp;· Build a Dedicated Container Image

Create `Dockerfile.heavy_sync` (or a new stage in the existing Dockerfile):

```dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt
COPY rest_api/ ./rest_api/
COPY jobs/      ./jobs/
CMD ["python", "-m", "jobs.heavy_sync_job"]
```

---

## 4&nbsp;· Provision the Cloud Run Job

```bash
# one-time creation (subsequent releases use deploy)
gcloud run jobs create heavy-sync-job \
  --image gcr.io/$PROJECT_ID/heavy-sync:latest \
  --region $REGION \
  --service-account heavy-sync-sa@$PROJECT_ID.iam.gserviceaccount.com \
  --memory 4Gi --cpu 2 \
  --timeout 3h \
  --max-retries 1 \
  --set-env-vars FIRESTORE_PROJECT_ID=$PROJECT_ID
```

Grant the service account roles for Firestore, Secret Manager, and any external APIs.

---

## 5&nbsp;· Triggering the Job

Today: **Cloud Function →** HTTP POST `/sync/process-heavy/{entity_id}`.

Tomorrow: **Cloud Function / Scheduler →** execute the job:

```python
subprocess.run([
  "gcloud", "run", "jobs", "execute", "heavy-sync-job",
  "--region", REGION,
  "--args", json.dumps(payload)  # or env vars
], check=True)
```

*Preferred*: call the Cloud Run Admin API (`projects.locations.jobs.run`) or enqueue a Cloud Tasks target to decouple execution from gcloud.

---

## 6&nbsp;· Deprecate the FastAPI Endpoint (Optional)

1. **Interim**: keep `/sync/process-heavy/{entity_id}` but have it *start the Cloud Run job* instead of calling `heavy_sync_processor`.
2. **Later**: remove the route once no callers depend on it.

---

## 7&nbsp;· Observability

* The job continues to update `SYNC_JOBS`, so dashboards remain intact.
* Add Cloud Logging labels: `processing_type=heavy_sync`, `entity_id`, `sync_job_id`.
* (Optional) Emit a Cloud Monitoring custom metric when a job finishes.

---

## 8&nbsp;· CI / CD Pipeline Updates

```bash
# Build image on every merge
gcloud builds submit -f Dockerfile.heavy_sync -t gcr.io/$PROJECT_ID/heavy-sync:$SHORT_SHA

# Deploy updated definition
gcloud run jobs deploy heavy-sync-job \
  --image gcr.io/$PROJECT_ID/heavy-sync:$SHORT_SHA \
  --region $REGION
```

---

## 9&nbsp;· Rollback Strategy

* Re-deploy previous heavy-sync container image **or**
* Temporarily switch Cloud Function back to FastAPI endpoint (feature flag).

---

## 10&nbsp;· Indicative Timeline & Owners

| Day | Activity | Owner |
|-----|----------|-------|
| 0 | Design review | Lead dev |
| 1-2 | Entry-point extraction + Docker build | Dev A |
| 3 | IAM & Job creation in staging | Ops B |
| 4 | Modify Cloud Function trigger; run E2E tests | Dev A |
| 5 | Deploy to prod behind flag; monitor | Dev A & Ops B |
| 7 | Delete legacy FastAPI path if stable | Dev A |

---

## 11&nbsp;· Advantages After Migration

* No request-timeout gymnastics—Cloud Run Jobs are built for long-running tasks.
* Isolates CPU / memory spikes away from the synchronous API.
* Cleaner billing visibility—pay only while the job runs. 

---

## 12 · Technical Specifications

### 12.1 Environment Variables / CLI-Argument Contract

| Name | Source | Required | Description |
|------|--------|----------|-------------|
| `ENTITY_ID` | env or `--entity_id` | ✅ | Firestore `ENTITIES` document ID |
| `CLIENT_ID` | env or `--client_id` | ✅ | Parent client identifier (for logging) |
| `SYNC_JOB_ID` | env or `--sync_job_id` | ✅ | Each run must have a stable ID so progress can be resumed |
| `BILLS_JSON` | env or `--bills` | ✅ | JSON-encoded array of `{ transaction_id, entity_id, ... }` |
| `ENTITY_SETTINGS_JSON` | env or `--entity_settings` | ✅ | JSON settings snapshot used by the processor |
| `BASE_CURRENCY` | env or `--base_currency` | 🚫 (defaults to `USD`) | Base reporting currency |
| `RUN_PREPAYMENT_DETECTOR` | env or `--run_prepayment_detector` | 🚫 (defaults to `true`) | Toggle prepayment detector |
| `FIRESTORE_PROJECT_ID` | env | ✅ | Used by google-cloud-firestore client |
| `OPENAI_API_KEY` / `MISTRAL_API_KEY` | Secret Manager | ✅ | LLM usage |

### 12.2 `build_context_from_env()` Sketch

```python
import os, json, argparse, google.cloud.firestore_async as firestore
from types import SimpleNamespace

_parser = argparse.ArgumentParser()
_parser.add_argument("--entity_id")
_parser.add_argument("--client_id")
_parser.add_argument("--sync_job_id")
_parser.add_argument("--bills")
_parser.add_argument("--entity_settings")
_parser.add_argument("--base_currency", default=os.getenv("BASE_CURRENCY", "USD"))
_parser.add_argument("--run_prepayment_detector", action="store_true")
args, _ = _parser.parse_known_args()

bills = json.loads(args.bills or os.getenv("BILLS_JSON", "[]"))
settings = json.loads(args.entity_settings or os.getenv("ENTITY_SETTINGS_JSON", "{}"))

return SimpleNamespace(
    db=firestore.AsyncClient(project=os.getenv("FIRESTORE_PROJECT_ID")),
    entity_id=args.entity_id or os.getenv("ENTITY_ID"),
    client_id=args.client_id or os.getenv("CLIENT_ID"),
    sync_job_id=args.sync_job_id or os.getenv("SYNC_JOB_ID"),
    bills_to_process=bills,
    entity_settings=settings,
    base_currency=args.base_currency,
    run_prepayment_detector=args.run_prepayment_detector or os.getenv("RUN_PREPAYMENT_DETECTOR", "true").lower() == "true",
    settings=os  # forward env for helpers
)
```

### 12.3 IAM Roles for `heavy-sync-sa`

```
roles/datastore.user          # Firestore access
roles/secretmanager.secretAccessor
roles/logging.logWriter
roles/monitoring.metricWriter
roles/storage.objectAdmin     # if GCS uploads are part of heavy sync
roles/run.invoker             # (only if Chain-triggering other Cloud Run services)
```

> Grant least-privilege; some entities (eg. OpenAI API) are accessed via Secrets, not IAM.

### 12.4 Declarative Job YAML (alternative to CLI)

```yaml
apiVersion: run.googleapis.com/v1
kind: Job
metadata:
  name: heavy-sync-job
  annotations:
    run.googleapis.com/launch-stage: BETA
spec:
  template:
    template:
      spec:
        serviceAccountName: heavy-sync-sa@${PROJECT_ID}.iam.gserviceaccount.com
        containers:
          - image: gcr.io/${PROJECT_ID}/heavy-sync:latest
            env:
              - name: FIRESTORE_PROJECT_ID
                value: ${PROJECT_ID}
              - name: OPENAI_API_KEY
                valueFrom:
                  secretKeyRef:
                    name: openai-api-key
                    key: latest
        timeoutSeconds: 10800   # 3 h
        maxRetries: 1
```

Apply with:
```bash
gcloud run jobs replace heavy-sync.yaml --region $REGION
```

### 12.5 Trigger via Cloud Tasks (recommended)

```python
from google.cloud import tasks_v2

tasks_client = tasks_v2.CloudTasksClient()
parent = tasks_client.queue_path(PROJECT_ID, REGION, "heavy-sync-queue")

task = {
  "http_request": {
    "http_method": tasks_v2.HttpMethod.POST,
    "url": f"https://{REGION}-run.googleapis.com/apis/run.googleapis.com/v1/namespaces/{PROJECT_ID}/jobs/heavy-sync-job:run",
    "oidc_token": {"service_account_email": TRIGGER_SA},
    "body": json.dumps({"args": ["--entity_id", ENTITY_ID, "--sync_job_id", SYNC_JOB_ID]}).encode(),
    "headers": {"Content-Type": "application/json"},
  }
}
response = tasks_client.create_task(parent=parent, task=task)
```

Benefits: automatic retry backoff, fully async, Cloud Scheduler-friendly.

### 12.6 Logging & Monitoring Standards

* Use `structlog` or Python `logging` with JSON encoder.
* Mandatory per-log labels: `entity_id`, `sync_job_id`, `processing_stage` (eg. `ocr`, `llm`, `amortisation`).
* Emit custom metric `heavy_sync/duration_seconds` once per job.
* Alerting: **critical** if job exits non-zero ≥ 3 times in 15 min.

### 12.7 Local Reproduction / Debugging

```bash
# Build and run locally
docker build -f Dockerfile.heavy_sync -t heavy-sync:dev .

docker run --rm -e FIRESTORE_EMULATOR_HOST="host.docker.internal:8080" \
  -e ENTITY_ID=demo-entity -e CLIENT_ID=demo-client \
  -e SYNC_JOB_ID=$(uuidgen) \
  -e BILLS_JSON="$(cat tests/fixtures/bills.json)" \
  -e ENTITY_SETTINGS_JSON="$(cat tests/fixtures/settings.json)" \
  heavy-sync:dev
```

Use the Firestore emulator to avoid mutating prod data.

### 12.8 Runbook / Troubleshooting

| Symptom | Likely Cause | Action |
|---------|--------------|--------|
| Job stuck in `Active` for > 3 h | Infinite loop / network hang | Cancel with `gcloud run jobs executions delete`; inspect Cloud Trace |
| `PermissionDenied` on Firestore | SA missing `roles/datastore.user` | Update IAM & redeploy |
| LLM calls rate-limited | Incorrect key or usage spike | Rotate key via Secret Manager version; throttle within `heavy_sync_processor` |
| Execution quota exhausted | Too many concurrent jobs | Introduce concurrency cap in Cloud Scheduler or Cloud Tasks queue |

---

## 13 · Change-Management Checklist

- [ ] **Code:** New `jobs/` module merged & unit-tested
- [ ] **Secrets:** API keys moved to Secret Manager; references in env vars
- [ ] **Build:** CI builds `heavy-sync` image
- [ ] **IAM:** `heavy-sync-sa` created & roles granted
- [ ] **Job:** `heavy-sync-job` deployed in **staging**
- [ ] **Trigger:** Cloud Function / Scheduler updated behind feature flag
- [ ] **Monitoring:** Log-based metrics & alert policies configured
- [ ] **Docs:** This plan updated with final parameters
- [ ] **Go-Live:** Flag enabled in production, monitor for 48 h

---

> _This document is living—keep PRs small but frequent to incrementally refine the migration._ 