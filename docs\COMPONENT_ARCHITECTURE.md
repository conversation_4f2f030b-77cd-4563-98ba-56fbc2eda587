# Component Architecture Guide

*Last verified: 2025-07-17 against actual component structure*

## Overview

The DRCR frontend follows a component-based architecture using React 18.3.1 with TypeScript, shadcn/ui components, and a well-organized directory structure.

## Directory Structure

```
src/components/
├── ui/                     # Shadcn/UI components (don't modify)
├── auth/                   # Authentication components
├── layout/                 # Layout and navigation components
├── entities/               # Entity management components
├── prepayments/           # Prepayment/amortization components
├── transactions/          # Transaction-related components
├── clients/               # Client management components
├── common/                # Shared utility components
├── amortization/          # Amortization-specific components
├── TailwindTest.tsx       # Tailwind CSS testing component
├── XeroOperationDemo.tsx  # Xero integration demo
└── XeroOrganizationSelector.tsx  # Xero org selection
```

## Component Categories

### 1. UI Components (`src/components/ui/`)

**Purpose:** Shadcn/UI component library - DO NOT MODIFY

**Key Components:**
- `alert.tsx`, `alert-dialog.tsx` - Alert notifications
- `button.tsx` - Button variants
- `card.tsx` - Card containers
- `form.tsx`, `input.tsx`, `label.tsx` - Form components
- `table.tsx` - Data tables
- `dialog.tsx`, `sheet.tsx` - Modal/drawer components
- `dropdown-menu.tsx` - Dropdown menus
- `tabs.tsx` - Tab navigation
- `skeleton.tsx` - Loading skeletons
- `sonner.tsx` - Toast notifications
- `status-filter-selector.tsx` - Advanced multi-select filtering (for transactions)
- `user-filter-selector.tsx` - User status/role filtering (for user management)
- `dashboard-filter-selector.tsx` - Client/entity multi-select filtering (for dashboard)

**Usage:**
```typescript
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
```

### 2. Authentication Components (`src/components/auth/`)

**Purpose:** User authentication and route protection

**Components:**
- `LoginForm.tsx` - Login form with Firebase auth
- `SignupForm.tsx` - User registration form
- `ProtectedRoute.tsx` - Route protection wrapper

**Usage Example:**
```typescript
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';

// Protect routes
<ProtectedRoute>
  <DashboardPage />
</ProtectedRoute>
```

### 3. Layout Components (`src/components/layout/`)

**Purpose:** Application layout and navigation

**Components:**
- `AppLayout.tsx` - Main application layout wrapper
- `AppSidebar.tsx` - Application sidebar with navigation
- `ClientSwitcher.tsx` - Client/organization switcher
- `NavMain.tsx` - Main navigation menu
- `NavUser.tsx` - User navigation menu
- `SidebarLayout.tsx` - Sidebar layout wrapper
- `AppShellWithSidebar.tsx` - Consolidated sidebar layout shell (NEW)

**Usage Pattern:**
```typescript
// New consolidated approach (recommended)
import { AppShellWithSidebar } from '@/components/layout/AppShellWithSidebar';
// Used in App.tsx routing - provides single SidebarProvider for all pages
<Route element={<ProtectedRoute><AppShellWithSidebar /></ProtectedRoute>}>
  <Route path="/dashboard" element={<DashboardPage />} />
  <Route path="/account" element={<AccountPage />} />
</Route>

// Legacy individual layout (being phased out)
import { AppLayout } from '@/components/layout/AppLayout';
<AppLayout>
  <PageContent />
</AppLayout>
```

### 4. Entity Management Components (`src/components/entities/`)

**Purpose:** Entity (organization) management and setup

**Components:**
- `EntityDashboard.tsx` - Entity overview dashboard
- `EntityDetailView.tsx` - Detailed entity information
- `EntitySetupWizard.tsx` - Basic entity setup wizard
- `EnhancedEntitySetupWizard.tsx` - Advanced setup wizard
- `EntitySettingsManagement.tsx` - Entity settings configuration (base currency read-only)
- `ConnectNewEntityModal.tsx` - New entity connection modal
- `CreateEntityModal.tsx` - Entity creation modal
- `OAuthCallback.tsx` - OAuth callback handler

**Usage Example:**
```typescript
import { EntitySetupWizard } from '@/components/entities/EntitySetupWizard';
import { EntitySettingsManagement } from '@/components/entities/EntitySettingsManagement';

// Entity setup flow
<EntitySetupWizard
  clientId={clientId}
  onComplete={handleSetupComplete}
  onCancel={handleCancel}
/>

// Entity settings with read-only base currency
<EntitySettingsManagement
  entityId={entityId}
  fetchSettings={handleFetchSettings}
  fetchChartOfAccounts={handleFetchChartOfAccounts}
  saveSettings={handleSaveSettings}
  hideFooter={true}
/>
```

**Important Design Patterns:**
- **Base Currency**: Always read-only, derived from accounting system (Xero/QBO)
- **Settings Modal**: Use `DraggableDialog` for consistent UX
- **Field Access**: Use canonical field names via backend `get_field()` helper
- **Account Filtering**: Use backend-classified accounts (prepayment_candidates) for consistency with wizard
- **Excluded Accounts**: Filter by `Class === 'EXPENSE'` only, sorted by account code

### 5. Prepayments Components (`src/components/prepayments/`)

**Purpose:** Prepayment and amortization schedule management (Core feature)

**Components:**
- `BillsList.tsx` - List of bills for amortization
- `HierarchicalBillsList.tsx` - Tree view of bills with hierarchy
- `BillsToolbar.tsx` - Toolbar for bill operations
- `AmortizationSummary.tsx` - Summary of amortization data
- `AmortizationConfiguration.tsx` - Amortization settings
- `MonthlyScheduleTable.tsx` - Monthly schedule breakdown
- `EditScheduleModal.tsx` - Schedule editing modal
- `BulkEditScheduleModal.tsx` - Bulk schedule editing
- `SideBySideReview.tsx` - Side-by-side schedule review
- `EmptyAmortizationState.tsx` - Empty state display
- `KeyboardShortcutsModal.tsx` - Keyboard shortcuts help

**Usage Example:**
```typescript
import { MonthlyScheduleTable } from '@/components/prepayments/MonthlyScheduleTable';

// Monthly schedule display
<MonthlyScheduleTable
  scheduleEntries={monthlyEntries}
  onEditEntry={handleEntryUpdate}
  currencyCode="GBP"
  isLoading={false}
  selectedCount={summaryData.selectedCount} // For bulk mode detection
/>
```

**Bulk Mode Support:**
All prepayment components now support bulk mode operations:
- **Bulk Mode Detection**: `selectedCount > 1` triggers bulk mode
- **Disabled Editing**: Individual editing disabled in bulk mode
- **Visual Indicators**: Clear messaging when in bulk mode
- **Preserved Functionality**: Bulk posting still works via PostProcessingService

### 6. Transaction Components (`src/components/transactions/`)

**Purpose:** Transaction-related UI components

**Components:**
- `TransactionTable.tsx` - Transaction data table
- `TransactionFilters.tsx` - Transaction filtering controls

**Usage Example:**
```typescript
import { TransactionTable } from '@/components/transactions/TransactionTable';

// Transaction listing
<TransactionTable
  transactions={transactions}
  onRowClick={handleRowClick}
  loading={loading}
/>
```

### 7. Client Management Components (`src/components/clients/`)

**Purpose:** Client management operations

**Components:**
- `CreateClientModal.tsx` - Client creation modal
- `EditClientModal.tsx` - Client editing modal

**Usage Example:**
```typescript
import { CreateClientModal } from '@/components/clients/CreateClientModal';

// Client creation
<CreateClientModal
  open={showModal}
  onClose={handleClose}
  onSuccess={handleClientCreated}
/>
```

### 8. Common Components (`src/components/common/`)

**Purpose:** Shared utility components

**Components:**
- Various shared utilities and common UI patterns

### 9. Amortization Components (`src/components/amortization/`)

**Purpose:** Amortization-specific functionality

**Components:**
- Specialized amortization calculation and display components

### 10. Specialized Components

**Additional Components:**
- `TailwindTest.tsx` - Tailwind CSS testing and validation
- `XeroOperationDemo.tsx` - Xero integration demonstration
- `XeroOrganizationSelector.tsx` - Xero organization selection

## Component Patterns

### Bulk Mode Pattern (Bills Amortization)

**Purpose:** Handles UX for multi-selection operations in Bills Amortization

**Pattern Implementation:**
```typescript
// Component props pattern
interface ComponentProps {
  // ... other props
  selectedCount?: number; // For bulk mode detection
}

// Component implementation
export function Component({ selectedCount = 1, ...props }: ComponentProps) {
  const isBulkMode = selectedCount > 1;
  
  // Disable editing in bulk mode
  const fieldsDisabled = readOnly || isBulkMode;
  
  // Visual indicators
  return (
    <Card>
      <CardHeader>
        <CardTitle>
          Title
          {isBulkMode && (
            <span className="ml-2 text-blue-600 text-sm font-normal">
              (Bulk mode - {selectedCount} bills)
            </span>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Bulk mode messaging */}
        {isBulkMode && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-sm text-blue-800">
              Individual editing disabled in bulk mode. Select a single bill to edit.
            </p>
          </div>
        )}
        
        {/* Form controls with bulk mode styling */}
        <Input
          disabled={fieldsDisabled}
          className={isBulkMode ? 'bg-gray-50 text-gray-600 cursor-not-allowed' : ''}
          title={isBulkMode ? 'Editing disabled in bulk mode' : ''}
        />
      </CardContent>
    </Card>
  );
}
```

**Components Supporting Bulk Mode:**
- `MonthlyScheduleTable` - Disables amount editing
- `AmortizationConfiguration` - Disables config changes
- `AmortizationSummary` - Shows bulk mode indicators
- `BillsToolbar` - Updates button labels for bulk operations
- `BillsAmortizationPage` - Orchestrates bulk mode state

**Key UX Principles:**
1. **Clear Visual Feedback**: Users immediately know they're in bulk mode
2. **Disabled Editing**: Prevents confusion from editing combined data
3. **Preserved Functionality**: Bulk posting still works
4. **Consistent Messaging**: All components show similar bulk indicators

### Standard Component Structure

```typescript
// 1. Imports
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { SomeService } from '@/services/some.service';
import type { ComponentProps } from '@/types/component.types';

// 2. Props interface
interface MyComponentProps {
  id: string;
  title: string;
  onUpdate?: (data: any) => void;
  className?: string;
}

// 3. Component implementation
export function MyComponent({ id, title, onUpdate, className }: MyComponentProps) {
  // 4. State hooks
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState(null);
  const [error, setError] = useState<string | null>(null);

  // 5. Effects
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const result = await SomeService.getData(id);
        setData(result);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id]);

  // 6. Event handlers
  const handleUpdate = async (newData: any) => {
    try {
      await SomeService.updateData(id, newData);
      onUpdate?.(newData);
    } catch (err) {
      setError(err.message);
    }
  };

  // 7. Render
  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <Card className={className}>
      <h2>{title}</h2>
      {/* Component content */}
    </Card>
  );
}
```

### Loading States Pattern

```typescript
// Standard loading state handling
const [loading, setLoading] = useState(false);

const performAction = async () => {
  setLoading(true);
  try {
    await someAsyncOperation();
  } catch (error) {
    toast.error(error.message);
  } finally {
    setLoading(false);
  }
};

// In render
{loading ? (
  <Skeleton className="h-4 w-full" />
) : (
  <ActualContent />
)}
```

### Form Handling Pattern

```typescript
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const formSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email'),
});

type FormData = z.infer<typeof formSchema>;

export function MyForm() {
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      email: '',
    },
  });

  const onSubmit = async (data: FormData) => {
    try {
      await SomeService.submitForm(data);
      toast.success('Form submitted successfully');
    } catch (error) {
      toast.error(error.message);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit">Submit</Button>
      </form>
    </Form>
  );
}
```

### Modal Component Pattern

```typescript
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';

interface ModalProps {
  open: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

export function MyModal({ open, onClose, title, children }: ModalProps) {
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        {children}
      </DialogContent>
    </Dialog>
  );
}
```

## State Management Integration

### Zustand Store Usage

```typescript
import { useAuthStore } from '@/store/auth.store';

export function MyComponent() {
  const { user, isLoading, signOut } = useAuthStore();

  if (isLoading) return <div>Loading...</div>;
  
  return (
    <div>
      <p>Welcome, {user?.displayName}</p>
      <Button onClick={signOut}>Sign Out</Button>
    </div>
  );
}
```

### URL State Synchronization

```typescript
import { useURLSync } from '@/hooks/useURLSync';

export function FilterableComponent() {
  const { filters, updateFilter } = useURLSync({
    defaultFilters: { status: 'active', page: 1 },
    paramKeys: ['status', 'page', 'search']
  });

  // Component uses filters and updates URL automatically
}
```

### Advanced Filtering Patterns

**Multi-Select Filter Component (User Management):**
```typescript
import { UserFilterSelector } from '@/components/ui/user-filter-selector';
import { USER_STATUS_OPTIONS, USER_ROLE_OPTIONS } from '@/constants/user-filters';

export function UserManagementPage() {
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
  
  // AND logic filtering: status filters OR role filters, combined with AND
  const filteredUsers = useMemo(() => {
    if (selectedFilters.length === 0) return users;
    
    const statusFilters = selectedFilters.filter(f => 
      ['active', 'inactive', 'invited', 'suspended'].includes(f)
    );
    const roleFilters = selectedFilters.filter(f => 
      ['firm_admin', 'firm_staff'].includes(f)
    );
    
    return users.filter(user => {
      const statusMatch = statusFilters.length === 0 || statusFilters.includes(user.status);
      const roleMatch = roleFilters.length === 0 || roleFilters.includes(user.role);
      return statusMatch && roleMatch;
    });
  }, [users, selectedFilters]);

  return (
    <UserFilterSelector
      selectedFilters={selectedFilters}
      onFiltersChange={setSelectedFilters}
      filterOptions={getFilterOptionsWithCounts()}
    />
  );
}
```

**Multi-Select Filter Component (Dashboard):**
```typescript
import { DashboardFilterSelector } from '@/components/ui/dashboard-filter-selector';
import { CLIENT_STATUS_OPTIONS, CONNECTION_STATUS_OPTIONS, ENTITY_TYPE_OPTIONS } from '@/constants/dashboard-filters';

export function DashboardPage() {
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
  
  // AND logic filtering: client status AND connection status AND entity type
  const filteredClients = useMemo(() => {
    if (selectedFilters.length === 0) return clients;
    
    const statusFilters = selectedFilters.filter(f => 
      ['action_needed', 'error', 'ok'].includes(f)
    );
    const connectionFilters = selectedFilters.filter(f => 
      ['connected', 'disconnected'].includes(f)
    );
    const entityTypeFilters = selectedFilters.filter(f => 
      ['xero', 'qbo', 'manual'].includes(f)
    );
    
    return clients.filter(client => {
      const statusMatch = statusFilters.length === 0 || statusFilters.includes(client.overall_status);
      const connectionMatch = connectionFilters.length === 0 || client.entities?.some(entity => {
        const connectionStatus = entity.connection_status === 'active' ? 'connected' : 'disconnected';
        return connectionFilters.includes(connectionStatus);
      });
      const entityTypeMatch = entityTypeFilters.length === 0 || client.entities?.some(entity => 
        entityTypeFilters.includes(entity.type || 'manual')
      );
      
      return statusMatch && connectionMatch && entityTypeMatch;
    });
  }, [clients, selectedFilters]);

  return (
    <DashboardFilterSelector
      selectedFilters={selectedFilters}
      onFiltersChange={setSelectedFilters}
      filterOptions={getFilterOptionsWithCounts()}
    />
  );
}
```

## Performance Optimization

### Memoization
```typescript
import { memo, useMemo, useCallback } from 'react';

// Memoize expensive components
export const ExpensiveComponent = memo(({ data }: { data: any[] }) => {
  const processedData = useMemo(() => {
    return data.map(item => expensiveProcessing(item));
  }, [data]);

  return <div>{/* Render processed data */}</div>;
});

// Memoize callback functions
const handleClick = useCallback((id: string) => {
  // Handle click
}, [dependency]);
```

### Lazy Loading
```typescript
import { lazy, Suspense } from 'react';

const LazyComponent = lazy(() => import('./HeavyComponent'));

export function ParentComponent() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LazyComponent />
    </Suspense>
  );
}
```

## Testing Components

### Component Testing Pattern
```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { MyComponent } from './MyComponent';

describe('MyComponent', () => {
  it('renders correctly', () => {
    render(<MyComponent title="Test" />);
    expect(screen.getByText('Test')).toBeInTheDocument();
  });

  it('handles click events', () => {
    const handleClick = vi.fn();
    render(<MyComponent onClick={handleClick} />);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalled();
  });
});
```

## Accessibility Guidelines

### ARIA Labels
```typescript
// Proper ARIA labeling
<Button
  aria-label="Delete schedule"
  onClick={handleDelete}
>
  <TrashIcon />
</Button>

// Form accessibility
<FormField
  control={form.control}
  name="email"
  render={({ field }) => (
    <FormItem>
      <FormLabel>Email Address</FormLabel>
      <FormControl>
        <Input 
          {...field}
          type="email"
          aria-describedby="email-error"
        />
      </FormControl>
      <FormMessage id="email-error" />
    </FormItem>
  )}
/>
```

### Keyboard Navigation
```typescript
// Keyboard event handling
const handleKeyDown = (event: React.KeyboardEvent) => {
  if (event.key === 'Enter' || event.key === ' ') {
    event.preventDefault();
    handleAction();
  }
};

<div
  role="button"
  tabIndex={0}
  onKeyDown={handleKeyDown}
  onClick={handleAction}
>
  Interactive Element
</div>
```

## Error Boundaries

### Error Boundary Implementation
```typescript
import { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || <div>Something went wrong.</div>;
    }

    return this.props.children;
  }
}
```

## Best Practices

### 1. Component Composition
```typescript
// ✅ Good - Composable components
<Card>
  <CardHeader>
    <CardTitle>Schedule Details</CardTitle>
  </CardHeader>
  <CardContent>
    <ScheduleForm />
  </CardContent>
</Card>

// ❌ Bad - Monolithic component
<ScheduleCardWithEverything />
```

### 2. Prop Drilling Prevention
```typescript
// ✅ Good - Use context or state management
const { user } = useAuthStore();

// ❌ Bad - Prop drilling
<ParentComponent user={user}>
  <ChildComponent user={user}>
    <GrandChildComponent user={user} />
  </ChildComponent>
</ParentComponent>
```

### 3. TypeScript Usage
```typescript
// ✅ Good - Proper typing
interface ComponentProps {
  data: ScheduleData[];
  onUpdate: (schedule: ScheduleData) => void;
}

// ❌ Bad - Any types
interface ComponentProps {
  data: any;
  onUpdate: (data: any) => void;
}
```

### 4. Error Handling
```typescript
// ✅ Good - User-friendly error states
if (error) {
  return (
    <Card>
      <CardContent>
        <p>Unable to load data. Please try again.</p>
        <Button onClick={handleRetry}>Retry</Button>
      </CardContent>
    </Card>
  );
}

// ❌ Bad - Technical error display
if (error) {
  return <div>Error: {error.stack}</div>;
}
```

## Page Components

### BillingPage (`src/pages/BillingPage.tsx`)

**Purpose:** Portfolio-wide credit billing and usage tracking
**Last Updated:** 2025-07-17

**Key Features:**
- **Portfolio Credit Overview:** Aggregated credit balance across all clients
- **Credit Usage History:** Per-sync credit consumption tracking
- **Credit Packages:** Available credit purchase options

**Usage History Table Structure:**
```typescript
interface UsageHistoryDisplay {
  date: string;           // Sync job start date
  entity: string;         // Entity name
  credits_used: number;   // Total credits consumed
  status: 'completed' | 'failed' | 'processing';
}
```

**Component Dependencies:**
- `TokenUsageService` - Credit usage data fetching
- `EntitiesService` - Entity credit information
- `ClientStore` - Client data management

**Key Implementation Details:**
- Displays **credit usage only** (not token/cost details)
- Hides technical LLM provider information (OpenAI/Mistral)
- Shows user-facing billing information (1 credit = 1 page processed)
- Includes sync job status badges for completion tracking

**Usage Pattern:**
```typescript
// Credit usage display
{job.credit_usage?.credits_used ? 
  job.credit_usage.credits_used.toLocaleString() : '—'
}

// Status badge with color coding
<Badge variant={job.status === 'completed' ? 'default' : 
               job.status === 'failed' ? 'destructive' : 'secondary'}>
  {job.status || 'Unknown'}
</Badge>
```

## Related Documentation

- **Service Layer Guide:** `docs/SERVICES_GUIDE.md` - How components integrate with services
- **API Reference:** `docs/API_REFERENCE.md` - API endpoints used by components
- **Troubleshooting:** `docs/TROUBLESHOOTING.md` - Common component issues
- **Development Guidelines:** `docs/DEVELOPMENT_GUIDELINES.md` - Coding standards