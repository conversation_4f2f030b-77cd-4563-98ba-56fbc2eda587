# Deploy <PERSON>ylas Email Poller Cloud Function

Write-Host "Deploying <PERSON><PERSON><PERSON> Email Poller to GCP..." -ForegroundColor Green

$envVars = "GCP_PROJECT_ID=drcr-d660a," +
           "NYLAS_CLIENT_ID=672d85ca-ad40-4d7d-b6ee-fbbd1d7e1d29," +
           "NYLAS_API_KEY=nyk_v0_243L0pUklhtwpk3Sn1wW4uaSsyiyPz9ApTJxb6ewKyatPSsbIFkQG8ysfnE72D6M," +
           "NYLAS_GRANT_ID=c12ff227-31ed-4099-b70f-6fde75f02e02," +
           "NYLAS_API_URI=https://api.us.nylas.com/v3," +
           "POLLING_BATCH_SIZE=50," +
           "POLLING_TIMEOUT_SECONDS=30," +
           "LOG_LEVEL=INFO," +
           "ENABLE_FIRESTORE_LOGGING=true"

gcloud functions deploy nylas_email_poller `
  --runtime python39 `
  --trigger-http `
  --allow-unauthenticated `
  --timeout 540s `
  --memory 512MB `
  --region europe-west2 `
  --set-env-vars $envVars

Write-Host "Deployment complete!" -ForegroundColor Green
Write-Host "Function URL:" -ForegroundColor Yellow
Write-Host "https://europe-west2-drcr-d660a.cloudfunctions.net/nylas_email_poller" -ForegroundColor Cyan

Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Test the function: curl -X POST https://europe-west2-drcr-d660a.cloudfunctions.net/nylas_email_poller" -ForegroundColor White
Write-Host "2. Set up Cloud Scheduler for automated polling" -ForegroundColor White