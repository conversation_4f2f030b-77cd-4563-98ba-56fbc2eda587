# API Reference Guide

*Last verified: 2025-01-17 against actual api.ts implementation*

## API Configuration

### Base Configuration
- **Development URL:** `http://localhost:8081`
- **Production URL:** `https://drcr-d660a.web.app`
- **Authentication:** Bearer token via Firebase ID token
- **Content-Type:** `application/json`
- **Timeout:** 15 seconds (configurable)

### Authentication Headers
```typescript
headers: {
  'Authorization': `Bearer ${firebaseIdToken}`,
  'Content-Type': 'application/json'
}
```

## Complete API Endpoints

### Client Management

#### Get All Clients
```typescript
GET /clients/
Response: { clients: ClientSummary[] }
```

#### Get Clients with Pagination
```typescript
GET /clients/summary?page=1&limit=10&client_filter=search&status_filter=active
Response: {
  clients: ClientSummary[];
  pagination: {
    current_page: number;
    page_size: number;
    total_items: number;
    total_pages: number;
  };
}
```

#### Create Client
```typescript
POST /clients/
Body: {
  name: string;
  contact_name?: string;
  contact_email?: string;
  contact_phone?: string;
}
Response: {
  message: string;
  client_id: string;
  name: string;
}
```

#### Get Client Details
```typescript
GET /clients/{clientId}
Response: ClientSummary
```

#### Update Client
```typescript
PUT /clients/{clientId}
Body: {
  name?: string;
  contact_name?: string;
  contact_email?: string;
  contact_phone?: string;
}
Response: { message: string }
```

### Entity Management

#### Get Entities for Client
```typescript
GET /entities/?client_id={clientId}
Response: { entities: EntitySummary[] }
```

#### Get Entity Details
```typescript
GET /entities/{entityId}
Response: EntitySummary & { settings?: EntitySettings }
```

#### Update Entity Settings
```typescript
PUT /entities/{entityId}/settings
Body: Partial<EntitySettings>
Response: { message: string }
```

#### Check Connection Status
```typescript
GET /entities/{entityId}/connection/status
Response: {
  entity_id: string;
  entity_name: string;
  type: string;
  connection_status: {
    status: 'active' | 'error' | 'disconnected' | 'syncing' | 'pending';
    last_checked?: string;
    error_message?: string;
  };
}
```

#### Disconnect Entity
```typescript
POST /entities/{entityId}/connection/disconnect
Response: { message: string }
```

#### Get Entity Accounts
```typescript
GET /entities/{entityId}/accounts
Response: { accounts: Account[] }
```

### Entity Setup Wizard

#### Get Wizard Analysis
```typescript
GET /entities/{entityId}/analysis/wizard
Response: {
  entity_id: string;
  entity_name: string;
  organization_info: {
    name: string;
    base_currency: string;
    financial_year_start?: string;
  };
  bills_analysis: {
    bills_with_attachments: number;
    total_bills: number;
    scanning_cost_estimate: string;
    expected_prepayments: string;
  };
  accounts_analysis: {
    prepayment_asset_accounts: Array<{
      code: string;
      name: string;
      recommended: boolean;
    }>;
    relevant_expense_accounts: Array<{
      code: string;
      name: string;
    }>;
  };
  recommendations: {
    sync_start_date: string;
    sync_frequency: string;
    enable_ai_scanning: boolean;
    selected_asset_accounts: string[];
    excluded_accounts: string[];
  };
}
```

#### Complete Entity Setup
```typescript
POST /entities/{entityId}/setup/complete
Body: {
  enable_ai_scanning: boolean;
  sync_settings: {
    sync_start_date: string;
    sync_frequency: string;
    auto_sync_enabled: boolean;
    sync_invoices: boolean;
    sync_bills: boolean;
    sync_payments: boolean;
    sync_bank_transactions: boolean;
    sync_journal_entries: boolean;
    sync_spend_money: boolean;
  };
  account_settings: {
    prepayment_asset_accounts: string[];
    excluded_accounts: string[];
    default_expense_account?: string;
    base_currency: string;
  };
}
Response: { message: string; entity_id: string }
```

#### Get Bill Aggregates
```typescript
GET /entities/{entityId}/analysis/bill-aggregates?date_range_months=12&amount_threshold=0
Response: {
  aggregates: {
    [month: string]: {
      by_account: { [accountCode: string]: { bills: number; with_attachments: number; total_amount: number; } };
      by_supplier: { [supplier: string]: { bills: number; with_attachments: number; total_amount: number; } };
      total_bills: number;
      total_with_attachments: number;
      total_amount: number;
    };
  };
  account_classifications: {
    prepayment_candidates: string[];
    exclude_recommended: string[];
    all_accounts: { [accountCode: string]: { name: string; type: string; class: string; } };
  };
  date_range: { start_date: string; end_date: string; months: number; };
  total_bills: number;
  total_with_attachments: number;
}
```

### Xero Integration

#### Initiate Xero Connection
```typescript
GET /xero/connect/initiate/{clientId}
Response: { authorization_url: string }
```

#### Get Xero Configuration
```typescript
GET /clients/{clientId}/xero/configure?entities=comma,separated,ids
Response: {
  client: { client_id: string; name: string; status: string; };
  entities: Array<{
    entity_id: string;
    entity_name: string;
    type: string;
    status: string;
    connection_status: string;
    settings: any;
    requires_configuration: boolean;
  }>;
  has_xero_entities: boolean;
  configuration_complete: boolean;
}
```

#### Revoke Xero Connection
```typescript
POST /xero/entities/{entityId}/revoke
Response: { message: string }
```

#### Get Available Organizations
```typescript
GET /xero/clients/{clientId}/xero/available-organizations
Response: {
  client_id: string;
  organizations: Array<{
    tenant_id: string;
    tenant_name: string;
    is_already_connected: boolean;
    connection_type: string;
  }>;
  reconnections_processed?: number;
  message?: string;
}
```

#### Connect Selected Organization
```typescript
POST /xero/clients/{clientId}/xero/connect-organization
Body: { tenant_id: string }
Response: {
  message: string;
  entity: {
    entity_id: string;
    entity_name: string;
    requires_configuration: boolean;
    is_new_connection: boolean;
  };
}
```

### Schedule Management

#### Calculate Preview
```typescript
POST /schedules/calculate-preview
Body: {
  amount: number;
  start_date: string; // "YYYY-MM-DD"
  end_date: string;
  calculation_method?: "auto" | "day_based" | "equal_monthly";
  entity_id: string;
}
Response: {
  calculation_method: "day_based" | "equal_monthly";
  total_months: number;
  monthly_entries: Array<{
    month_date: string;
    amount: number;
  }>;
}
```

#### Get Schedule Details
```typescript
GET /schedules/{scheduleId}
Response: {
  id: string;
  status: "pending_configuration" | "pending_confirmation" | "proposed" | "confirmed" | "posted";
  calculation_method: "day_based" | "equal_monthly";
  detection_method: "llm_only" | "gl_coding";
  transaction_id: string;
  amount: number;
  monthly_entries: Array<MonthlyEntry>;
  account_code?: string;
  expense_account_code?: string;
  description?: string;
  created_at: string;
  updated_at: string;
}
```

#### Update Schedule Metadata
```typescript
PUT /schedules/{scheduleId}
Body: {
  account_code?: string;         // Asset/prepayment account code
  expense_account_code?: string; // Expense account code  
  description?: string;          // Schedule description
}
Response: {
  message: string;
  schedule_id: string;
  status_progression?: { from: string; to: string; };
}
```

#### Recalculate Schedule
```typescript
PUT /schedules/{scheduleId}/recalculate
Body: {
  amount: number;
  start_date: string;
  end_date: string;
  calculation_method?: "auto" | "day_based" | "equal_monthly";
}
// ⚠️ WARNING: This destroys all existing monthly entries and regenerates them
```

#### Preview Changes
```typescript
PUT /schedules/{scheduleId}/preview-changes
Body: {
  amount?: number;
  start_date?: string;
  end_date?: string;
  calculation_method?: "auto" | "day_based" | "equal_monthly";
}
Response: {
  monthly_entries: Array<{ month_date: string; amount: number; }>;
  calculation_method: string;
}
```

#### Confirm Schedule
```typescript
POST /schedules/{scheduleId}/confirm
Response: { message: string }
```

#### Skip Schedule
```typescript
POST /schedules/{scheduleId}/skip
Body: { reason: string }
Response: { message: string }
```

#### Update Schedule Status
```typescript
PUT /schedules/{scheduleId}/status
Body: { status: string }
Response: { message: string }
```

### Monthly Entry Operations

#### Update Individual Entry
```typescript
PUT /schedules/{scheduleId}/entries/{entryIndex}
Body: { amount: number }
Response: {
  success: boolean;
  message: string;
  updated_entry: {
    entry_index: number;
    amount: number;
    month_date: string;
    status: string;
  };
}
```

#### Post Single Entry
```typescript
POST /schedules/{scheduleId}/entries/{entryIndex}/post
Response: {
  message: string;
  status: 'posted' | 'failed';
  journal_id?: string;
  error?: string;
}
```

#### Bulk Post Entries
```typescript
POST /schedules/{scheduleId}/entries/bulk-post
Body: { entry_indices: number[] }  // e.g., [0, 1, 2]
Response: {
  message: string;
  posted_count: number;
  failed_count: number;
  results: Array<{
    entry_index: number;
    status: 'posted' | 'failed';
    journal_id?: string;
    error?: string;
  }>;
}
```

#### Bulk Post Multiple Schedules (Legacy)
```typescript
// Legacy approach: Posts schedules individually  
PostProcessingService.saveAndPostReady(
  selectedScheduleIds: string[],
  amortizationConfig: AmortizationConfig,
  monthlySchedule: MonthlyScheduleEntry[],
  handleDataRefresh: Function
)
// Uses individual POST /schedules/{scheduleId}/entries/bulk-post calls
```

#### Consolidated Posting (New - Recommended)
```typescript
POST /schedules/bulk-post-consolidated
Body: string[]  // Array of schedule IDs: ["schedule1", "schedule2"]
Response: {
  success: boolean;
  message: string;
  summary: {
    schedules_processed: number;
    journals_created: number;
    entries_posted: number;
    entries_failed: number;
    consolidation_groups: number;
  };
  consolidation_results: Array<{
    type: "consolidated" | "individual";
    journal_id: string;
    schedule_count: number;
    schedule_ids: string[];
    success: boolean;
    error?: string;
  }>;
}
```

**Consolidation Benefits:**
- **Reduces journal clutter**: Groups schedules with same transaction/accounts/dates
- **Improves organization**: Multiple expense lines per journal instead of separate journals
- **Maintains accuracy**: All accounting detail preserved within consolidated journals
- **Better performance**: Single API call vs multiple individual calls

**Frontend Integration:**
```typescript
// PrepaymentsService method
PrepaymentsService.postConsolidatedEntries(scheduleIds: string[])

// PostProcessingService uses consolidated posting automatically
PostProcessingService.saveAndPostReady() // Now uses consolidated API
```

### Dashboard & Reporting

#### Get Client Dashboard
```typescript
GET /reports/dashboard?client_id={clientId}&entity_id={entityId}
Response: {
  proposed: { count: number; total_amount: number; };
  approved: { count: number; total_amount: number; };
  this_month: { count: number; total_amount: number; };
  recent_transactions: any[];
}
```

#### Get Transactions
```typescript
GET /transactions/?client_id={clientId}&entity_id={entityId}&transaction_type=ACCPAY&page=1&limit=10
Response: {
  transactions: Transaction[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

// Transaction structure includes:
Transaction {
  id: string;
  document_number: string;
  total: number;
  amount_due: number;
  amount_paid: number;
  currency: string;
  status: string;
  has_amortization_schedules?: boolean;
  skip_reason?: string;  // Present when transaction was skipped (e.g., "below_scanning_threshold")
  metadata: {
    contact_name: string;
    recommended_action?: string;     // AI recommendation: "create_prepayment", "no_prepayment", etc.
    confidence_score?: number;       // AI confidence (0-1)
    has_prepayment_line_items?: boolean;
    gl_based_analysis_completed?: boolean;
    llm_based_analysis_completed?: boolean;
    skip_reason?: string;           // Also available in metadata
    // ... other metadata fields
  };
  line_items: LineItem[];
}
```

#### Get Dashboard Transactions
```typescript
GET /transactions/dashboard?client_id={clientId}&entity_id={entityId}&status_filters=pending_configuration&status_filters=proposed
Response: {
  transactions: any[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}
```

#### Get Specific Transaction
```typescript
GET /transactions/{transactionId}
Response: TransactionDetails
```

#### Get Amortization Report
```typescript
GET /reports/amortization?client_id={clientId}&entity_id={entityId}&year=2024&month=1
Response: AmortizationReportData
```

### Additional Features

#### Get Contacts
```typescript
GET /contacts?entity_id={entityId}&page=1&limit=10&name_filter=search
Response: ContactsListResponse
```

#### Get Audit Logs
```typescript
GET /audit?entity_id={entityId}&page=1&limit=10&event_category=schedule&date_from=2024-01-01
Response: AuditLogsResponse
```

#### Get Attachment
```typescript
GET /attachments/{attachmentId}
Response: Blob (binary file data)
```

#### Create Bulk Schedules
```typescript
POST /transactions/{transactionId}/schedules/bulk-create
Body: {
  line_item_ids: string[];
  amortization_start_date: string;
  number_of_periods: number;
  amortization_account_code: string;
  expense_account_code: string;
  notes?: string;
}
Response: {
  message: string;
  created_schedules: Array<{
    schedule_id: string;
    line_item_id: string;
    status: string;
  }>;
  total_created: number;
}
```

## Status System Reference

### Schedule Statuses
- `pending_configuration` - Missing account codes (LLM-detected schedules)
- `pending_confirmation` - Reviewed, ready for final confirmation
- `proposed` - Ready for user review and approval (legacy compatibility)
- `confirmed` - Approved, ready to post to accounting system
- `posted` - Successfully posted to Xero
- `partially_posted` - Some entries posted, some pending
- `skipped` - User chose not to amortize
- `cancelled` - User cancelled
- `error` - Error occurred

### Actionable Statuses
Statuses that require user attention:
- `pending_configuration`
- `pending_confirmation` 
- `proposed`

## Error Handling

### Common Error Codes
- `400` - Invalid request data
- `401` - Authentication required
- `403` - Permission denied  
- `404` - Resource not found
- `422` - Validation error
- `500` - Server error

### Error Response Format
```typescript
{
  detail: string;        // Error message
  status_code: number;   // HTTP status
}
```

## Notes

### Known Implementation Gaps
1. **Manual Schedule Creation** (`POST /schedules`) - Documented but not implemented
2. **Direct Status Updates** (`PUT /schedules/{id}/status`) - May not be fully implemented

### Request Format Discrepancies
- **Bulk Post**: Documentation shows `{entry_indices: [...]}`, implementation may use direct array

### Caching Behavior
- Most GET requests are cached with 5-minute TTL
- Cache can be disabled with `cache: false` parameter
- Mutations automatically invalidate related cache entries