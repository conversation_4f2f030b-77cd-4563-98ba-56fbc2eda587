# DRCR Backend API Reference

**Complete validated API documentation for all 97 endpoints across 17 route modules**
*Last Updated: 2025-07-23 | Validated against actual FastAPI implementation*

## API Base Information

**Base URL:** `http://localhost:8081` (development) | `https://drcr-d660a-rest-api.uc.r.appspot.com` (production)
**Authentication:** Bearer token (Firebase ID token) in `Authorization` header
**Content-Type:** `application/json`

## Route Groups Overview

### Core Business Resources
- **Authentication (`/auth`)** - User management, password reset, firm registration (14 endpoints)
- **Firms (`/firms`)** - Firm information and summaries (2 endpoints)
- **Clients (`/clients`)** - Client management and relationships (12 endpoints)
- **Entities (`/entities`)** - Business entity configuration and connections (22 endpoints)

### Financial Data Management
- **Transactions (`/transactions`)** - Bills, invoices, financial data (6 endpoints)
- **Schedules (`/schedules`)** - Amortization schedule management (15 endpoints)
- **Manual Journals (`/manual-journals`)** - Manual journal processing (3 endpoints)
- **Reports (`/reports`)** - Financial reports and dashboards (2 endpoints)

### Integration & Operations
- **Xero Integration (`/xero`)** - Xero OAuth and data sync (7 endpoints)
- **Sync Operations (`/sync`)** - Data synchronization management (4 endpoints)
- **Attachments (`/attachments`)** - Document attachment handling (1 endpoint)
- **Contacts (`/contacts`)** - Contact/counterparty management (8 endpoints)

### System & Analytics
- **Audit Logs (`/audit`)** - System audit and logging (8 endpoints)
- **Token Usage (`/token-usage`)** - AI token analytics (3 endpoints)
- **Comments (`/comments`)** - Universal commenting system with mentions (6 endpoints)
- **Invoices** - Invoice-specific operations (3 endpoints)

## Authentication Endpoints (`/auth`) - 14 endpoints

### Password Management
- `POST /auth/forgot-password` - Request password reset email
- `POST /auth/reset-password` - Reset password with token (auto-activates invited users)
- `GET /auth/verify-reset-token/{token}` - Verify reset token validity
- `PUT /auth/change-password` - Change password for authenticated user

### User Management
- `GET /auth/me` - Get current user profile with firm details
- `PUT /auth/me` - Update current user profile
- `GET /auth/users` - List all firm users (pagination support)
- `GET /auth/users/{user_id}` - Get specific user details
- `PUT /auth/users/{user_id}/role` - Update user role and client assignments
- `PUT /auth/users/{user_id}/status` - Update user status (activate/deactivate)
- `DELETE /auth/users/{user_id}` - Remove user from firm

### Firm & Invitations
- `POST /auth/register-firm` - Register new firm (onboarding)
- `POST /auth/invite-user` - Invite new user to firm (status: "invited")
- `POST /auth/users/{user_id}/resend-invite` - Resend invitation email

**Note:** Complete invitation flow documented in `docs/USER_INVITATION_FLOW.md`

## Clients Endpoints (`/clients`) - 12 endpoints

### Client CRUD Operations
- `POST /clients/` - Create new client
- `POST /clients/wizard` - Create client via setup wizard
- `GET /clients/` - List clients with filters and pagination
- `GET /clients/summary` - Get clients summary with entity counts
- `GET /clients/{client_id}` - Get specific client details
- `PUT /clients/{client_id}` - Update client information
- `DELETE /clients/{client_id}` - Soft delete client

### Client Utilities
- `GET /clients/{client_id}/entities` - List entities for client
- `GET /clients/{client_id}/xero/configure` - Xero configuration status
- `GET /clients/enums/client-types` - Available client types
- `GET /clients/enums/client-sizes` - Available client sizes  
- `GET /clients/enums/client-statuses` - Available client statuses

## Entities Endpoints (`/entities`) - 22 endpoints

### Entity Management
- `GET /entities/` - List entities with filters
- `GET /entities/{entity_id}` - Get entity details with connection status
- `POST /entities/` - Create new entity
- `PUT /entities/{entity_id}` - Update entity information
- `DELETE /entities/{entity_id}` - Delete entity
- `PUT /entities/{entity_id}/settings` - Update entity settings

### Connection Management
- `GET /entities/{entity_id}/connection/status` - Check connection status
- `POST /entities/{entity_id}/connection/disconnect` - Disconnect from platform
- `POST /entities/{entity_id}/connection/test` - Test connection

### OAuth Flow
- `POST /entities/{entity_id}/oauth/initiate` - Start OAuth flow
- `POST /entities/{entity_id}/oauth/callback` - Handle OAuth callback
- `POST /entities/{entity_id}/oauth/revoke` - Revoke OAuth tokens

### Sync Operations
- `POST /entities/{entity_id}/sync/trigger` - Trigger manual sync
- `GET /entities/{entity_id}/sync/status` - Get sync status
- `GET /entities/{entity_id}/accounts` - Get chart of accounts

### Analytics & Configuration
- `GET /entities/{entity_id}/dashboard` - Entity dashboard data
- `GET /entities/{entity_id}/health` - Entity health check
- `GET /entities/{entity_id}/configuration/status` - Configuration status
- `GET /entities/{entity_id}/analysis/wizard` - Analysis wizard data
- `GET /entities/{entity_id}/analysis/bill-aggregates` - Bill analysis
- `POST /entities/{entity_id}/setup/complete` - Complete entity setup
- `GET /entities/enums` - Entity enums and options

## Schedules Endpoints (`/schedules`) - 15 endpoints

### Schedule Management
- `POST /schedules/calculate-preview` - Calculate amortization preview
- `POST /schedules/` - Create new schedule
- `GET /schedules/{schedule_id}` - Get schedule details
- `GET /schedules/{schedule_id}/attachments` - Get schedule attachments
- `PUT /schedules/{schedule_id}` - Update schedule
- `PUT /schedules/{schedule_id}/recalculate` - Recalculate schedule
- `PUT /schedules/{schedule_id}/preview-changes` - Preview schedule changes
- `PUT /schedules/{schedule_id}/status` - Update schedule status

### Schedule Actions
- `POST /schedules/{schedule_id}/confirm` - Confirm proposed schedule
- `POST /schedules/{schedule_id}/skip` - Skip schedule processing

### Entry Management
- `POST /schedules/{schedule_id}/entries/{entry_index}/post` - Post single entry to Xero
- `POST /schedules/{schedule_id}/entries/bulk-post` - Bulk post entries to Xero
- `PUT /schedules/{schedule_id}/entries/{entry_index}` - Update monthly entry

### Consolidated Posting (New)
- `POST /schedules/bulk-post-consolidated` - **Post multiple schedules with consolidated journal creation**
  - **Purpose**: Groups schedules with same transaction/accounts/dates into consolidated journals
  - **Body**: `List[str]` - Array of schedule IDs
  - **Returns**: Consolidation summary with journal IDs and effectiveness metrics
  - **Impact**: Reduces Xero journal clutter by up to 50% while maintaining accounting detail

## Transactions Endpoints (`/transactions`) - 6 endpoints

### Transaction Operations
- `GET /transactions/` - List transactions with filters and pagination
- `GET /transactions/dashboard` - Dashboard transactions view
- `GET /transactions/{transaction_id}` - Get transaction details
- `GET /transactions/{transaction_id}/schedules` - Get related schedules
- `GET /transactions/{transaction_id}/attachments` - Get transaction attachments
- `POST /transactions/` - Create new transaction
- `PUT /transactions/{transaction_id}` - Update transaction
- `DELETE /transactions/{transaction_id}` - Delete transaction

## Xero Integration Endpoints (`/xero`) - 7 endpoints

### OAuth & Connection
- `GET /xero/connect/initiate/{client_id}` - Start Xero OAuth flow
- `GET /xero/callback` - Handle Xero OAuth callback
- `GET /xero/callback/organizations` - List available Xero organizations
- `POST /xero/clients/{client_id}/xero/connect-organization` - Connect Xero organization
- `GET /xero/clients/{client_id}/xero/available-organizations` - Get available orgs

### Entity Configuration
- `GET /xero/entities/{entity_id}/accounts` - Get Xero chart of accounts
- `PUT /xero/entities/{entity_id}/settings` - Update Xero entity settings
- `POST /xero/entities/{entity_id}/revoke` - Revoke Xero connection

## Sync Operations Endpoints (`/sync`) - 4 endpoints

### Sync Management
- `GET /sync/health` - Sync system health check
- `POST /sync/process-heavy/{entity_id}` - Trigger heavy processing (includes token usage tracking)
- `GET /sync/status/{sync_job_id}` - Get sync job status (includes token usage data)
- `GET /sync/entity/{entity_id}/jobs` - List entity sync jobs
- `GET /sync/entity/{entity_id}/processing-summary` - Get processing summary

**Token Usage**: Heavy processing now properly tracks LLM token consumption in `SYNC_JOBS.token_usage` for accurate billing.

## Additional Route Groups

### Firms (`/firms`) - 2 endpoints
- `GET /firms/{firm_id}` - Get firm details
- `GET /firms/{firm_id}/summary` - Get firm summary statistics

### Reports (`/reports`) - 2 endpoints
- `GET /reports/dashboard` - Dashboard report data
- `GET /reports/amortization` - Amortization report

### Contacts (`/contacts`) - 8 endpoints
- `GET /contacts/` - List contacts with pagination
- `GET /contacts/{contact_id}` - Get contact details
- `POST /contacts/` - Create new contact
- `PUT /contacts/{contact_id}` - Update contact
- `DELETE /contacts/{contact_id}` - Delete/deactivate contact
- `GET /contacts/stats/summary` - Contact statistics
- `GET /contacts/enums/contact-types` - Contact type enums
- `GET /contacts/enums/source-systems` - Source system enums

### Comments (`/comments`) - 6 endpoints

#### Core Comment Operations
- `POST /comments/` - Create new comment with mentions
- `GET /comments/{parent_type}/{parent_id}` - List comments for record with pagination
- `PUT /comments/{comment_id}` - Update own comment (author only)
- `DELETE /comments/{comment_id}` - Soft delete own comment (author only)
- `GET /comments/{comment_id}` - Get specific comment by ID
- `GET /comments/stats/{parent_type}/{parent_id}` - Get comment statistics for record

#### Comment Features
- **Universal Attachment**: Comments can be attached to any record type (schedules, transactions, entities, etc.)
- **@Mention System**: Mention users with notifications via Cloud Functions
- **Client-Scoped Security**: Comments respect existing firm/client access controls
- **Soft Deletion**: Comments are marked as deleted rather than permanently removed
- **Real-time Notifications**: Mentioned users receive in-app and email notifications
- **Search & Filtering**: Full-text search and advanced filtering options

#### Supported Parent Types
- `schedule` - Amortization schedules
- `transaction` - Financial transactions
- `entity` - Business entities
- `invoice` - Invoice records
- `manual_journal` - Manual journal entries
- `client` - Client records

#### Security Model
- Users can only comment on records they have access to through client permissions
- Only comment authors can edit or delete their own comments
- Mentioned users must have access to the same client
- All operations respect firm-level and client-level access controls

### Manual Journals (`/manual-journals`) - 3 endpoints
- `GET /manual-journals/{journal_id}/match-preview` - Preview journal matching
- `GET /manual-journals/{journal_id}/detection-status` - Get detection status
- `POST /manual-journals/{journal_id}/confirm-match` - Confirm journal match

### Audit Logs (`/audit`) - 8 endpoints
- `GET /audit/` - List audit logs with pagination
- `GET /audit/{audit_id}` - Get audit log details
- `GET /audit/stats/summary` - Audit statistics
- `GET /audit/metadata/event-types` - Event type metadata
- `POST /audit/export` - Export audit logs
- `POST /audit/bulk-actions` - Bulk audit actions
- `DELETE /audit/cleanup` - Clean up old audit logs
- `GET /audit/enums/categories` - Audit categories

### Token Usage (`/token-usage`) - 3 endpoints
- `GET /token-usage/sync/{sync_job_id}/tokens` - Sync job token usage
- `GET /token-usage/client/{client_id}/tokens` - Client token usage
- `GET /token-usage/summary` - Overall token usage summary

**Note**: Token usage tracking has been enhanced to properly capture LLM processing costs. All attachment processing paths now correctly include `_token_usage` data in sync job aggregation.

### Attachments (`/attachments`) - 1 endpoint
- `GET /attachments/{attachment_id}` - Get attachment content

### Invoices (3 endpoints)
- `GET /invoices/` - List invoices
- `GET /invoices/{transaction_id}` - Get invoice details
- `POST /invoices/{transaction_id}/preview-schedule` - Preview invoice schedule

## Common Request Patterns

### Authentication Headers
```typescript
headers: {
  'Authorization': `Bearer ${firebaseIdToken}`,
  'Content-Type': 'application/json'
}
```

### Pagination
Most list endpoints support pagination:
```typescript
GET /endpoint?limit=25&offset=0&client_id=uuid
```

### Filtering
Common filter parameters:
- `client_id` - Filter by client
- `entity_id` - Filter by entity  
- `status` - Filter by status
- `date_from`, `date_to` - Date range filtering

### Error Responses
All endpoints return consistent error format:
```json
{
  "detail": "Error message",
  "status_code": 400,
  "timestamp": 1642712345
}
```

## Frontend Integration Notes

### Base URL Configuration
Frontend should use `http://localhost:8081` for development:
```typescript
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081';
```

### Data Transformations
- Backend uses `snake_case` fields
- Frontend expects `camelCase` in some cases  
- Connection status mapping: `active` → `connected`
- Entity field: Use `entity_name` not `name`

### Critical Endpoints for Frontend
1. `GET /auth/me` - User authentication check
2. `GET /clients/summary` - Main dashboard data
3. `GET /transactions/dashboard` - Prepayments data
4. `POST /schedules/{id}/confirm` - Main user action
5. `GET /entities/{id}/accounts` - Chart of accounts

This comprehensive reference covers all 91 validated endpoints with their exact paths, methods, and integration patterns.