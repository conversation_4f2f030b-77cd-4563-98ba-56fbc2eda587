import { toast } from 'sonner';
import { PrepaymentsService } from './prepayments.service';
import { isManuallyEdited } from '../types/schedule.types';

// Local type definitions (these should match the main component interfaces)
export interface AmortizationConfig {
  method: string;
  startDate: string;
  endDate: string;
  prepaymentAccount: string;
  expenseAccount: string;
  numberOfPeriods: number;
  custom_narration?: string;
}

export interface MonthlyScheduleEntry {
  period: number;
  date: string;
  amount: number;
  originalAmount: number | null;
  status: 'proposed' | 'confirmed' | 'posted';
  runningBalance: number;
}

export interface OptimisticUIState {
  isLoading: boolean;
  operation: string;
}

export interface OptimisticUIHelpers {
  beginSave: () => () => void;
  beginPost: () => () => void;
  beginReset: () => () => void;
  beginExport: () => () => void;
  beginSkip: () => () => void;
}

export interface PostProcessingOptions {
  preserveSelection?: boolean;
  disableCache?: boolean;
  showSuccessToast?: boolean;
  showErrorToast?: boolean;
}

export class PostProcessingService {
  /**
   * Create optimistic UI helpers that return cleanup functions
   * This prevents code duplication for loading state management
   */
  static createOptimisticHelpers(
    setToolbarLoading: React.Dispatch<React.SetStateAction<{
      exporting: boolean;
      resetting: boolean;
      saving: boolean;
      posting: boolean;
      skipping: boolean;
    }>>
  ): OptimisticUIHelpers {
    return {
      beginSave: () => {
        setToolbarLoading(prev => ({ ...prev, saving: true }));
        return () => setToolbarLoading(prev => ({ ...prev, saving: false }));
      },
      
      beginPost: () => {
        setToolbarLoading(prev => ({ ...prev, posting: true }));
        return () => setToolbarLoading(prev => ({ ...prev, posting: false }));
      },
      
      beginReset: () => {
        setToolbarLoading(prev => ({ ...prev, resetting: true }));
        return () => setToolbarLoading(prev => ({ ...prev, resetting: false }));
      },
      
      beginExport: () => {
        setToolbarLoading(prev => ({ ...prev, exporting: true }));
        return () => setToolbarLoading(prev => ({ ...prev, exporting: false }));
      },
      
      beginSkip: () => {
        setToolbarLoading(prev => ({ ...prev, skipping: true }));
        return () => setToolbarLoading(prev => ({ ...prev, skipping: false }));
      },
    };
  }

  /**
   * Save and post ready - orchestrates the complex multi-step operation
   * with try/rollback pattern to ensure UI doesn't remain out of sync
   */
  static async saveAndPostReady(
    selectedScheduleIds: string[],
    amortizationConfig: AmortizationConfig,
    monthlySchedule: MonthlyScheduleEntry[],
    handleDataRefresh: (
      preserveSelection?: boolean, 
      disableCache?: boolean,
      getCurrentSelectionState?: () => any,
      restoreSelectionState?: (data: any, selectionState: any) => any
    ) => Promise<void>,
    getCurrentSelectionState?: () => any,
    restoreSelectionState?: (data: any, selectionState: any) => any,
    options: PostProcessingOptions = {}
  ): Promise<{ success: boolean; message: string; consolidationSummary?: any }> {
    const {
      preserveSelection = true,
      disableCache = true,
      showSuccessToast = true,
      showErrorToast = true,
    } = options;

    if (selectedScheduleIds.length === 0) {
      const message = 'No schedules selected';
      if (showErrorToast) toast.error(message);
      return { success: false, message };
    }

    console.log('PostProcessingService: Starting save and post ready operation', {
      scheduleCount: selectedScheduleIds.length,
      monthlyEntries: monthlySchedule.length,
      config: amortizationConfig
    });

    try {
      // Determine what changes need to be made
      const configChanged = true; // TODO: Detect actual config changes
      const manualEdits = monthlySchedule.filter(entry => isManuallyEdited(entry));

      console.log('PostProcessingService: Changes detected for save and post:', {
        configChanged,
        manualEditsCount: manualEdits.length
      });

      // Save complete schedule exactly as displayed to user before posting
      console.log('PostProcessingService: Saving complete schedule structure before posting');
      const saveResults = await Promise.allSettled(
        selectedScheduleIds.map(scheduleId =>
          PrepaymentsService.updateSchedule(scheduleId, {
            // Complete monthly entries as displayed to user
            monthly_entries: monthlySchedule.map(entry => ({
              amount: entry.amount,
              month_date: entry.date,
              status: entry.status === 'confirmed' ? 'proposed' : entry.status as 'proposed' | 'posted'
            })),
            // Account codes
            account_code: amortizationConfig.prepaymentAccount,
            expense_account_code: amortizationConfig.expenseAccount,
            // Configuration
            calculation_method: amortizationConfig.method as 'day_based' | 'equal_monthly',
            start_date: amortizationConfig.startDate,
            end_date: amortizationConfig.endDate,
            custom_narration: amortizationConfig.custom_narration,
          })
        )
      );

      const saveFailures = saveResults.filter(r => r.status === 'rejected');
      if (saveFailures.length > 0) {
        console.error('PostProcessingService: Save failures before posting:', saveFailures);
        if (showErrorToast) {
          toast.error(`Failed to save ${saveFailures.length} schedules before posting`);
        }
        return { success: false, message: 'Save failed before posting' };
      }

      console.log('PostProcessingService: Schedule save completed successfully, proceeding to post');

      // Step 3: Post entries to Xero using consolidated posting
      console.log('PostProcessingService: Step 3 - Posting entries to Xero with consolidation');
      
      // Use consolidated posting for multiple schedules (posts all ready entries)
      const postResult = await PrepaymentsService.postConsolidatedEntries(
        selectedScheduleIds
      );
      
      console.log('PostProcessingService: Consolidated posting completed', { 
        result: postResult,
        journalsCreated: postResult.summary.journals_created,
        entriesPosted: postResult.summary.entries_posted
      });

      // Step 4: Refresh data to sync with backend state
      console.log('PostProcessingService: Step 4 - Refreshing data');
      await handleDataRefresh(preserveSelection, disableCache, getCurrentSelectionState, restoreSelectionState);

      // Create success message with consolidation info
      const consolidatedCount = postResult.consolidation_results.filter(r => r.type === 'consolidated').length;
      let message = 'Changes saved and posted to Xero successfully';
      if (consolidatedCount > 0) {
        message = `Changes saved and ${postResult.summary.journals_created} consolidated journals posted to Xero (${postResult.summary.entries_posted} entries)`;
      }
      
      if (showSuccessToast) toast.success(message);
      
      return { success: true, message, consolidationSummary: postResult.summary };

    } catch (error: any) {
      console.error('PostProcessingService: Error in save and post operation:', error);
      
      // Try/rollback pattern: Re-sync local cache to prevent UI desync
      console.log('PostProcessingService: Error occurred, refreshing data to prevent UI desync');
      try {
        await handleDataRefresh(preserveSelection, disableCache, getCurrentSelectionState, restoreSelectionState);
      } catch (refreshError) {
        console.error('PostProcessingService: Failed to refresh data after error:', refreshError);
      }

      // Handle specific error types
      let message = 'Failed to save and post changes';
      if (error.response?.status === 422) {
        console.error('PostProcessingService: 422 Validation Error Details:', error.response.data);
        message = `Validation Error: ${error.response.data?.message || 'Invalid request data'}`;
      }

      if (showErrorToast) toast.error(message);
      return { success: false, message };
    }
  }

  /**
   * Save changes only (without posting)
   * with smart persistence and Promise.allSettled for robustness
   */
  static async saveChanges(
    selectedScheduleIds: string[],
    amortizationConfig: AmortizationConfig,
    monthlySchedule: MonthlyScheduleEntry[],
    handleDataRefresh: (
      preserveSelection?: boolean, 
      disableCache?: boolean,
      getCurrentSelectionState?: () => any,
      restoreSelectionState?: (data: any, selectionState: any) => any
    ) => Promise<void>,
    getCurrentSelectionState?: () => any,
    restoreSelectionState?: (data: any, selectionState: any) => any,
    options: PostProcessingOptions = {}
  ): Promise<{ success: boolean; message: string }> {
    const {
      preserveSelection = true,
      disableCache = true,
      showSuccessToast = true,
      showErrorToast = true,
    } = options;

    if (selectedScheduleIds.length === 0) {
      const message = 'No schedules selected';
      if (showErrorToast) toast.error(message);
      return { success: false, message };
    }

    console.log('PostProcessingService: Starting save changes operation');

    try {
      // Determine what changes need to be made
      const configChanged = true; // TODO: Detect actual config changes
      const manualEdits = monthlySchedule.filter(entry => isManuallyEdited(entry));

      console.log('PostProcessingService: Changes detected:', {
        configChanged,
        manualEditsCount: manualEdits.length
      });

      // Save complete schedule exactly as displayed to user
      console.log('PostProcessingService: Saving complete schedule structure');
      const saveResults = await Promise.allSettled(
        selectedScheduleIds.map(scheduleId =>
          PrepaymentsService.updateSchedule(scheduleId, {
            // Complete monthly entries as displayed to user
            monthly_entries: monthlySchedule.map(entry => ({
              amount: entry.amount,
              month_date: entry.date,
              status: entry.status === 'confirmed' ? 'proposed' : entry.status as 'proposed' | 'posted'
            })),
            // Account codes
            account_code: amortizationConfig.prepaymentAccount,
            expense_account_code: amortizationConfig.expenseAccount,
            // Configuration
            calculation_method: amortizationConfig.method as 'day_based' | 'equal_monthly',
            start_date: amortizationConfig.startDate,
            end_date: amortizationConfig.endDate,
            custom_narration: amortizationConfig.custom_narration,
          })
        )
      );

      const saveFailures = saveResults.filter(r => r.status === 'rejected');
      if (saveFailures.length > 0) {
        console.error('PostProcessingService: Save failures:', saveFailures);
        if (showErrorToast) {
          toast.error(`Failed to save ${saveFailures.length} schedules`);
        }
        return { success: false, message: 'Save failed' };
      }

      console.log('PostProcessingService: Schedule save completed successfully');

      // Step 3: Refresh data to sync with backend
      await handleDataRefresh(preserveSelection, disableCache, getCurrentSelectionState, restoreSelectionState);
      
      const message = 'Changes saved successfully';
      if (showSuccessToast) toast.success(message);
      
      return { success: true, message };

    } catch (error: any) {
      console.error('PostProcessingService: Error saving changes:', error);
      
      // Try/rollback: Re-sync on error
      try {
        await handleDataRefresh(preserveSelection, disableCache, getCurrentSelectionState, restoreSelectionState);
      } catch (refreshError) {
        console.error('PostProcessingService: Failed to refresh data after save error:', refreshError);
      }

      const message = 'Failed to save changes';
      if (showErrorToast) toast.error(message);
      return { success: false, message };
    }
  }

  /**
   * Reset to saved state with cache invalidation
   * and try/rollback pattern for data consistency
   */
  static async resetToSavedState(
    selectedScheduleIds: string[],
    handleConfigurationChange: (updates: Partial<AmortizationConfig>) => void,
    handleDataRefresh: (
      preserveSelection?: boolean, 
      disableCache?: boolean,
      getCurrentSelectionState?: () => any,
      restoreSelectionState?: (data: any, selectionState: any) => any
    ) => Promise<void>,
    getCurrentSelectionState?: () => any,
    restoreSelectionState?: (data: any, selectionState: any) => any,
    options: PostProcessingOptions = {}
  ): Promise<{ success: boolean; message: string }> {
    const {
      preserveSelection = true,
      disableCache = true,
      showSuccessToast = true,
      showErrorToast = true,
    } = options;

    console.log('PostProcessingService: Starting reset to saved state operation');

    try {
      // Fetch the actual saved configuration from backend
      const savedConfig = await this.fetchSavedConfiguration(selectedScheduleIds);
      
      if (savedConfig) {
        // Restore to saved configuration
        handleConfigurationChange(savedConfig);
        // Refresh data to restore original monthly values with cache invalidation
        await handleDataRefresh(preserveSelection, disableCache, getCurrentSelectionState, restoreSelectionState);
        
        const message = 'Configuration and monthly values reset to saved values';
        if (showSuccessToast) toast.success(message);
        return { success: true, message };
      } else {
        // Fall back to defaults if no saved config found
        const defaultStartDate = new Date().toISOString().split('T')[0];
        const defaultEndDate = new Date();
        defaultEndDate.setMonth(defaultEndDate.getMonth() + 12);
        
        handleConfigurationChange({
          method: 'straight_line',
          startDate: defaultStartDate,
          endDate: defaultEndDate.toISOString().split('T')[0],
          numberOfPeriods: 12,
          prepaymentAccount: '',
          expenseAccount: '',
        });
        
        // Refresh data to restore original monthly values
        await handleDataRefresh(preserveSelection, disableCache, getCurrentSelectionState, restoreSelectionState);
        
        const message = 'Reset to default configuration and original monthly values';
        if (showSuccessToast) toast.info(message);
        return { success: true, message };
      }
    } catch (error: any) {
      console.error('PostProcessingService: Error resetting configuration:', error);
      
      // Try/rollback: Attempt to refresh data even if reset fails
      try {
        await handleDataRefresh(preserveSelection, disableCache, getCurrentSelectionState, restoreSelectionState);
      } catch (refreshError) {
        console.error('PostProcessingService: Failed to refresh data after reset error:', refreshError);
      }

      const message = 'Failed to reset configuration';
      if (showErrorToast) toast.error(message);
      return { success: false, message };
    }
  }

  /**
   * Skip schedules - orchestrates the skip operation for single or bulk
   */
  static async skipSchedules(
    selectedScheduleIds: string[],
    reason: string,
    handleDataRefresh: (
      preserveSelection?: boolean, 
      disableCache?: boolean,
      getCurrentSelectionState?: () => any,
      restoreSelectionState?: (data: any, selectionState: any) => any
    ) => Promise<void>,
    getCurrentSelectionState?: () => any,
    restoreSelectionState?: (data: any, selectionState: any) => any,
    options: PostProcessingOptions = {}
  ): Promise<{ success: boolean; message: string }> {
    const {
      preserveSelection = true,
      disableCache = true,
      showSuccessToast = true,
      showErrorToast = true
    } = options;

    console.log('PostProcessingService: Starting skip operation');

    try {
      // Preserve current selection state for potential rollback
      const selectionState = getCurrentSelectionState?.();
      
      const isBulk = selectedScheduleIds.length > 1;
      
      // Skip each schedule
      if (isBulk) {
        console.log(`PostProcessingService: Skipping ${selectedScheduleIds.length} schedules in bulk`);
        
        // For bulk operations, skip all schedules
        const skipResults = await Promise.allSettled(
          selectedScheduleIds.map(scheduleId => 
            PrepaymentsService.skipSchedule(scheduleId, reason)
          )
        );

        const failures = skipResults.filter(r => r.status === 'rejected');
        if (failures.length > 0) {
          console.error('PostProcessingService: Some skip operations failed:', failures);
          const successCount = selectedScheduleIds.length - failures.length;
          if (successCount > 0 && showSuccessToast) {
            toast.success(`Successfully skipped ${successCount} of ${selectedScheduleIds.length} schedules`);
          }
          if (showErrorToast) {
            toast.error(`Failed to skip ${failures.length} schedules`);
          }
        } else if (showSuccessToast) {
          toast.success(`Successfully skipped ${selectedScheduleIds.length} schedules`);
        }
      } else {
        // Single schedule skip
        console.log(`PostProcessingService: Skipping single schedule ${selectedScheduleIds[0]}`);
        await PrepaymentsService.skipSchedule(selectedScheduleIds[0], reason);
        
        if (showSuccessToast) {
          toast.success('Schedule skipped successfully');
        }
      }

      // Refresh data after successful skip
      console.log('PostProcessingService: Refreshing data after skip operation');
      await handleDataRefresh(preserveSelection, disableCache, getCurrentSelectionState, restoreSelectionState);

      const message = isBulk 
        ? `${selectedScheduleIds.length} schedules skipped successfully`
        : 'Schedule skipped successfully';

      return { success: true, message };

    } catch (error: any) {
      console.error('PostProcessingService: Skip operation failed:', error);
      
      // Try/rollback pattern: Re-sync local cache to prevent UI desync
      console.log('PostProcessingService: Error occurred, refreshing data to prevent UI desync');
      try {
        await handleDataRefresh(preserveSelection, disableCache, getCurrentSelectionState, restoreSelectionState);
      } catch (refreshError) {
        console.error('PostProcessingService: Failed to refresh data after skip error:', refreshError);
      }

      const errorMessage = error instanceof Error ? error.message : 'Failed to skip schedule(s)';
      
      if (showErrorToast) {
        toast.error(errorMessage);
      }

      return { success: false, message: errorMessage };
    }
  }

  /**
   * Export schedule data
   * Simple operation with optimistic UI support
   */
  static async exportScheduleData(
    monthlySchedule: MonthlyScheduleEntry[],
    options: PostProcessingOptions = {}
  ): Promise<{ success: boolean; message: string }> {
    const {
      showSuccessToast = false, // Don't show success toast for export by default
      showErrorToast = true,
    } = options;

    console.log('PostProcessingService: Starting export operation');

    try {
      // Simulate export operation (replace with actual export logic)
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // TODO: Implement actual export functionality
      // This could generate CSV, Excel, or PDF based on requirements
      
      const message = 'Schedule exported successfully';
      if (showSuccessToast) toast.success(message);
      
      return { success: true, message };

    } catch (error: any) {
      console.error('PostProcessingService: Error exporting schedule:', error);
      
      const message = 'Failed to export schedule';
      if (showErrorToast) toast.error(message);
      return { success: false, message };
    }
  }

  /**
   * Private helper to fetch saved configuration from backend
   * Used by resetToSavedState operation
   */
  private static async fetchSavedConfiguration(
    selectedScheduleIds: string[]
  ): Promise<AmortizationConfig | null> {
    if (selectedScheduleIds.length === 0) return null;

    try {
      // Get configuration from the first selected schedule
      const scheduleId = selectedScheduleIds[0];
      const scheduleDetails = await PrepaymentsService.getSchedule(scheduleId);
      
      // Transform backend response to AmortizationConfig format
      return {
        method: 'straight_line', // Backend may not store this, using default
        startDate: scheduleDetails.start_date || new Date().toISOString().split('T')[0],
        endDate: scheduleDetails.end_date || new Date().toISOString().split('T')[0],
        numberOfPeriods: scheduleDetails.number_of_periods || 12,
        prepaymentAccount: scheduleDetails.account_code || '',
        expenseAccount: scheduleDetails.expense_account_code || '',
      };
    } catch (error) {
      console.error('PostProcessingService: Error fetching saved configuration:', error);
      return null;
    }
  }
}