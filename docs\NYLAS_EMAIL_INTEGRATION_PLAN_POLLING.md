# Nylas Email Integration Implementation Plan (POLLING-BASED)

**Comprehensive implementation plan for email ingestion, processing, and integration with DRCR prepayment workflows**
*Last Updated: 2025-07-19 | Status: PRODUCTION (Phase 1 Complete) | Architecture: POLLING*

## Overview

### Business Value
Transform DRCR from **reactive** (post-Xero processing) to **proactive** (email-first processing) by:
- **Intercepting vendor invoices** at `<EMAIL>` before Xero entry
- **AI-powered prepayment detection** on incoming documents
- **Automated amortization schedule generation** from email attachments
- **Streamlined vendor communication** with thread management

### PRODUCTION Architecture Pattern (POLLING-BASED)
Following DRCR's **two-stage processing model** with **reliable polling instead of broken webhooks**:
```
Cloud Scheduler (every 10 min) → Cloud Function (Nylas API polling) → Firestore
                                                                         ↓
                                      Cloud Run Job (batch heavy processing)
                                      Triggered by:
                                      • Schedule: Every 30 minutes
                                      • Threshold: 5+ pending emails  
                                      • Manual: User-triggered sync
```

### Why Polling Instead of Webhooks
- ✅ **Reliable**: No dependency on <PERSON><PERSON><PERSON> webhook delivery (which is broken)
- ✅ **Controllable**: We control timing, retries, and error handling
- ✅ **Testable**: Immediate feedback and easy debugging
- ✅ **Proven**: Follows same pattern as successful Xero integration
- ✅ **Simple**: No webhook verification, signatures, or delivery failures

### Technical Foundation
- **Single Gmail inbox**: `<EMAIL>` connected to Nylas
- **Plus addressing**: `<EMAIL>` routes to entity contexts
- **Subcollection storage**: `ENTITIES/{entity_id}/EMAILS/{email_id}`
- **GCS attachment storage**: `/entities/{entity_id}/emails/{email_id}/`
- **Polling frequency**: Every 10 minutes (144 API calls/day well within limits)

## Phase 1: Polling Infrastructure ✅ COMPLETED & IN PRODUCTION

### Objectives
- [x] ~~Cloud Function webhook handler~~ → **Polling-based Cloud Function**
- [x] Email metadata storage in Firestore subcollections
- [x] Entity ID extraction and routing
- [x] Working polling solution that processes emails
- [x] Cloud Scheduler for automated polling (10-minute intervals)
- [x] Duplicate detection and error handling
- [x] **Code review improvements applied** (timestamp fixes, batching, retries)
- [x] **Performance optimizations** (93% reduction in Firestore RPCs)
- [x] **Production deployment** with monitoring and error handling

### 1.1 Firestore Schema Design (UNCHANGED)

#### ENTITIES/{entity_id}/EMAILS Subcollection
```json
{
  "email_id": "nylas_message_id",
  "thread_id": "nylas_thread_id", 
  "entity_id": "entity_456",
  "client_id": "client_123",
  "firm_id": "firm_789",
  "subject": "Invoice #12345 - Software License",
  "from_address": ["<EMAIL>"],
  "to_addresses": ["<EMAIL>"],
  "sender_name": "Vendor Company",
  "received_at": "2025-01-15T10:30:00Z",
  "created_at": "2025-01-15T10:31:15Z",
  "has_attachments": true,
  "attachment_count": 2,
  "processing_status": "pending",
  "metadata_sync_status": "completed",
  "error_message": null,
  "created_by": "polling_system"
}
```

### 1.2 Polling Implementation ✅ COMPLETED & OPTIMIZED

#### Core Polling Logic (Production Version)
```python
def process_messages(self, messages: List[Dict]) -> Dict:
    """
    Process messages with optimized Firestore batching
    
    Performance improvements:
    - Batch entity checks (150 → 10 RPCs)
    - Retry logic for network resilience  
    - Compiled regex for performance
    - UTC timestamp handling
    """
    
    # 1. Extract entity emails and IDs for batch processing
    entity_messages = []
    entity_ids = set()
    
    for message in messages:
        entity_id = self._extract_entity_id_from_message(message)
        if entity_id:
            entity_messages.append((message, entity_id))
            entity_ids.add(entity_id)
    
    # 2. Batch check which entities exist (OPTIMIZED)
    existing_entities = self._batch_check_entities_exist(list(entity_ids))
    
    # 3. Process messages for existing entities only
    for message, entity_id in entity_messages:
        if entity_id in existing_entities:
            if not self._email_already_processed(entity_id, message.get('id')):
                self._store_email_metadata(entity_id, message)
                processed_count += 1
```

#### Entity ID Extraction (OPTIMIZED)
```python
# Compiled at module level for performance
ENTITY_EXTRACTION_PATTERN = re.compile(r'inbox\+([a-zA-Z0-9_-]+)@drcrlabs\.com', re.IGNORECASE)

def extract_entity_id_from_email(email_address: str) -> str:
    """Extract entity <NAME_EMAIL>"""
    match = ENTITY_EXTRACTION_PATTERN.match(email_address.strip())
    return match.group(1) if match else None
```

### 1.3 Cloud Function Structure (PRODUCTION)

#### Directory: `drcr_back/cloud_functions/nylas_email_poller/`
```
nylas_email_poller/                    # Production-ready structure
├── main.py                           # Cloud Function entry point (✅ optimized)
├── requirements.txt                  # Dependencies with tenacity
├── deploy.ps1                       # Production deployment script
├── test.ps1                         # Simple testing capability  
├── README.md                        # Updated documentation
├── .env.example                     # Environment template
├── services/
│   ├── __init__.py
│   ├── nylas_service.py            # ✅ With retry logic & attachment fields
│   └── email_processor.py          # ✅ Optimized Firestore batching
└── utils/
    ├── __init__.py
    └── error_handler.py            # ✅ Improved error handling
```

#### main.py (Cloud Function Entry Point)
```python
import functions_framework
from services.email_processor import EmailProcessor

@functions_framework.http
def nylas_email_poller(request):
    """
    Cloud Function triggered by Cloud Scheduler to poll Nylas for new emails
    
    Processes emails and stores metadata in Firestore subcollections
    """
    try:
        processor = EmailProcessor()
        result = processor.poll_and_process_emails()
        
        return {
            "status": "success",
            "processed_emails": result["processed_count"],
            "timestamp": result["timestamp"]
        }
    except Exception as e:
        logger.error(f"Polling failed: {str(e)}")
        return {"status": "error", "error": str(e)}, 500
```

### 1.4 Cloud Scheduler Configuration

#### Scheduled Polling Job (✅ ACTIVE IN PRODUCTION)
```bash
gcloud scheduler jobs create http nylas-email-polling \
  --schedule="*/10 * * * *" \
  --uri="https://europe-west2-drcr-d660a.cloudfunctions.net/nylas_email_poller" \
  --http-method=POST \
  --headers="Content-Type=application/json" \
  --message-body='{"source":"scheduler"}' \
  --time-zone="UTC" \
  --location="europe-west2" \
  --project="drcr-d660a" \
  --description="Poll Nylas for new emails every 10 minutes"
```

**Status**: ✅ LIVE - Scheduler job running automatically every 10 minutes

#### Monitoring and Alerting
- **Success rate monitoring**: >95% successful polls
- **Latency monitoring**: <30 seconds per poll
- **Error alerting**: Failed polls or API rate limits
- **Daily summary**: Emails processed, entities affected

## Phase 2: Document Processing Pipeline (UNCHANGED)

### Objectives
- [ ] GCS attachment storage from Nylas API
- [ ] AI document processing integration
- [ ] Amortization schedule generation
- [ ] Prepayment detection and classification

### 2.1 Attachment Processing

#### Cloud Run Job Structure (Batch Processing)
```
Email Processing Job (triggered by email volume or schedule)
├── Fetch pending emails from Firestore
├── Download attachments from Nylas → GCS
├── AI document processing (existing pipeline)
├── Generate amortization schedules
├── Update email status to "processed"
└── Trigger notifications/integrations
```

#### GCS Storage Pattern
```
gs://drcr-email-attachments/
├── entities/
│   └── {entity_id}/
│       └── emails/
│           └── {email_id}/
│               ├── attachment_1.pdf
│               ├── attachment_2.png
│               └── metadata.json
```

### 2.2 AI Integration (Reuse Existing)

#### Document Processing Flow
1. **Extract attachments** from Nylas API
2. **Store in GCS** with entity/email organization
3. **Process with existing AI pipeline** (OpenAI/Mistral)
4. **Generate amortization schedules** using existing logic
5. **Update Firestore** with processing results

## Phase 3: Advanced Features & Integration

### 3.1 API Endpoints (Integration with REST API)
- `GET /emails/{entity_id}` - List entity emails
- `GET /emails/{entity_id}/{email_id}` - Get email details
- `POST /emails/{email_id}/process` - Manual processing trigger
- `GET /emails/stats` - Processing statistics

### 3.2 Frontend Integration
- **Email inbox view** in entity dashboard
- **Processing status indicators**
- **Manual trigger buttons**
- **Email thread management**

### 3.3 Vendor Communication
- **Reply handling** through Nylas API
- **Thread management** for vendor follow-ups
- **Status notifications** to vendors

## Implementation Roadmap

### Week 1: ✅ COMPLETED
- [x] Polling solution proof of concept
- [x] Email metadata storage
- [x] Entity ID extraction
- [x] Firestore integration

### Week 2: ✅ COMPLETED + CODE REVIEW IMPROVEMENTS
- [x] Cloud Function deployment
- [x] Cloud Scheduler setup (10-minute polling)
- [x] Error handling and monitoring
- [x] Duplicate detection
- [x] **Performance optimizations** (93% Firestore RPC reduction)
- [x] **Timestamp bug fixes** (milliseconds → seconds conversion)
- [x] **Retry logic** for network resilience
- [x] **Production deployment** with all improvements

### Week 3: 📋 READY TO START
- [ ] Attachment processing integration
- [ ] AI pipeline connection
- [ ] Batch job implementation

### Week 4: 📋 PLANNED
- [ ] API endpoints
- [ ] Frontend integration
- [ ] Testing and optimization

## Cost Analysis (POLLING VS WEBHOOKS)

### Polling Approach Costs (PRODUCTION ACTUAL)
- **Cloud Function invocations**: 4,320/month (every 10 min) × $0.0000004 = $0.0017
- **Nylas API calls**: 4,320/month (within free tier)
- **Firestore operations**: ~$3-5/month (93% reduction with batching)
- **Total**: ~$3-5/month

### Benefits vs Webhook Approach
- ✅ **Reliability**: 100% vs ~0% (webhooks were broken)
- ✅ **Debuggability**: Full control vs black box
- ✅ **Scalability**: Linear scaling vs webhook delivery limits
- ✅ **Maintenance**: Simple monitoring vs complex webhook management

## Key Implementation Files

### Essential Files (PRODUCTION)
```
drcr_back/cloud_functions/nylas_email_poller/
├── main.py                    # ✅ Production Cloud Function entry point
├── requirements.txt           # ✅ Dependencies with tenacity retry logic
├── deploy.ps1                # ✅ Production deployment script
├── test.ps1                  # ✅ Simple testing capability
├── README.md                 # ✅ Updated documentation
├── .env.example              # ✅ Environment template
├── services/
│   ├── nylas_service.py      # ✅ With retry logic & attachment fields
│   └── email_processor.py    # ✅ Optimized Firestore batching
└── utils/
    └── error_handler.py      # ✅ Improved error handling
```

### Deleted Files (CLEANUP COMPLETED)
```
drcr_back/cloud_functions/nylas_webhook_handler/       # 🗑️ DELETED (entire directory)
├── All webhook-related files                          # 🗑️ DELETED
├── Webhook debugging scripts                          # 🗑️ DELETED  
└── Webhook service files                              # 🗑️ DELETED

Development artifacts removed from nylas_email_poller/: # 🗑️ DELETED
├── test_function.ps1 & test_function.sh              # 🗑️ DELETED
├── setup_scheduler.ps1 & setup_scheduler.sh          # 🗑️ DELETED
├── test_commands.txt & scheduler_command.txt          # 🗑️ DELETED
└── test_improved_function.ps1                        # 🗑️ RENAMED to test.ps1
```

## Monitoring and Maintenance

### Key Metrics
- **Emails processed per hour**
- **Processing latency** (poll → storage)
- **Error rates** and types
- **API quota usage**
- **Storage costs**

### Alerts
- **Failed polling attempts** (>2 consecutive failures)
- **High processing latency** (>60 seconds)
- **API rate limit warnings**
- **Storage quota alerts**

### Regular Maintenance
- **Weekly**: Review processing stats and error logs
- **Monthly**: Optimize polling frequency based on email volume
- **Quarterly**: Review costs and scaling needs

---

## Summary: Why This Approach Works

1. **✅ Reliable**: Polling eliminates webhook delivery dependencies
2. **✅ Testable**: Direct API control for immediate feedback
3. **✅ Scalable**: Linear cost scaling with email volume
4. **✅ Maintainable**: Simple architecture with clear error handling
5. **✅ Proven**: Follows successful DRCR patterns

**Current Status**: ✅ Phase 1 PRODUCTION COMPLETE - Polling system live with code review improvements

**Performance**: 16 messages processed in 1.9 seconds, 93% reduction in Firestore RPCs
**Reliability**: 100% polling success vs 0% webhook delivery success
**Cost**: $3-5/month with optimized operations

**Next Phase**: Ready to begin Phase 2 - Document Processing Pipeline (GCS attachment storage and AI integration)

---
**Last Updated**: 2025-07-19 | **Status**: PRODUCTION (Phase 1 Complete) | **Architecture**: Polling-Based | **Webhook Status**: ABANDONED