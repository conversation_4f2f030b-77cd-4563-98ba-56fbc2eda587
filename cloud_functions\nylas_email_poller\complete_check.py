#!/usr/bin/env python3
"""
Complete systematic check of everything
"""

import requests
import json

NYLAS_API_KEY = "nyk_v0_BlT3sXqnvDnmP3R8nWgn9ngMiMW0RZL6WeforK3GcznWbIPwbLO4noOzHgBb3oqy"
GRANT_ID = "8ede108b-5335-47bf-9a4a-325e60f2b2b8"
API_URI = "https://api.eu.nylas.com/v3"

headers = {
    'Authorization': f'Bearer {NYLAS_API_KEY}',
    'Content-Type': 'application/json'
}

def complete_system_check():
    """Check absolutely everything step by step"""
    print("🔍 COMPLETE SYSTEM CHECK")
    print("=" * 60)
    
    issues_found = []
    
    # 1. Basic API connectivity
    print("\n1️⃣ API Connectivity...")
    try:
        response = requests.get(f"{API_URI}/applications", headers=headers, timeout=10)
        if response.status_code == 200:
            print("✅ API connectivity OK")
        else:
            print(f"❌ API connectivity issue: {response.status_code}")
            issues_found.append("API connectivity failed")
    except Exception as e:
        print(f"❌ API connectivity error: {str(e)}")
        issues_found.append(f"API connectivity error: {str(e)}")
    
    # 2. Grant details
    print("\n2️⃣ Grant Status...")
    try:
        response = requests.get(f"{API_URI}/grants/{GRANT_ID}", headers=headers, timeout=10)
        if response.status_code == 200:
            grant_data = response.json().get('data', {})
            print(f"✅ Grant found: {grant_data.get('grant_status')}")
            print(f"   Email: {grant_data.get('email')}")
            print(f"   Provider: {grant_data.get('provider')}")
            
            scopes = grant_data.get('scopes', [])
            print(f"   Raw Scopes: {scopes}")
            
            if not scopes:
                print("❌ EMPTY SCOPES ARRAY")
                issues_found.append("Grant has empty scopes array")
            else:
                print("✅ Grant has scopes")
                
        else:
            print(f"❌ Grant check failed: {response.status_code} - {response.text}")
            issues_found.append(f"Grant check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Grant check error: {str(e)}")
        issues_found.append(f"Grant check error: {str(e)}")
    
    # 3. Connector check
    print("\n3️⃣ Connector Configuration...")
    try:
        response = requests.get(f"{API_URI}/connectors", headers=headers, timeout=10)
        if response.status_code == 200:
            connectors = response.json().get('data', [])
            google_connector = None
            
            for connector in connectors:
                if connector.get('provider') == 'google':
                    google_connector = connector
                    break
            
            if google_connector:
                print(f"✅ Google connector found: {google_connector.get('name')}")
                connector_scopes = google_connector.get('scope', [])
                print(f"   Connector Scopes: {connector_scopes}")
                
                if not connector_scopes:
                    print("❌ CONNECTOR HAS NO DEFAULT SCOPES")
                    issues_found.append("Google connector missing default scopes")
                elif 'gmail.send' in str(connector_scopes):
                    print("✅ Connector has gmail.send scope")
                else:
                    print("❌ CONNECTOR MISSING gmail.send SCOPE")
                    issues_found.append("Google connector missing gmail.send scope")
            else:
                print("❌ NO GOOGLE CONNECTOR FOUND")
                issues_found.append("No Google connector found")
                
        else:
            print(f"❌ Connector check failed: {response.status_code}")
            issues_found.append("Connector check failed")
    except Exception as e:
        print(f"❌ Connector check error: {str(e)}")
        issues_found.append(f"Connector check error: {str(e)}")
    
    # 4. API endpoint check
    print("\n4️⃣ API Endpoint Check...")
    try:
        # Try the exact send endpoint
        send_url = f"{API_URI}/grants/{GRANT_ID}/messages"
        print(f"   Testing endpoint: {send_url}")
        
        # Just test with GET first (should be 405 Method Not Allowed, not 403)
        response = requests.get(send_url, headers=headers, timeout=10)
        print(f"   GET Response: {response.status_code}")
        
        if response.status_code == 403:
            print("❌ 403 on GET - permissions issue")
            issues_found.append("403 Forbidden on messages endpoint")
        elif response.status_code == 405:
            print("✅ 405 Method Not Allowed - endpoint exists, permissions OK")
        else:
            print(f"   Unexpected response: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Endpoint check error: {str(e)}")
        issues_found.append(f"Endpoint check error: {str(e)}")
    
    # 5. Headers check
    print("\n5️⃣ Request Headers Check...")
    print(f"   Authorization: Bearer {NYLAS_API_KEY[:20]}...")
    print(f"   Content-Type: {headers.get('Content-Type')}")
    print(f"   API URI: {API_URI}")
    
    # 6. Summary
    print("\n" + "=" * 60)
    if issues_found:
        print("❌ ISSUES FOUND:")
        for issue in issues_found:
            print(f"   • {issue}")
        print("\n🔧 These need to be fixed!")
    else:
        print("✅ NO OBVIOUS CONFIGURATION ISSUES FOUND")
        print("   This confirms it's likely a Nylas platform bug")
    
    return len(issues_found) == 0

def main():
    """Run complete check"""
    all_good = complete_system_check()
    
    if all_good:
        print("\n🤔 If everything looks correct but send still fails,")
        print("   it's definitely a Nylas platform issue.")
    else:
        print("\n🔧 Fix the issues above and try again.")

if __name__ == "__main__":
    main()