import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/auth.store';
import { useFirmName } from '../hooks/useFirmName';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '../components/ui/breadcrumb';
import { Separator } from '../components/ui/separator';
import {
  SidebarTrigger,
} from '../components/ui/sidebar';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../components/ui/tooltip';
import { FirmClientsOverviewDashboard } from '../features/dashboard/components/FirmClientsOverviewDashboard';

export function DashboardPage() {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const { firmName, isLoading: firmNameLoading } = useFirmName();

  // Don't render dashboard if user is not loaded or doesn't have firm info
  if (!user || !user.firmId) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <>
      <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <SidebarTrigger className="-ml-1" />
            </TooltipTrigger>
            <TooltipContent side="bottom" className="flex items-center gap-2">
              <span>Toggle sidebar</span>
              <div className="flex items-center gap-1 text-xs opacity-60">
                <span>{typeof navigator !== 'undefined' && navigator.platform.toUpperCase().indexOf('MAC') >= 0 ? 'Cmd' : 'Ctrl'}+B</span>
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbPage>
                {firmNameLoading ? 'Loading...' : firmName}
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </header>
      <div className="flex-1 overflow-auto">
        <FirmClientsOverviewDashboard
          firmId={user.firmId}
          firmName={firmName}
          isAdmin={user.role === 'firm_admin'}
        />
      </div>
    </>
  );
}
