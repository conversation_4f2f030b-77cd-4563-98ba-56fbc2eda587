export interface DashboardFilterOption {
  value: string;
  label: string;
  count?: number;
}

export const CLIENT_STATUS_OPTIONS: DashboardFilterOption[] = [
  {
    value: 'action_needed',
    label: 'Action Needed',
  },
  {
    value: 'error',
    label: 'Error',
  },
];

export const CONNECTION_STATUS_OPTIONS: DashboardFilterOption[] = [
  {
    value: 'connected',
    label: 'Connected',
  },
  {
    value: 'disconnected',
    label: 'Disconnected',
  },
  {
    value: 'error',
    label: 'Connection Error',
  },
];

export const ENTITY_TYPE_OPTIONS: DashboardFilterOption[] = [
  {
    value: 'xero',
    label: 'Xero',
  },
  {
    value: 'qbo',
    label: 'QuickBooks',
  },
  {
    value: 'manual',
    label: 'Manual',
  },
];

export const DEFAULT_DASHBOARD_FILTER_OPTIONS: DashboardFilterOption[] = [
  ...CLIENT_STATUS_OPTIONS,
  ...CONNECTION_STATUS_OPTIONS,
  ...ENTITY_TYPE_OPTIONS,
];

export const ACTIONABLE_CLIENT_STATUSES = ['action_needed', 'error'];
export const STANDARD_CLIENT_STATUSES = ['action_needed', 'error'];
export const CONNECTION_STATUSES = ['connected', 'disconnected', 'error'];
export const ENTITY_TYPES = ['xero', 'qbo', 'manual'];