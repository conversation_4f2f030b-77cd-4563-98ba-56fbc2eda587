/**
 * Shared design tokens for consistent styling across components
 */

// Manual edit highlighting tokens
export const manualEditTokens = {
  light: {
    backgroundColor: 'rgb(239 246 255)', // bg-blue-50
    borderColor: 'rgb(147 197 253)',     // border-blue-300
    fontWeight: '600',                   // font-semibold
  },
  dark: {
    backgroundColor: 'rgb(23 37 84)',    // bg-blue-950
    borderColor: 'rgb(37 99 235)',       // border-blue-600
    fontWeight: '600',                   // font-semibold
  }
};

// CSS-in-JS helper for manual edit styling
export const getManualEditStyles = (isDark: boolean = false) => {
  const tokens = isDark ? manualEditTokens.dark : manualEditTokens.light;
  
  return {
    backgroundColor: tokens.backgroundColor,
    borderColor: tokens.borderColor,
    fontWeight: tokens.fontWeight,
  };
};

// Tailwind CSS class names for manual edit styling
export const manualEditClasses = {
  light: 'font-semibold border-blue-300 bg-blue-50',
  dark: 'font-semibold border-blue-600 bg-blue-950',
  combined: 'font-semibold border-blue-300 bg-blue-50 dark:border-blue-600 dark:bg-blue-950'
};

// SCSS mixin for manual edit styling (for projects using SCSS)
export const manualEditScssMixin = `
@mixin manually-edited {
  font-weight: 600;
  border-color: rgb(147 197 253);
  background-color: rgb(239 246 255);
  
  @media (prefers-color-scheme: dark) {
    border-color: rgb(37 99 235);
    background-color: rgb(23 37 84);
  }
}

.manually-edited {
  @include manually-edited;
}
`;

// Loading state tokens
export const loadingTokens = {
  overlay: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    backdropFilter: 'blur(2px)',
  },
  spinner: {
    color: 'rgb(37 99 235)', // text-blue-600
    size: '1.25rem',         // h-5 w-5
  }
};

// Focus management tokens
export const focusTokens = {
  returnDelay: 100, // ms delay before returning focus after loading
  ring: 'rgb(59 130 246)', // blue-500 for focus rings
};