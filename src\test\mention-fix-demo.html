<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mention Fix Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ccc;
            padding: 20px;
            border-radius: 8px;
        }
        textarea {
            width: 100%;
            height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .mention-dropdown {
            position: absolute;
            background: white;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }
        .mention-item {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
        }
        .mention-item:hover {
            background-color: #f5f5f5;
        }
        .mention-item.selected {
            background-color: #e3f2fd;
        }
        .log {
            margin-top: 20px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>@Mention Bug Fix Demo</h1>
        <p>Test the mention system:</p>
        <ol>
            <li>Type "@Art" in the textarea</li>
            <li>Click on "Art U" from the dropdown</li>
            <li>Verify that the dropdown closes and doesn't reopen</li>
        </ol>
        
        <div style="position: relative;">
            <textarea id="commentText" placeholder="Type @Art to test mentions..."></textarea>
            <div id="mentionDropdown" class="mention-dropdown">
                <div class="mention-item" data-user="Art U">Art U (<EMAIL>)</div>
                <div class="mention-item" data-user="Arthur User">Arthur User (<EMAIL>)</div>
            </div>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
        <div id="log" class="log"></div>
    </div>

    <script>
        // Simulate the React component logic
        let skipNextDetection = false;
        let skipTimeout = null;
        let mentionStartPos = 0;
        let mentionSearch = '';
        let searchCallCount = 0;
        
        const textarea = document.getElementById('commentText');
        const dropdown = document.getElementById('mentionDropdown');
        const log = document.getElementById('log');
        const status = document.getElementById('status');
        
        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }
        
        function showStatus(message, type) {
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
        }
        
        function handleTextareaChange() {
            addLog('handleTextareaChange called');
            
            // Skip mention detection if we just selected a mention
            if (skipNextDetection) {
                addLog('Skipping mention detection (flag is set)');
                return;
            }
            
            const value = textarea.value;
            const cursorPos = textarea.selectionStart;
            
            // Find if cursor is in an active mention
            const textBeforeCursor = value.slice(0, cursorPos);
            const lastAtIndex = textBeforeCursor.lastIndexOf('@');
            
            if (lastAtIndex !== -1) {
                const textAfterAt = textBeforeCursor.slice(lastAtIndex + 1);
                
                // A mention candidate is valid only if it contains NO whitespace or punctuation
                const invalidCharPattern = /[\s\.,;:!\?\(\)\[\]\{\}]/;
                if (
                    textAfterAt.length > 0 &&
                    textAfterAt.length <= 30 &&
                    !invalidCharPattern.test(textAfterAt)
                ) {
                    // Active mention detected
                    mentionStartPos = lastAtIndex;
                    mentionSearch = textAfterAt;
                    
                    addLog(`Mention detected: "${textAfterAt}"`);
                    searchCallCount++;
                    
                    if (textAfterAt.length >= 1) {
                        showMentionDropdown();
                    } else {
                        hideMentionDropdown();
                    }
                } else {
                    hideMentionDropdown();
                }
            } else {
                hideMentionDropdown();
            }
        }
        
        function showMentionDropdown() {
            addLog('Showing mention dropdown');
            dropdown.style.display = 'block';
            
            // Position dropdown near cursor (simplified)
            const rect = textarea.getBoundingClientRect();
            dropdown.style.top = (rect.bottom + 5) + 'px';
            dropdown.style.left = rect.left + 'px';
        }
        
        function hideMentionDropdown() {
            addLog('Hiding mention dropdown');
            dropdown.style.display = 'none';
        }
        
        function handleMentionSelect(userName) {
            addLog(`handleMentionSelect called with: "${userName}"`);
            
            const currentText = textarea.value;
            
            // Calculate the exact end position of the current @mention text
            const mentionEndPos = mentionStartPos + 1 + mentionSearch.length;
            
            // Replace the @search text with @username
            const beforeMention = currentText.slice(0, mentionStartPos);
            const afterMention = currentText.slice(mentionEndPos);
            const mentionText = `@${userName} `;
            const newText = beforeMention + mentionText + afterMention;
            
            // Close dropdown
            hideMentionDropdown();
            
            // Set flag to skip next mention detection
            skipNextDetection = true;
            addLog('Set skipNextDetection = true');
            
            // Clear any existing timeout
            if (skipTimeout) {
                clearTimeout(skipTimeout);
            }
            
            // Update the text (this will trigger handleTextareaChange)
            textarea.value = newText;
            
            // Set cursor position after the mention and reset skip flag after positioning
            setTimeout(() => {
                const newCursorPos = mentionStartPos + mentionText.length;
                textarea.setSelectionRange(newCursorPos, newCursorPos);
                textarea.focus();
                
                addLog('Cursor positioned, setting timeout to reset flag');
                
                // Reset the skip flag after cursor positioning is complete
                skipTimeout = setTimeout(() => {
                    skipNextDetection = false;
                    skipTimeout = null;
                    addLog('Reset skipNextDetection = false');
                    
                    // Check if fix worked
                    if (searchCallCount === 1) {
                        showStatus('✅ SUCCESS: Mention selection did not trigger re-detection!', 'success');
                    } else {
                        showStatus(`❌ FAILED: Search was called ${searchCallCount} times (should be 1)`, 'error');
                    }
                }, 50);
            }, 0);
        }
        
        // Event listeners
        textarea.addEventListener('input', handleTextareaChange);
        
        // Handle mention item clicks
        dropdown.addEventListener('click', (e) => {
            if (e.target.classList.contains('mention-item')) {
                const userName = e.target.getAttribute('data-user');
                handleMentionSelect(userName);
            }
        });
        
        // Reset test
        function resetTest() {
            textarea.value = '';
            skipNextDetection = false;
            if (skipTimeout) {
                clearTimeout(skipTimeout);
                skipTimeout = null;
            }
            searchCallCount = 0;
            hideMentionDropdown();
            log.innerHTML = '';
            status.style.display = 'none';
            addLog('Test reset');
        }
        
        // Add reset button
        const resetBtn = document.createElement('button');
        resetBtn.textContent = 'Reset Test';
        resetBtn.onclick = resetTest;
        resetBtn.style.marginTop = '10px';
        resetBtn.style.padding = '8px 16px';
        resetBtn.style.backgroundColor = '#007bff';
        resetBtn.style.color = 'white';
        resetBtn.style.border = 'none';
        resetBtn.style.borderRadius = '4px';
        resetBtn.style.cursor = 'pointer';
        document.querySelector('.container').appendChild(resetBtn);
        
        addLog('Demo initialized - Type @Art to test');
    </script>
</body>
</html>
