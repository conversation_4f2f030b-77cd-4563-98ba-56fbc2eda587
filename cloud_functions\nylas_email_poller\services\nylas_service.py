"""
Nylas API Service

Handles all interactions with Nylas API for email polling
"""

import os
import requests
import logging
import uuid
from typing import List, Dict, Optional
from datetime import datetime
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

logger = logging.getLogger(__name__)

class NylasService:
    """Service for interacting with Nylas API"""
    
    def __init__(self):
        self.api_key = os.getenv("NYLAS_API_KEY")
        self.grant_id = os.getenv("NYLAS_GRANT_ID") 
        self.api_uri = os.getenv("NYLAS_API_URI", "https://api.us.nylas.com/v3")
        
        if not self.api_key or not self.grant_id:
            raise ValueError("NYLAS_API_KEY and NYLAS_GRANT_ID must be set")
        
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((requests.exceptions.ConnectionError, requests.exceptions.Timeout)),
        reraise=True
    )
    def fetch_recent_threads(self, limit: int = 50) -> List[Dict]:
        """
        Fetch recent email threads from Nylas API with automatic retries
        
        Args:
            limit: Maximum number of threads to fetch
            
        Returns:
            List of thread dictionaries
        """
        try:
            threads_url = f'{self.api_uri}/grants/{self.grant_id}/threads'
            params = {
                'limit': limit,
                'fields': 'id,participants,subject,latest_message_received_date,message_count'
            }
            
            logger.info(f"Fetching threads from Nylas: {threads_url}")
            response = requests.get(threads_url, headers=self.headers, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                threads = data.get('data', [])
                logger.info(f"Successfully fetched {len(threads)} threads")
                return threads
            else:
                logger.error(f"Nylas API error: {response.status_code} - {response.text}")
                return []
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Request error fetching threads: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error fetching threads: {str(e)}")
            return []

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((requests.exceptions.ConnectionError, requests.exceptions.Timeout)),
        reraise=True
    )
    def fetch_recent_messages(self, limit: int = 50) -> List[Dict]:
        """
        Fetch recent messages from Nylas API with automatic retries
        
        Args:
            limit: Maximum number of messages to fetch
            
        Returns:
            List of message dictionaries
        """
        try:
            messages_url = f'{self.api_uri}/grants/{self.grant_id}/messages'
            params = {
                'limit': limit,
                'fields': 'id,subject,from,to,cc,bcc,date,thread_id,has_attachments,attachments'
            }
            
            logger.info(f"Fetching messages from Nylas: {messages_url}")
            response = requests.get(messages_url, headers=self.headers, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                messages = data.get('data', [])
                logger.info(f"Successfully fetched {len(messages)} messages")
                return messages
            else:
                logger.error(f"Nylas API error: {response.status_code} - {response.text}")
                return []
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Request error fetching messages: {str(e)}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error fetching messages: {str(e)}")
            return []
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((requests.exceptions.ConnectionError, requests.exceptions.Timeout)),
        reraise=True
    )
    def get_thread_messages(self, thread_id: str) -> List[Dict]:
        """
        Get all messages for a specific thread
        
        Args:
            thread_id: Nylas thread ID
            
        Returns:
            List of message dictionaries in the thread
        """
        try:
            # Get messages filtered by thread_id
            messages_url = f'{self.api_uri}/grants/{self.grant_id}/messages'
            params = {
                'thread_id': thread_id,
                'fields': 'id,subject,from,to,cc,bcc,date,thread_id,has_attachments,attachments'
            }
            
            logger.debug(f"Fetching messages for thread {thread_id}")
            response = requests.get(messages_url, headers=self.headers, params=params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                messages = data.get('data', [])
                logger.debug(f"Found {len(messages)} messages in thread {thread_id}")
                return messages
            else:
                logger.error(f"Error fetching thread messages: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            logger.error(f"Error fetching messages for thread {thread_id}: {str(e)}")
            return []
    
    def get_message_details(self, message_id: str) -> Optional[Dict]:
        """
        Get detailed information for a specific message
        
        Args:
            message_id: Nylas message ID
            
        Returns:
            Message details dictionary or None if error
        """
        try:
            message_url = f'{self.api_uri}/grants/{self.grant_id}/messages/{message_id}'
            
            response = requests.get(message_url, headers=self.headers, timeout=30)
            
            if response.status_code == 200:
                return response.json().get('data')
            else:
                logger.error(f"Error fetching message {message_id}: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error fetching message details for {message_id}: {str(e)}")
            return None
    
    def download_attachment(self, message_id: str, attachment_id: str) -> Optional[bytes]:
        """
        Download an attachment from a message
        
        Args:
            message_id: Nylas message ID
            attachment_id: Nylas attachment ID
            
        Returns:
            Attachment content as bytes or None if error
        """
        try:
            attachment_url = f'{self.api_uri}/grants/{self.grant_id}/messages/{message_id}/attachments/{attachment_id}/download'
            
            response = requests.get(attachment_url, headers=self.headers, timeout=60)
            
            if response.status_code == 200:
                logger.info(f"Downloaded attachment {attachment_id} ({len(response.content)} bytes)")
                return response.content
            else:
                logger.error(f"Error downloading attachment {attachment_id}: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error downloading attachment {attachment_id}: {str(e)}")
            return None
    
    def check_api_health(self) -> bool:
        """
        Check if Nylas API is accessible
        
        Returns:
            True if API is healthy, False otherwise
        """
        try:
            grant_url = f'{self.api_uri}/grants/{self.grant_id}'
            response = requests.get(grant_url, headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                grant_data = response.json().get('data', {})
                status = grant_data.get('grant_status')
                logger.info(f"Nylas API health check: grant status = {status}")
                return status == 'valid'
            else:
                logger.error(f"Nylas API health check failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Nylas API health check error: {str(e)}")
            return False
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((requests.exceptions.ConnectionError, requests.exceptions.Timeout)),
        reraise=True
    )
    def send_reply(self, reply_data: Dict) -> Dict:
        """
        Send a reply email through Nylas API with proper threading
        
        Args:
            reply_data: Dictionary containing reply information:
                - to_email: Recipient email address
                - entity_id: Entity ID for plus-addressing
                - subject: Email subject
                - body: Email body content
                - reply_to_message_id: Original message ID for threading
                - thread_id: Thread ID (optional, for validation)
                
        Returns:
            Dictionary with send result:
                - success: Boolean indicating success
                - external_message_id: Nylas message ID if successful
                - error: Error message if failed
        """
        try:
            # Generate unique request ID for idempotency
            request_id = str(uuid.uuid4())
            
            # Build from address with entity plus-addressing
            from_email = f"inbox+{reply_data['entity_id']}@drcrlabs.com"
            
            # Prepare message payload
            message_payload = {
                "to": [{"email": reply_data["to_email"]}],
                "from": [{"email": from_email}],
                "subject": reply_data["subject"],
                "body": reply_data["body"],
                "reply_to_message_id": reply_data["reply_to_message_id"]
            }
            
            # Add thread_id if provided for validation
            if reply_data.get("thread_id"):
                message_payload["thread_id"] = reply_data["thread_id"]
            
            # Prepare headers with idempotency
            headers = self.headers.copy()
            headers["Nylas-Client-Request-Id"] = request_id
            
            # Send message
            messages_url = f'{self.api_uri}/grants/{self.grant_id}/messages'
            logger.info(f"Sending reply from {from_email} to {reply_data['to_email']}")
            
            response = requests.post(
                messages_url,
                headers=headers,
                json=message_payload,
                timeout=30
            )
            
            if response.status_code in [200, 201]:
                data = response.json()
                external_message_id = data.get('data', {}).get('id')
                
                logger.info(f"Reply sent successfully: {external_message_id}")
                return {
                    "success": True,
                    "external_message_id": external_message_id,
                    "request_id": request_id
                }
            else:
                error_msg = f"Nylas API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "error": error_msg,
                    "status_code": response.status_code
                }
                
        except Exception as e:
            error_msg = f"Error sending reply: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }