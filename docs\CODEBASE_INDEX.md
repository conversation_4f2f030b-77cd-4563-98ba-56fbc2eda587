# DRCR Frontend Codebase Index

**Last Updated:** $(date)  
**Version:** 0.0.0  
**Framework:** React 19.1 + TypeScript + Vite

## Project Structure Overview

```
drcr_front/
├── public/                     # Static assets
│   ├── vite.svg               # Vite logo
│   └── logo files             # DRCR logos
├── src/                       # Source code
│   ├── components/            # React components
│   ├── pages/                 # Page components
│   ├── hooks/                 # Custom React hooks
│   ├── lib/                   # Utility libraries
│   ├── services/              # API services
│   ├── store/                 # State management
│   ├── types/                 # TypeScript definitions
│   ├── providers/             # React context providers
│   ├── utils/                 # Utility functions
│   └── assets/                # Static assets
├── docs/                      # Documentation
├── config files               # Configuration files
└── package files              # Package management
```

## Core Technologies

### Frontend Stack
- **React**: 18.3.1
- **TypeScript**: ~5.8.3
- **Vite**: 6.3.5 (Build tool)
- **React Router**: 7.6.0 (Routing)

### UI & Styling
- **Shadcn/UI**: Component library
- **Tailwind CSS**: 3.4.15 (Utility-first CSS)
- **Radix UI**: Primitive components
- **Lucide React**: 0.511.0 (Icons)
- **Class Variance Authority**: 0.7.1 (Component variants)

### State & Data Management
- **Zustand**: 5.0.5 (State management)
- **React Hook Form**: 7.56.4 (Form handling)
- **Zod**: 3.25.17 (Schema validation)
- **Axios**: 1.9.0 (HTTP client)

### Authentication & Backend
- **Firebase**: 11.8.0 (Authentication)
- **Backend API**: FastAPI (Python)

## Component Architecture

### UI Components (`src/components/ui/`)
**Status**: ✅ Well-organized Shadcn/UI components

- `alert.tsx` - Alert notifications
- `avatar.tsx` - User avatars
- `breadcrumb.tsx` - Navigation breadcrumbs
- `button.tsx` - Button component
- `card.tsx` - Card containers
- `collapsible.tsx` - Collapsible content
- `dropdown-menu.tsx` - Dropdown menus
- `form.tsx` - Form components
- `input.tsx` - Input fields
- `label.tsx` - Form labels
- `separator.tsx` - Visual separators
- `sheet.tsx` - Side sheets
- `sidebar.tsx` - Sidebar component
- `skeleton.tsx` - Loading skeletons
- `sonner.tsx` - Toast notifications
- `tooltip.tsx` - Tooltips

### Layout Components (`src/components/layout/`)
**Status**: ⚠️ Needs cleanup - duplicate components exist

- `AppLayout.tsx` - Main application layout
- `AppSidebar.tsx` - Application sidebar ✅ (Keep this)
- `ClientSwitcher.tsx` - Client organization switcher
- `NavMain.tsx` - Main navigation
- `NavUser.tsx` - User navigation
- `SidebarLayout.tsx` - Sidebar layout wrapper

### Authentication Components (`src/components/auth/`)
**Status**: ✅ Well-structured

- `LoginForm.tsx` - Login form component
- `ProtectedRoute.tsx` - Route protection wrapper
- `SignupForm.tsx` - User registration form

### Additional Components (verified 2025-01-15)
- `TailwindTest.tsx` - Tailwind CSS testing component
- `XeroOperationDemo.tsx` - Xero integration demonstration component
- `XeroOrganizationSelector.tsx` - Organization selection component for Xero setup

### Removed Components ✅
- Duplicate components have been cleaned up as documented

## Page Components (`src/pages/`)

### Authentication Pages
- `ShadcnLoginPage.tsx` - Main login page ✅ (Primary)
- `LoginPage.tsx` - Alternative login page
- `BasicLoginPage.tsx` - Basic login implementation
- `EnhancedLoginPage.tsx` - Enhanced login implementation

### Application Pages
- `DashboardPage.tsx` - Main dashboard ✅
- `PrepaymentsPage.tsx` - Prepayments management
- `SettingsPage.tsx` - Application settings

### Test/Development Pages
- `TailwindTest.tsx` - Tailwind CSS testing component

## State Management (`src/store/`)

### Auth Store (`auth.store.ts`)
**Status**: ✅ Well-implemented Zustand store

**State:**
- `user: UserProfile | null` - Current user
- `isLoading: boolean` - Loading state
- `error: string | null` - Error messages

**Actions:**
- `initialize()` - Initialize auth state
- `signUp(email, password)` - User registration
- `signIn(email, password)` - User login
- `signOut()` - User logout
- `resetError()` - Clear error state

## Services & API (`src/services/`, `src/lib/`)

### Service Layer (9 Services - verified 2025-01-15)
**Status**: ✅ Complete service layer with domain separation

#### **Authentication Service (`auth.service.ts`)**
- `signIn(email, password)` - Firebase sign in
- `signUp(email, password)` - Firebase sign up
- `signOut()` - Firebase sign out
- `handleFirebaseError(error)` - Error handling

#### **Clients Service (`clients.service.ts`)**
- `getClients()` / `getClientsEnhanced()` - Client listing with pagination
- `createClient()` / `createClientEnhanced()` - Client creation (legacy + enhanced)
- `createClientFromWizard()` - Multi-step client creation
- `getClient()` / `getClientDetails()` - Client details
- `updateClient()` / `updateClientEnhanced()` - Client updates
- `deleteClient()` - Soft delete operations
- `getClientEnums()` - Form options

#### **Dashboard Service (`dashboard.service.ts`)**
- `getFirmDashboard()` - Firm-level dashboard data
- `getClientDashboard()` - Client-specific dashboard

#### **Entities Service (`entities.service.ts`)**
- `getEntitiesForClient()` - Entity listing
- `getEntity()` - Entity details
- `createEntityFromWizard()` - Entity creation workflow
- `updateEntitySettings()` - Settings management
- `checkEntityConnectionStatus()` / `disconnectEntity()` - Connection management

#### **Prepayments Service (`prepayments.service.ts`)**
- `getPrepaymentsData()` - Dashboard data with filtering
- Schedule operations: `getSchedule()`, `updateSchedule()`, `confirmSchedule()`, `skipSchedule()`
- Calculations: `calculatePreview()`, `recalculateSchedule()`, `previewChanges()`
- Entry management: `updateMonthlyEntry()`
- Xero posting: `postReadyEntries()`, `postSingleEntry()`
- Attachments: `getAttachmentUrl()`

#### **Users Service (`users.service.ts`)**
- `getUserProfile()` - User profile retrieval
- `updateUserProfile()` - Profile updates

#### **Firm Service (`firm.service.ts`)**
- `getFirmDetails()` - Firm information
- `updateFirmSettings()` - Firm configuration

#### **Post-Processing Service (`post-processing.service.ts`)**
- `processSchedule()` - Schedule processing workflows
- `validateSchedule()` - Schedule validation

#### **Token Usage Service (`tokenUsage.service.ts`)**
- `trackTokenUsage()` - LLM token tracking
- `getTokenUsageSummary()` - Usage analytics

### API Client (`lib/api.ts`)
**Status**: ✅ Axios-based client with auth

**Features:**
- Automatic token injection
- Request/response interceptors
- Error handling
- Type-safe methods

### Firebase Configuration (`lib/firebase.ts`)
**Status**: ✅ Properly configured

### Utilities (`lib/utils.ts`)
**Status**: ✅ Tailwind utilities

## Hooks (`src/hooks/`)

### Custom Hooks
- `use-mobile.ts` - Mobile detection hook ✅ (Keep this)
- `use-mobile.tsx` - ❌ Duplicate file (Remove)
- `use-sidebar-context.tsx` - Sidebar context hook

## Types (`src/types/`)

### Type Definitions
- `auth.types.ts` - Authentication types

## Configuration Files

### Build Configuration
- `vite.config.ts` - Vite configuration ✅
- `tsconfig.json` - TypeScript configuration ✅
- `tsconfig.app.json` - App-specific TypeScript config
- `tsconfig.node.json` - Node-specific TypeScript config

### Styling Configuration
- `tailwind.config.js` - Tailwind CSS configuration ✅
- `postcss.config.cjs` - PostCSS configuration ✅ (Keep this)
- `postcss.config.js` - ❌ Duplicate PostCSS config (Remove)
- `index.css` - Global styles and Tailwind imports

### Package Management
- `package.json` - Dependencies and scripts ✅
- `package-lock.json` - Dependency lock file
- `components.json` - Shadcn/UI configuration ✅

### Linting & Code Quality
- `eslint.config.js` - ESLint configuration ✅

## Documentation

### Project Documentation
- `README.md` - Project overview and setup
- `FRONTEND_BACKEND_INTEGRATION.md` - Integration guide
- `DEVELOPMENT_GUIDELINES.md` - Development standards ✅ (New)
- `ISSUES_REPORT.md` - Current issues report ✅ (New)
- `CODEBASE_INDEX.md` - This file ✅ (New)

### Environment Configuration
- `.env.example` - Environment variables template ✅

## Dependencies Analysis

### Production Dependencies (42 packages)
**Core Framework:**
- react@18.3.1, react-dom@18.3.1
- typescript@5.8.3

**UI & Styling:**
- @radix-ui/* (Multiple packages)
- tailwindcss@3.4.15
- lucide-react@0.511.0

**State & Forms:**
- zustand@5.0.5
- react-hook-form@7.56.4
- zod@3.25.17

**HTTP & Auth:**
- axios@1.9.0
- firebase@11.8.0

**Unused Dependencies (❌ Remove):**
- next-themes@0.4.6 - Not implemented
- tw-animate-css@1.3.0 - Not used

### Development Dependencies (11 packages)
**Build Tools:**
- vite@6.3.5
- @vitejs/plugin-react@4.4.1

**Linting:**
- eslint@9.25.0
- typescript-eslint@8.30.1

**Types:**
- @types/react@18.3.12
- @types/react-dom@18.3.1

## Current Issues Summary

### Critical (🚨)
1. Duplicate files causing conflicts
2. PostCSS configuration conflicts
3. Component architecture inconsistencies

### High Priority (⚠️)
1. Unused dependencies
2. TypeScript configuration issues
3. Missing test infrastructure

### Medium Priority (📋)
1. Tailwind CSS version preference
2. Component standardization
3. Performance optimizations

## Recommended Actions

### Immediate (Today)
1. Remove duplicate files
2. Fix PostCSS configuration
3. Clean up component structure

### This Week
1. Remove unused dependencies
2. Standardize TypeScript usage
3. Update documentation

### This Month
1. Set up testing infrastructure
2. Implement performance optimizations
3. Add accessibility features

---

**Maintenance Notes:**
- Update this index when adding new components
- Review dependencies monthly
- Keep documentation in sync with code changes
