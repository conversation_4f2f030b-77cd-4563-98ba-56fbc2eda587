# Fix Nylas Send Permissions - Dashboard Method

## Issue Found
✅ **Grant Status**: valid  
✅ **Email**: <EMAIL>  
❌ **Scopes**: [] (empty - no send permissions)

## Quick Fix Steps

### 1. Go to <PERSON>ylas Dashboard
Visit: https://dashboard.nylas.com

### 2. Navigate to Your App
- Click on your app (likely named something with "drcr" or "email")
- Go to **"Grants"** section

### 3. Check Current Grant
- Find grant ID: `c12ff227-31ed-4099-b70f-6fde75f02e02`
- You should see it has **no scopes** or only read scopes

### 4. Update Google Integration Scopes
- Go to **"Integrations"** → **"Google"**
- Make sure these scopes are enabled:
  - `https://www.googleapis.com/auth/gmail.readonly` ✅
  - `https://www.googleapis.com/auth/gmail.compose` ❌ (add this)
  - `https://www.googleapis.com/auth/gmail.send` ❌ (add this)

### 5. Re-authenticate Gmail Account
Option A - **Test Integration (Recommended)**:
- Go to **"Test your integration"** 
- Click **"Add account"**
- Sign in with `<EMAIL>`
- **IMPORTANT**: Grant ALL permissions including "Compose and send emails"
- Copy the new Grant ID

Option B - **Manual Re-auth**:
- Delete the existing grant
- Create a new one with proper scopes

### 6. Update Environment Variables
Replace the old grant ID with the new one:

```bash
# Old (current)
$env:NYLAS_GRANT_ID="c12ff227-31ed-4099-b70f-6fde75f02e02"

# New (from step 5)
$env:NYLAS_GRANT_ID="NEW_GRANT_ID_HERE"
```

### 7. Update Cloud Function Environment
Don't forget to update the deployed Cloud Function:

```bash
gcloud functions deploy nylas_email_poller --set-env-vars "NYLAS_GRANT_ID=NEW_GRANT_ID_HERE,NYLAS_API_KEY=nyk_v0_NlAanHXzaCvTlVVKTZMDrSrWghdTPRRkP7cPRe4N8ohY3OMueY3lMVmk2KzTkifj" --region europe-west2
```

### 8. Test Again
Run the reply test:

```bash
python test_real_reply.py
```

## Expected Result After Fix
- 🔐 **Scopes**: Should show gmail.readonly, gmail.compose, gmail.send
- 📤 **Send Test**: Should return 200/201 instead of 403
- ✅ **Reply Test**: Should successfully send email

## Troubleshooting
- If you can't find your app: Look for Client ID `672d85ca-ad40-4d7d-b6ee-fbbd1d7e1d29`
- If scopes don't save: You may need to recreate the Google integration
- If Gmail rejects: Make sure you're signing in with the correct Gmail account (`<EMAIL>`)