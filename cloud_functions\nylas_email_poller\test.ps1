# Test Nylas Email Poller function
Write-Host "Testing Nylas Email Poller..." -ForegroundColor Green

$uri = "https://europe-west2-drcr-d660a.cloudfunctions.net/nylas_email_poller"
$body = '{"test": true}'

try {
    $response = Invoke-RestMethod -Uri $uri -Method POST -Body $body -ContentType "application/json"
    
    Write-Host "Response:" -ForegroundColor Yellow
    $response | ConvertTo-Json -Depth 5
    
    if ($response.status -eq "success") {
        Write-Host "`n✅ SUCCESS" -ForegroundColor Green
        Write-Host "Duration: $($response.duration_seconds)s | Processed: $($response.processed_emails)" -ForegroundColor Cyan
    } else {
        Write-Host "❌ ERROR in response" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ ERROR: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nLogs: gcloud functions logs read nylas_email_poller --region europe-west2 --limit 5" -ForegroundColor Yellow