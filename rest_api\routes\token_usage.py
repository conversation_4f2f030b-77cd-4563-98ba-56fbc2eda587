"""
Token Usage Analytics API

Provides endpoints for querying LLM token usage and costs across sync jobs.
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
from google.cloud import firestore
import logging

from ..core.firebase_auth import get_current_user, get_firm_user_with_client_access, AuthUser
from ..dependencies import get_db
from ..models.token_usage import TokenUsageSummary, ClientTokenUsage, SyncJobTokenUsage

logger = logging.getLogger(__name__)
router = APIRouter(tags=["Token Usage Analytics"])


@router.get("/sync/{sync_job_id}/tokens", response_model=SyncJobTokenUsage)
async def get_sync_job_token_usage(
    sync_job_id: str,
    db: firestore.AsyncClient = Depends(get_db),
    current_user: AuthUser = Depends(get_current_user)
):
    """
    Get token usage details for a specific sync job.
    
    Returns:
        - Total token counts by provider
        - Total cost
        - Number of operations
        - Detailed operation list (if available)
    """
    try:
        # Get sync job document
        sync_job_ref = db.collection("SYNC_JOBS").document(sync_job_id)
        sync_job_doc = await sync_job_ref.get()
        
        if not sync_job_doc.exists:
            raise HTTPException(status_code=404, detail="Sync job not found")
        
        sync_job_data = sync_job_doc.to_dict()
        
        # Check user has access to this client
        client_id = sync_job_data.get("client_id")
        if not client_id:
            raise HTTPException(status_code=400, detail="Sync job missing client_id")
        
        # Verify user has access to this client
        try:
            await get_firm_user_with_client_access(client_id, current_user)
        except HTTPException:
            raise HTTPException(status_code=403, detail="Access denied to this client")
        
        # Extract token usage data
        token_usage = sync_job_data.get("token_usage", {})
        
        return SyncJobTokenUsage(
            sync_job_id=sync_job_id,
            client_id=client_id,
            entity_id=sync_job_data.get("entity_id"),
            status=sync_job_data.get("status"),
            openai_tokens=token_usage.get("openai_tokens", 0),
            mistral_tokens=token_usage.get("mistral_tokens", 0),
            total_cost=token_usage.get("total_cost", 0.0),
            operations_count=sync_job_data.get("token_breakdown_count", 0),
            operations=token_usage.get("operations", []),
            started_at=sync_job_data.get("started_at"),
            completed_at=sync_job_data.get("completed_at")
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting sync job token usage: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/client/{client_id}/tokens", response_model=ClientTokenUsage)
async def get_client_token_usage(
    client_id: str,
    start_date: Optional[datetime] = Query(None, description="Start date for filtering (ISO 8601)"),
    end_date: Optional[datetime] = Query(None, description="End date for filtering (ISO 8601)"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of sync jobs to include"),
    detailed: bool = Query(False, description="Include detailed operation lists (may result in large responses)"),
    db: firestore.AsyncClient = Depends(get_db),
    current_user: AuthUser = Depends(get_current_user)
):
    """
    Get aggregated token usage for a client across all sync jobs.
    
    Optional filtering by date range.
    Returns summary totals plus recent sync job details.
    Set detailed=true to include operation lists (may result in large responses).
    """
    try:
        # Verify user has access to this client
        await get_firm_user_with_client_access(client_id, current_user)
        
        # Build query for sync jobs
        query = db.collection("SYNC_JOBS").where(
            filter=firestore.FieldFilter("client_id", "==", client_id)
        )
        
        # Add date filtering if provided
        if start_date:
            query = query.where(
                filter=firestore.FieldFilter("started_at", ">=", start_date)
            )
        if end_date:
            query = query.where(
                filter=firestore.FieldFilter("started_at", "<=", end_date)
            )
        
        # Order by start time and apply limit
        query = query.order_by("started_at", direction=firestore.Query.DESCENDING).limit(limit)
        
        # Execute query
        sync_jobs = []
        total_openai_tokens = 0
        total_mistral_tokens = 0
        total_cost = 0.0
        total_operations = 0
        
        async for doc in query.stream():
            job_data = doc.to_dict()
            token_usage = job_data.get("token_usage", {})
            
            # Aggregate totals
            openai_tokens = token_usage.get("openai_tokens", 0)
            mistral_tokens = token_usage.get("mistral_tokens", 0)
            cost = token_usage.get("total_cost", 0.0)
            operations_count = job_data.get("token_breakdown_count", 0)
            
            total_openai_tokens += openai_tokens
            total_mistral_tokens += mistral_tokens
            total_cost += cost
            total_operations += operations_count
            
            # Add to sync jobs list
            sync_job_summary = {
                "sync_job_id": doc.id,
                "entity_id": job_data.get("entity_id"),
                "status": job_data.get("status"),
                "openai_tokens": openai_tokens,
                "mistral_tokens": mistral_tokens,
                "total_cost": cost,
                "operations_count": operations_count,
                "started_at": job_data.get("started_at"),
                "completed_at": job_data.get("completed_at")
            }
            
            # Include detailed operations only if requested
            if detailed:
                operations = token_usage.get("operations", [])
                # Convert dict operations to TokenOperationDetail format
                sync_job_summary["operations"] = [
                    {
                        "provider": op.get("provider", ""),
                        "model": op.get("model", ""),
                        "operation": op.get("operation", ""),
                        "tokens": op.get("tokens", 0),
                        "cost": op.get("cost", 0.0),
                        "timestamp": op.get("timestamp", "")
                    }
                    for op in operations
                ]
            
            sync_jobs.append(sync_job_summary)
        
        return ClientTokenUsage(
            client_id=client_id,
            total_openai_tokens=total_openai_tokens,
            total_mistral_tokens=total_mistral_tokens,
            total_cost=total_cost,
            total_operations=total_operations,
            sync_jobs_count=len(sync_jobs),
            start_date=start_date,
            end_date=end_date,
            sync_jobs=sync_jobs
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting client token usage: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/summary", response_model=TokenUsageSummary)
async def get_token_usage_summary(
    start_date: Optional[datetime] = Query(None, description="Start date for filtering (ISO 8601)"),
    end_date: Optional[datetime] = Query(None, description="End date for filtering (ISO 8601)"),
    db: firestore.AsyncClient = Depends(get_db),
    current_user: AuthUser = Depends(get_current_user)
):
    """
    Get token usage summary across all clients the user has access to.
    
    Admin users see all usage, regular users see only their clients.
    """
    try:
        # Build base query
        query = db.collection("SYNC_JOBS")
        
        # Add date filtering if provided
        if start_date:
            query = query.where(
                filter=firestore.FieldFilter("started_at", ">=", start_date)
            )
        if end_date:
            query = query.where(
                filter=firestore.FieldFilter("started_at", "<=", end_date)
            )
        
        # For non-admin users, filter by accessible clients
        accessible_clients = []
        if not current_user.is_admin:
            # Get user's accessible clients
            user_ref = db.collection("USERS").document(current_user.uid)
            user_doc = await user_ref.get()
            
            if user_doc.exists:
                user_data = user_doc.to_dict()
                accessible_clients = user_data.get("accessible_clients", [])
            
            if not accessible_clients:
                # User has no accessible clients
                return TokenUsageSummary(
                    total_openai_tokens=0,
                    total_mistral_tokens=0,
                    total_cost=0.0,
                    total_operations=0,
                    sync_jobs_count=0,
                    clients_count=0,
                    start_date=start_date,
                    end_date=end_date
                )
        
        # Aggregate data
        total_openai_tokens = 0
        total_mistral_tokens = 0
        total_cost = 0.0
        total_operations = 0
        sync_jobs_count = 0
        clients_seen = set()
        
        async for doc in query.stream():
            job_data = doc.to_dict()
            job_client_id = job_data.get("client_id")
            
            # Skip if user doesn't have access to this client
            if not current_user.is_admin and job_client_id not in accessible_clients:
                continue
            
            token_usage = job_data.get("token_usage", {})
            
            # Aggregate totals
            total_openai_tokens += token_usage.get("openai_tokens", 0)
            total_mistral_tokens += token_usage.get("mistral_tokens", 0)
            total_cost += token_usage.get("total_cost", 0.0)
            total_operations += job_data.get("token_breakdown_count", 0)
            sync_jobs_count += 1
            
            if job_client_id:
                clients_seen.add(job_client_id)
        
        return TokenUsageSummary(
            total_openai_tokens=total_openai_tokens,
            total_mistral_tokens=total_mistral_tokens,
            total_cost=total_cost,
            total_operations=total_operations,
            sync_jobs_count=sync_jobs_count,
            clients_count=len(clients_seen),
            start_date=start_date,
            end_date=end_date
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting token usage summary: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")