"""
Entity Analysis Service - Quick analysis for entity setup wizard
Provides smart defaults and recommendations based on Xero organization data
"""
from typing import Dict, Any, List, Optional
import logging
from datetime import datetime, timedelta
from google.cloud import firestore

from drcr_shared_logic.clients.xero_client import XeroApiClient

logger = logging.getLogger(__name__)


class EntityAnalysisService:
    """Service for analyzing entity data to provide wizard recommendations"""

    def __init__(self, db):
        self.db = db

    async def analyze_entity_for_wizard(self, entity_id: str) -> Dict[str, Any]:
        """
        Quick analysis of entity for setup wizard
        Returns smart defaults and recommendations
        """
        try:
            # Get entity data
            entity_ref = self.db.collection("ENTITIES").document(entity_id)
            entity_doc = await entity_ref.get()
            
            if not entity_doc.exists:
                raise ValueError("Entity not found")
            
            entity_data = entity_doc.to_dict()
            client_id = entity_data.get("client_id")
            entity_type = entity_data.get("type")
            
            if entity_type != "xero":
                # For non-Xero entities, return basic analysis
                return self._get_basic_analysis(entity_data)
            
            # For Xero entities, get detailed analysis
            return await self._analyze_xero_entity(entity_id, client_id, entity_data)
            
        except Exception as e:
            logger.error(f"Failed to analyze entity {entity_id}: {e}")
            raise

    async def _analyze_xero_entity(self, entity_id: str, client_id: str, entity_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze Xero entity with quick API queries"""
        try:
            # Initialize Xero client
            xero_client = await XeroApiClient.create(
                platform_org_id=entity_id,
                tenant_id=client_id
            )
            
            # Get organization details (already synced)
            organization_info = await self._get_organization_details(xero_client)
            
            # Get chart of accounts (already synced)
            accounts_info = await self._analyze_chart_of_accounts(entity_id)
            
            # Quick count queries for bills with attachments
            bills_analysis = await self._analyze_bills_with_attachments(xero_client, organization_info)
            
            # Generate smart recommendations
            recommendations = await self._generate_recommendations(accounts_info, bills_analysis, organization_info)
            
            return {
                "entity_id": entity_id,
                "entity_name": entity_data.get("entity_name"),
                "organization_info": organization_info,
                "accounts_analysis": accounts_info,
                "bills_analysis": bills_analysis,
                "recommendations": recommendations,
                "analysis_timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze Xero entity {entity_id}: {e}")
            # Return basic analysis if detailed analysis fails
            return self._get_basic_analysis(entity_data)

    async def _get_organization_details(self, xero_client: XeroApiClient) -> Dict[str, Any]:
        """Get organization details from Xero API"""
        try:
            # Use existing get_records method for Organization endpoint
            org_data = await xero_client.get_records("Organisation")
            
            if org_data and len(org_data) > 0:
                org = org_data[0]  # Organisation endpoint returns array with single item
                
                return {
                    "name": org.get("Name"),
                    "legal_name": org.get("LegalName"),
                    "country_code": org.get("CountryCode"),
                    "base_currency": org.get("BaseCurrency"),
                    "financial_year_end_day": org.get("FinancialYearEndDay"),
                    "financial_year_end_month": org.get("FinancialYearEndMonth"),
                    "tax_number": org.get("TaxNumber"),
                    "organisation_type": org.get("OrganisationType"),
                    "version": org.get("Version")
                }
            else:
                logger.warning("No organization data returned from Xero")
                return {}
                
        except Exception as e:
            logger.warning(f"Failed to get organization details: {e}")
            return {}

    async def _analyze_chart_of_accounts(self, entity_id: str) -> Dict[str, Any]:
        """Analyze chart of accounts from Firestore (already synced)"""
        try:
            # Query synced chart of accounts
            accounts_query = self.db.collection("CHART_OF_ACCOUNTS").where(
                filter=firestore.FieldFilter("entity_id", "==", entity_id)
            )
            accounts_docs = await accounts_query.get()
            
            asset_accounts = []
            expense_accounts = []
            bank_fee_accounts = []
            
            for doc in accounts_docs:
                account = doc.to_dict()
                account_type = account.get("type", "").upper()
                account_class = account.get("class", "").upper()
                name = account.get("name", "").lower()
                
                # Identify potential prepayment asset accounts
                if account_type in ["ASSET", "CURRENTASSET"] and any(
                    keyword in name for keyword in ["prepaid", "prepayment", "advance", "deposit"]
                ):
                    asset_accounts.append({
                        "code": account.get("code"),
                        "name": account.get("name"),
                        "account_id": account.get("account_id"),
                        "recommended": True
                    })
                
                # Identify common expense accounts
                if account_type in ["EXPENSE", "OVERHEADS"] and any(
                    keyword in name for keyword in [
                        "insurance", "software", "subscription", "rent", "utilities",
                        "license", "maintenance", "support"
                    ]
                ):
                    expense_accounts.append({
                        "code": account.get("code"),
                        "name": account.get("name"),
                        "account_id": account.get("account_id")
                    })
                
                # Identify accounts to exclude
                if any(keyword in name for keyword in ["bank fee", "interest", "fee", "charge"]):
                    bank_fee_accounts.append({
                        "code": account.get("code"),
                        "name": account.get("name"),
                        "account_id": account.get("account_id")
                    })
            
            return {
                "total_accounts": len(accounts_docs),
                "prepayment_asset_accounts": asset_accounts,
                "relevant_expense_accounts": expense_accounts[:10],  # Limit to top 10
                "suggested_exclusions": bank_fee_accounts,
                "default_expense_account": expense_accounts[0] if expense_accounts else None
            }
            
        except Exception as e:
            logger.warning(f"Failed to analyze chart of accounts: {e}")
            return {"total_accounts": 0, "prepayment_asset_accounts": [], "relevant_expense_accounts": []}

    async def _analyze_bills_with_attachments(self, xero_client: XeroApiClient, org_info: Dict[str, Any]) -> Dict[str, Any]:
        """Quick count queries for bills with attachments"""
        try:
            # Calculate financial year start date
            financial_year_start = self._calculate_financial_year_start(org_info)
            
            # Format date for Xero API (YYYY-MM-DD)
            start_date_str = financial_year_start.strftime("%Y-%m-%d")
            
            # Quick count query for bills with attachments
            bills_with_attachments_where = f"Type==\"ACCPAY\" AND Status!=\"VOIDED\" AND Status!=\"DELETED\" AND HasAttachments==true AND Date>={start_date_str}"
            
            # Quick count query for total bills
            total_bills_where = f"Type==\"ACCPAY\" AND Status!=\"VOIDED\" AND Status!=\"DELETED\" AND Date>={start_date_str}"
            
            # Use Xero API to get counts (not full data)
            bills_with_attachments = await self._get_invoice_count(xero_client, bills_with_attachments_where)
            total_bills = await self._get_invoice_count(xero_client, total_bills_where)
            
            return {
                "financial_year_start": start_date_str,
                "bills_with_attachments": bills_with_attachments,
                "total_bills": total_bills,
                "scanning_cost_estimate": "Variable (1 credit per page - most bills are 1-3 pages)",
                "expected_prepayments": self._estimate_prepayments(bills_with_attachments)
            }
            
        except Exception as e:
            logger.warning(f"Failed to analyze bills with attachments: {e}")
            return {
                "bills_with_attachments": 0,
                "total_bills": 0,
                "scanning_cost_estimate": "Variable (1 credit per page)",
                "expected_prepayments": "0-2 items"
            }

    async def _get_invoice_count(self, xero_client: XeroApiClient, where_clause: str) -> int:
        """Get count of invoices matching criteria using minimal API query"""
        try:
            # Make a minimal query with pagination to get just the count
            # Xero API doesn't have a direct count endpoint, so we use a small page
            invoices = await xero_client.get_records(
                record_type="Invoices",
                where_filter=where_clause,
                order="Date DESC",
                page=1
            )
            
            # For a more accurate count, we could make multiple paginated requests
            # But for wizard purposes, even an estimate based on first page is useful
            if hasattr(invoices, '__len__'):
                # If we get a full page (100 items), there are likely more
                # For wizard purposes, we can provide ranges like "100+" 
                return len(invoices)
            else:
                return 0
                
        except Exception as e:
            logger.warning(f"Failed to get invoice count: {e}")
            return 0

    def _calculate_financial_year_start(self, org_info: Dict[str, Any]) -> datetime:
        """Calculate financial year start date based on organization settings"""
        try:
            current_date = datetime.now()
            
            # Get financial year end from organization
            fy_end_day = org_info.get("financial_year_end_day", 31)
            fy_end_month = org_info.get("financial_year_end_month", 3)  # Default to March
            
            # Calculate current financial year start
            if current_date.month > fy_end_month or (current_date.month == fy_end_month and current_date.day > fy_end_day):
                # We're past the financial year end, so this year's start
                fy_start = datetime(current_date.year, fy_end_month, fy_end_day) + timedelta(days=1)
            else:
                # We're before the financial year end, so last year's start
                fy_start = datetime(current_date.year - 1, fy_end_month, fy_end_day) + timedelta(days=1)
            
            return fy_start
            
        except Exception as e:
            logger.warning(f"Failed to calculate financial year start: {e}")
            # Default to July 1st (common financial year start)
            current_year = datetime.now().year
            return datetime(current_year, 7, 1)

    def _estimate_prepayments(self, bills_count: int) -> str:
        """Estimate number of prepayments based on bills count"""
        if bills_count == 0:
            return "0 items"
        elif bills_count < 20:
            return "2-5 items"
        elif bills_count < 50:
            return "5-8 items"
        elif bills_count < 100:
            return "8-15 items"
        else:
            return "15-25+ items"

    async def _generate_recommendations(self, accounts_info: Dict[str, Any], bills_analysis: Dict[str, Any], org_info: Dict[str, Any]) -> Dict[str, Any]:
        """Generate smart recommendations based on analysis"""
        recommendations = {
            "sync_start_date": bills_analysis.get("financial_year_start"),
            "sync_frequency": "daily",  # Default recommendation
            "enable_ai_scanning": bills_analysis.get("bills_with_attachments", 0) > 0,
            "selected_asset_accounts": [acc["code"] for acc in accounts_info.get("prepayment_asset_accounts", [])],
            "excluded_accounts": [acc["code"] for acc in accounts_info.get("suggested_exclusions", [])],
            "default_expense_account": accounts_info.get("default_expense_account", {}).get("code") if accounts_info.get("default_expense_account") else None,
            "base_currency": org_info.get("base_currency", "USD"),
            "priority": "high" if bills_analysis.get("bills_with_attachments", 0) > 50 else "medium"
        }
        
        return recommendations

    def _get_basic_analysis(self, entity_data: Dict[str, Any]) -> Dict[str, Any]:
        """Return basic analysis for non-Xero entities or when detailed analysis fails"""
        return {
            "entity_id": entity_data.get("entity_id"),
            "entity_name": entity_data.get("entity_name"),
            "entity_type": entity_data.get("type"),
            "organization_info": {"name": entity_data.get("entity_name")},
            "accounts_analysis": {"total_accounts": 0, "prepayment_asset_accounts": []},
            "bills_analysis": {"bills_with_attachments": 0, "total_bills": 0},
            "recommendations": {
                "sync_start_date": datetime.now().strftime("%Y-%m-%d"),
                "sync_frequency": "daily",
                "enable_ai_scanning": False,
                "base_currency": "USD"
            },
            "analysis_timestamp": datetime.utcnow().isoformat()
        }