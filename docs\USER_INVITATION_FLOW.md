# User Invitation Flow Documentation

## Overview
This document explains the complete user invitation flow, including how invited users transition from "invited" to "active" status after setting up their password.

## Flow Summary

### 1. User Invitation (`POST /auth/invite-user`)
- Firm admin invites a user via email
- System creates or finds existing Firebase user
- Creates FIRM_USERS document with status "invited"
- Generates invitation token (token_type: "invitation")
- Sends invitation email with setup link

### 2. User Setup Process
- User clicks setup link in email (contains invitation token)
- User is redirected to `/reset-password?token=<invitation_token>`
- User enters new password and submits

### 3. Password Reset with Status Update (`POST /auth/reset-password`)
- System validates the invitation token
- Updates user password in Firebase Auth
- **Critical**: Detects token_type "invitation" and automatically updates user status from "invited" to "active"
- Marks token as used

### 4. User Access
- User can now login with their email and password
- User has "active" status and can access the system

## Key Components

### PasswordResetService.reset_password()
Location: `rest_api/services/password_reset_service.py:319-380`

**Enhanced Logic (Fixed Issue):**
```python
# Check if this is an invitation token - if so, update user status from "invited" to "active"
token_type = token_data.get("token_type")
if token_type == "invitation":
    try:
        # Find the user in FIRM_USERS collection and update their status
        user_query = self.db.collection("FIRM_USERS").where("user_id", "==", user_id)
        user_docs = await user_query.get()
        
        for user_doc in user_docs:
            user_data = user_doc.to_dict()
            if user_data.get("status") == "invited":
                await user_doc.reference.update({
                    "status": "active",
                    "updated_at": SERVER_TIMESTAMP
                })
                logger.info(f"User {user_id} status updated from 'invited' to 'active' after password reset")
                break
    except Exception as e:
        logger.error(f"Error updating user status for {user_id}: {e}")
        # Don't fail the password reset if status update fails
```

### Token Types
- **"invitation"**: Generated for new user invitations
- **Regular reset**: Generated for existing user password resets

### User Status Values
- **"invited"**: User has been invited but hasn't set up their account
- **"active"**: User has completed setup and can access the system
- **"inactive"**: User has been deactivated
- **"suspended"**: User has been suspended

## Database Schema

### FIRM_USERS Collection
```json
{
  "user_id": "string (Firebase UID)",
  "firm_id": "string",
  "email": "string",
  "status": "invited|active|inactive|suspended",
  "role": "firm_admin|firm_staff",
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

### password_reset_tokens Collection
```json
{
  "user_id": "string (Firebase UID)",
  "token": "string (hashed)",
  "token_type": "invitation|reset",
  "expires_at": "timestamp",
  "used": "boolean",
  "created_at": "timestamp"
}
```

## API Endpoints

### POST /auth/invite-user
**Purpose:** Invite a new user to the firm
**Token Generated:** invitation token (token_type: "invitation")
**User Status:** Set to "invited"

### POST /auth/reset-password
**Purpose:** Reset password using token
**Enhanced Logic:** 
- If token_type is "invitation", automatically update user status to "active"
- If token_type is "reset", just reset password (no status change)

### POST /auth/users/{user_id}/resend-invite
**Purpose:** Resend invitation email to user with "invited" status
**Token Generated:** New invitation token

## Error Handling

### Status Update Failures
- If user status update fails during password reset, it's logged but doesn't fail the password reset
- User can still login, but admin may need to manually update status
- Proper error logging for debugging

### Token Validation
- Expired tokens are rejected
- Used tokens are rejected
- Invalid tokens are rejected

## Security Considerations

### Token Security
- Tokens are hashed using SHA-256 before storage
- 24-hour expiration for invitation tokens
- Single-use tokens (marked as used after successful reset)

### User Status Protection
- Only users with "invited" status can be transitioned to "active" via invitation flow
- Existing "active" users are not affected by invitation token usage

## Testing

### Manual Testing Steps
1. Invite a user via `/auth/invite-user`
2. Check user status is "invited" in Firestore
3. Use invitation token in `/auth/reset-password`
4. Verify user status changes to "active"
5. Confirm user can login

### Common Issues
- **User stays "invited"**: Check if password reset service is updating status
- **Token not working**: Verify token hasn't expired or been used
- **Status not updating**: Check Firestore permissions and connection

## Recent Fix (2025-07-16)
**Issue:** Users invited via invitation email remained in "invited" status even after completing password setup.

**Root Cause:** The `PasswordResetService.verify_reset_token()` method was not including the `token_type` field in the returned data, causing the status update logic to fail.

**Fix:** 
1. Added `token_type` to the returned data in `verify_reset_token` method
2. Enhanced the `reset_password` method to detect invitation tokens and automatically update user status from "invited" to "active"
3. Added comprehensive debug logging for troubleshooting

**Files Modified:**
- `rest_api/services/password_reset_service.py` (lines 312, 358-396)

---
**Last Updated:** 2025-07-16 | **Status:** Active | **Tested:** ✅