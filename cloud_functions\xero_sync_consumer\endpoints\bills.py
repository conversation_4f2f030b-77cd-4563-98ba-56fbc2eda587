"""
Bills Endpoint Handler

Handles Bills synchronization from Xero with two-stage processing support.
Extracted from main.py for better modularity.
"""

import logging
from typing import List

from context import SyncContext
from utils.bills_processor import process_bills_sync
from utils.sync_helpers import _create_audit_log_entry

logger = logging.getLogger(__name__)

__all__ = ["sync_bills"]


# Heavy processing trigger function moved to utils/sync_helpers.py for shared access

async def sync_bills(ctx: SyncContext, requested_endpoints: List[str]) -> int:
    """
    Handle Bills synchronization with two-stage processing support.
    
    Args:
        ctx: Sync context containing all dependencies
        requested_endpoints: List of requested endpoints for sync
        
    Returns:
        Number of bills processed
        
    Raises:
        Exception: If bills sync fails
    """
    if "Bills" not in requested_endpoints and requested_endpoints:
        return 0
    
    try:
        logger.info(f"Starting Bills sync for entity_id: {ctx.entity_id}")
        
        # Check if we should use two-stage processing (metadata + heavy processing)
        use_two_stage_processing = ctx.use_two_stage_processing()
        logger.debug(f"Two-stage processing enabled: {use_two_stage_processing}")
        
        saved_bills_count = 0
        
        if use_two_stage_processing:
            # Stage 1: Metadata-only processing (fast, stays in Cloud Function)
            saved_bills_count, ctx.processed_prepayments_count = await process_bills_sync(
                db=ctx.db,
                xero_client=ctx.xero_client,
                entity_id=ctx.entity_id,
                client_id=ctx.client_id,
                entity_settings=ctx.entity_settings,
                message_data=ctx.message_data,
                force_full_sync_endpoints=ctx.force_full_sync_endpoints,
                sync_job_id=ctx.sync_job_id,
                processed_prepayments_count=ctx.processed_prepayments_count,
                metadata_only=True
            )
            
            # Stage 2: Heavy processing trigger moved to main.py after all endpoints complete
            # This ensures ManualJournals and other endpoints finish batch commits before Stage 2 starts
            logger.info(f"Bills sync completed, Stage 2 will be triggered after all endpoints finish")
        else:
            # Legacy mode: Full processing in Cloud Function (for backward compatibility)
            saved_bills_count, ctx.processed_prepayments_count = await process_bills_sync(
                db=ctx.db,
                xero_client=ctx.xero_client,
                entity_id=ctx.entity_id,
                client_id=ctx.client_id,
                entity_settings=ctx.entity_settings,
                message_data=ctx.message_data,
                force_full_sync_endpoints=ctx.force_full_sync_endpoints,
                sync_job_id=ctx.sync_job_id,
                processed_prepayments_count=ctx.processed_prepayments_count,
                metadata_only=False
            )
        
        logger.info(f"Bills sync completed for entity_id: {ctx.entity_id}, saved_bills_count: {saved_bills_count}")
        return saved_bills_count
        
    except Exception as e:
        logger.error(f"Bills sync failed for entity_id: {ctx.entity_id}: {e}")
        await _create_audit_log_entry(
            ctx.db, "SYNC", "BILLS_SYNC_FAILURE", ctx.client_id, ctx.entity_id, "FAILURE",
            {"error": str(e), "syncJobId": ctx.sync_job_id}
        )
        raise