import React from 'react';
import { Badge } from '@/components/ui/badge';
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  AlertTriangle, 
  Unlink, 
  <PERSON>ader2,
  Wif<PERSON>,
  WifiOff,
  Clock,
  AlertCircle
} from 'lucide-react';
import type { EntitySummary as TypesEntitySummary, SyncStatus as TypesSyncStatus, EntityStatus } from '@/types/entity.types';
import type { EntitySummary as ApiEntitySummary } from '@/lib/api';

// Type for API sync status (from API EntitySummary)
type ApiSyncStatus = {
  is_syncing: boolean;
  current_step: string;
  progress_percentage: number;
  estimated_remaining?: string;
  user_message: string;
  last_sync_completed?: string;
  sync_duration_warning?: string;
};

// Union type for both sync status formats
type SyncStatusUnion = TypesSyncStatus | ApiSyncStatus | null;

interface StatusInfo {
  badgeClass: string;
  icon: React.ReactNode;
  text: string;
  tooltip?: string;
  isIconOnly?: boolean;
}

// Union type to handle both API and types EntitySummary formats
type EntitySummaryUnion = TypesEntitySummary | ApiEntitySummary;

interface EntityStatusBadgeProps {
  entity: EntitySummaryUnion;
  syncStatus?: SyncStatusUnion;
  className?: string;
  showTooltip?: boolean;
}

export function getEntityStatusInfo(
  entity: EntitySummaryUnion,
  syncStatus?: SyncStatusUnion
): StatusInfo {
  const connectionStatus = entity.connection_status;
  const tooltip = entity.error_message;
  // Handle both API and types EntitySummary formats
  const resolvedSyncStatus = syncStatus || ('sync_status' in entity ? entity.sync_status : null);
  
  // Helper function to build tooltip with sync info
  const buildTooltipWithSync = (baseTooltip: string) => {
    let fullTooltip = baseTooltip;
    
    // Add last sync info if available
    if (entity.last_sync) {
      const lastSyncDate = new Date(entity.last_sync).toLocaleString();
      fullTooltip += `. Last sync: ${lastSyncDate}`;
    } else {
      fullTooltip += '. Never synced';
    }
    
    // Add sync warnings if present (API format only)
    if (resolvedSyncStatus && 'sync_duration_warning' in resolvedSyncStatus && resolvedSyncStatus.sync_duration_warning && resolvedSyncStatus.is_syncing) {
      fullTooltip += `. Warning: ${resolvedSyncStatus.sync_duration_warning}`;
    }
    
    return fullTooltip;
  };
  
  // Priority order: Connection errors > Sync status > Connection status
  
  // If connection has issues, show that first
  if (connectionStatus === 'error') {
    return { 
      badgeClass: 'bg-red-100 text-red-700 border border-red-200', 
      icon: <AlertTriangle className="h-3 w-3" />, 
      text: 'Connection Error', 
      tooltip: buildTooltipWithSync(tooltip || 'Connection error occurred')
    };
  }
  
  if (connectionStatus === 'disconnected') {
    return { 
      badgeClass: 'bg-gray-100 text-gray-500 border border-gray-200', 
      icon: <Unlink className="h-3 w-3" />, 
      text: 'Disconnected', 
      tooltip: buildTooltipWithSync(tooltip || 'Entity is disconnected')
    };
  }
  
  // If connected, show sync status
  if (connectionStatus === 'active' && resolvedSyncStatus) {
    if (resolvedSyncStatus.is_syncing) {
      const userMessage = 'user_message' in resolvedSyncStatus ? resolvedSyncStatus.user_message : 'Data synchronization in progress';
      return { 
        badgeClass: 'bg-blue-100 text-blue-700 border border-blue-200', 
        icon: <Loader2 className="h-3 w-3 animate-spin" />, 
        text: 'Syncing', 
        tooltip: buildTooltipWithSync(userMessage || 'Data synchronization in progress')
      };
    }
    
    if ('current_step' in resolvedSyncStatus && resolvedSyncStatus.current_step === 'error') {
      const userMessage = 'user_message' in resolvedSyncStatus ? resolvedSyncStatus.user_message : 'Data synchronization failed';
      return { 
        badgeClass: 'bg-red-100 text-red-700 border border-red-200', 
        icon: <AlertTriangle className="h-3 w-3" />, 
        text: 'Sync Error', 
        tooltip: buildTooltipWithSync(userMessage || 'Data synchronization failed')
      };
    }
    
    if ('current_step' in resolvedSyncStatus && resolvedSyncStatus.current_step === 'completed') {
      const userMessage = 'user_message' in resolvedSyncStatus ? resolvedSyncStatus.user_message : 'Connected and data synchronized';
      return {
        badgeClass: '', // No badge styling - will be handled differently
        icon: <CheckCircle className="h-4 w-4 text-green-600" />,
        text: '', // No text - just icon
        tooltip: buildTooltipWithSync(userMessage || 'Connected and data synchronized'),
        isIconOnly: true // Flag to indicate this should be rendered as icon only
      };
    }
  }
  
  // Handle specific status values for backward compatibility
  if (connectionStatus === 'syncing') {
    return { 
      badgeClass: 'bg-blue-100 text-blue-700 border border-blue-200', 
      icon: <Loader2 className="h-3 w-3 animate-spin" />, 
      text: 'Syncing', 
      tooltip: buildTooltipWithSync('Data synchronization in progress')
    };
  }
  
  if (connectionStatus === 'pending') {
    return { 
      badgeClass: 'bg-yellow-50 text-yellow-800 border border-yellow-300', 
      icon: <Clock className="h-3 w-3" />, 
      text: 'Pending', 
      tooltip: buildTooltipWithSync('Connection pending')
    };
  }
  
  if (connectionStatus === 'inactive') {
    return { 
      badgeClass: 'bg-gray-100 text-gray-500 border border-gray-200', 
      icon: <WifiOff className="h-3 w-3" />, 
      text: 'Inactive', 
      tooltip: buildTooltipWithSync('Entity is inactive')
    };
  }
  
  // Default for active connection without sync info - show minimal indicator
  if (connectionStatus === 'active') {
    return {
      badgeClass: '', // No badge styling - will be handled differently
      icon: <CheckCircle className="h-4 w-4 text-green-600" />,
      text: '', // No text - just icon
      tooltip: buildTooltipWithSync('Connected to accounting system'),
      isIconOnly: true // Flag to indicate this should be rendered as icon only
    };
  }
  
  // Fallback
  return { 
    badgeClass: 'bg-gray-100 text-gray-700 border border-gray-200', 
    icon: <AlertCircle className="h-3 w-3" />, 
    text: 'Unknown', 
    tooltip: buildTooltipWithSync('Status unknown') 
  };
}

export function EntityStatusBadge({
  entity,
  syncStatus,
  className = '',
  showTooltip = true
}: EntityStatusBadgeProps) {
  const statusInfo = getEntityStatusInfo(entity, syncStatus);

  // Handle icon-only display for "Ready" status
  if (statusInfo.isIconOnly) {
    const iconElement = (
      <span className={`inline-flex items-center ${className}`}>
        {statusInfo.icon}
      </span>
    );

    if (!showTooltip || !statusInfo.tooltip) {
      return iconElement;
    }

    return (
      <TooltipProvider delayDuration={300}>
        <Tooltip>
          <TooltipTrigger asChild>
            {iconElement}
          </TooltipTrigger>
          <TooltipContent>
            <p className="max-w-xs">{statusInfo.tooltip}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // Regular badge display for other statuses
  const badgeElement = (
    <Badge
      variant="outline"
      className={`flex items-center gap-1 ${statusInfo.badgeClass} ${className}`}
    >
      {statusInfo.icon}
      {statusInfo.text}
    </Badge>
  );

  if (!showTooltip || !statusInfo.tooltip) {
    return badgeElement;
  }

  return (
    <TooltipProvider delayDuration={300}>
      <Tooltip>
        <TooltipTrigger asChild>
          <span>{badgeElement}</span>
        </TooltipTrigger>
        <TooltipContent>
          <p className="max-w-xs">{statusInfo.tooltip}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}