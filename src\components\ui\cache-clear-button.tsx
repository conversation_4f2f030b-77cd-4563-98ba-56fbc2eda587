import { useState } from 'react';
import { But<PERSON> } from './button';
import { Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'lucide-react';
import { cacheManager } from '../../lib/cache-manager';
import { toast } from 'sonner';

interface CacheClearButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  showIcon?: boolean;
  className?: string;
  onClear?: () => void;
  type?: 'cache' | 'cookies' | 'both';
}

export function CacheClearButton({
  variant = 'outline',
  size = 'default',
  showIcon = true,
  className = '',
  onClear,
  type = 'cache'
}: CacheClearButtonProps) {
  const [isClearing, setIsClearing] = useState(false);

  const handleClear = async () => {
    setIsClearing(true);
    
    try {
      if (type === 'cookies' || type === 'both') {
        cacheManager.clearCookies();
      }
      
      if (type === 'cache' || type === 'both') {
        cacheManager.clearAllCaches();
      }
      
      // Show success message
      const message = type === 'cookies' ? 'Cookies cleared successfully' : 
                     type === 'both' ? 'Cache and cookies cleared successfully' :
                     'Cache cleared successfully';
      
      const description = type === 'cookies' ? 'All cookies have been removed.' :
                         type === 'both' ? 'All cache data and cookies have been removed.' :
                         'All cache data has been removed. The application will work with fresh data.';
      
      toast.success(message, {
        description,
        duration: 3000
      });
      
      // Call optional callback
      onClear?.();
      
      // Force page reload to ensure fresh state
      setTimeout(() => {
        window.location.reload();
      }, 1000);
      
    } catch (error) {
      console.error('Failed to clear:', error);
      toast.error('Error clearing', {
        description: 'Try refreshing the page manually.',
        duration: 5000
      });
    } finally {
      setIsClearing(false);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleClear}
      disabled={isClearing}
      className={className}
    >
      {isClearing ? (
        <>
          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
          Clearing...
        </>
      ) : (
        <>
          {showIcon && (type === 'cookies' ? <Cookie className="h-4 w-4 mr-2" /> : <Trash2 className="h-4 w-4 mr-2" />)}
          {type === 'cookies' ? 'Clear Cookies' : 
           type === 'both' ? 'Clear All' : 
           'Clear Cache'}
        </>
      )}
    </Button>
  );
} 