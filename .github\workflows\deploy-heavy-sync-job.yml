name: Deploy Heavy Sync Job to Cloud Run

on:
  workflow_dispatch:

jobs:
  deploy-heavy-sync-job:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          project_id: ${{ vars.GCP_PROJECT_ID }}

      - name: Configure Docker authentication
        run: |
          gcloud auth configure-docker europe-west2-docker.pkg.dev

      - name: Generate .env file for Docker image
        run: |
          echo "FIRESTORE_PROJECT_ID=${{ vars.GCP_PROJECT_ID }}" > jobs/.env
          echo "GCP_PROJECT_ID=${{ vars.GCP_PROJECT_ID }}" >> jobs/.env
          echo "OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}" >> jobs/.env
          echo "MISTRAL_API_KEY=${{ secrets.MISTRAL_API_KEY }}" >> jobs/.env
          echo "TOKEN_ENCRYPTION_KEY=${{ secrets.TOKEN_ENCRYPTION_KEY }}" >> jobs/.env
          echo "XERO_CLIENT_ID=${{ vars.XERO_CLIENT_ID }}" >> jobs/.env
          echo "XERO_CLIENT_SECRET=${{ secrets.XERO_CLIENT_SECRET }}" >> jobs/.env
          echo "GCS_BUCKET_NAME=${{ vars.GCS_BUCKET_NAME }}" >> jobs/.env
          echo "SENDGRID_API_KEY=${{ secrets.SENDGRID_API_KEY }}" >> jobs/.env

      - name: Build and Push Docker image for heavy_sync_job
        run: |
          docker build -f jobs/Dockerfile.heavy_sync -t europe-west2-docker.pkg.dev/${{ vars.GCP_PROJECT_ID }}/github-deploy/heavy-sync-job:$GITHUB_SHA .
          docker push europe-west2-docker.pkg.dev/${{ vars.GCP_PROJECT_ID }}/github-deploy/heavy-sync-job:$GITHUB_SHA

      - name: Generate env file for heavy_sync_job (Cloud Run Job)
        run: |
          echo '{' > .env.deploy.json
          echo '  "FIRESTORE_PROJECT_ID": "${{ vars.GCP_PROJECT_ID }}",' >> .env.deploy.json
          echo '  "GCP_PROJECT_ID": "${{ vars.GCP_PROJECT_ID }}",' >> .env.deploy.json
          echo '  "OPENAI_API_KEY": "${{ secrets.OPENAI_API_KEY }}",' >> .env.deploy.json
          echo '  "MISTRAL_API_KEY": "${{ secrets.MISTRAL_API_KEY }}",' >> .env.deploy.json
          echo '  "TOKEN_ENCRYPTION_KEY": "${{ secrets.TOKEN_ENCRYPTION_KEY }}",' >> .env.deploy.json
          echo '  "XERO_CLIENT_ID": "${{ vars.XERO_CLIENT_ID }}",' >> .env.deploy.json
          echo '  "XERO_CLIENT_SECRET": "${{ secrets.XERO_CLIENT_SECRET }}",' >> .env.deploy.json
          echo '  "GCS_BUCKET_NAME": "${{ vars.GCS_BUCKET_NAME }}",' >> .env.deploy.json
          echo '  "SENDGRID_API_KEY": "${{ secrets.SENDGRID_API_KEY }}"' >> .env.deploy.json
          echo '}' >> .env.deploy.json

      - name: Deploy Heavy Sync Job to Cloud Run Jobs
        run: |
          gcloud run jobs deploy heavy-sync-job \
            --image europe-west2-docker.pkg.dev/${{ vars.GCP_PROJECT_ID }}/github-deploy/heavy-sync-job:$GITHUB_SHA \
            --region ${{ vars.GCP_REGION }} \
            --service-account heavy-sync-sa@${{ vars.GCP_PROJECT_ID }}.iam.gserviceaccount.com \
            --env-vars-file .env.deploy.json 
            