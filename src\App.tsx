import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'sonner';
import { Suspense, lazy } from 'react';
import { useParams } from 'react-router-dom';

import { AuthProvider } from './providers/AuthProvider';
import { ProtectedRoute } from './components/auth/ProtectedRoute';
import { LoadingSpinner } from './components/ui/loading-spinner';
import { AppShellWithSidebar } from './components/layout/AppShellWithSidebar';

// Import cache utilities for global access
import './utils/cache-utils';

// Lazy load components for better performance
const ShadcnLoginPage = lazy(() => import('./pages/ShadcnLoginPage'));
const ForgotPasswordPage = lazy(() => import('./pages/ForgotPasswordPage'));
const ResetPasswordPage = lazy(() => import('./pages/ResetPasswordPage'));
const DashboardPage = lazy(() => import('./pages/DashboardPage').then(module => ({ default: module.DashboardPage })));
const BillsAmortizationPage = lazy(() => import('./pages/BillsAmortizationPage').then(module => ({ default: module.BillsAmortizationPage })));
const AccpayAllPage = lazy(() => import('./pages/AccpayAllPage').then(module => ({ default: module.AccpayAllPage })));
const AccountPage = lazy(() => import('./pages/AccountPage').then(module => ({ default: module.AccountPage })));
const BillingPage = lazy(() => import('./pages/BillingPage').then(module => ({ default: module.BillingPage })));
const NotificationsPage = lazy(() => import('./pages/NotificationsPage').then(module => ({ default: module.NotificationsPage })));
const UserManagementPage = lazy(() => import('./pages/UserManagementPage'));
const XeroOrganizationSelector = lazy(() => import('./components/XeroOrganizationSelector').then(module => ({ default: module.XeroOrganizationSelector })));
const TailwindTest = lazy(() => import('./components/TailwindTest').then(module => ({ default: module.TailwindTest })));
const DraggableDialogDemo = lazy(() => import('./pages/DraggableDialogDemo'));
const EntityManagement = lazy(() => import('./pages/EntityManagement').then(module => ({ default: module.EntityManagement })));
const EntitySetupWizard = lazy(() => import('./components/entities/EnhancedEntitySetupWizard').then(module => ({ default: module.EnhancedEntitySetupWizard })));

// Component to handle Xero configure redirect
const XeroConfigureRedirect = () => {
  const { clientId } = useParams<{ clientId: string }>();
  return <Navigate to={`/clients/${clientId}/entities`} replace />;
};

function App() {
  return (
    <BrowserRouter>
      <AuthProvider>
        <Suspense fallback={<LoadingSpinner />}>
          <Routes>
            {/* Public routes */}
            <Route path="/login" element={<ShadcnLoginPage />} />
            <Route path="/forgot-password" element={<ForgotPasswordPage />} />
            <Route path="/reset-password" element={<ResetPasswordPage />} />
            <Route path="/tailwind-test" element={<TailwindTest />} />
            <Route path="/dialog-demo" element={<DraggableDialogDemo />} />

            {/* Protected routes with sidebar */}
            <Route element={<ProtectedRoute><AppShellWithSidebar /></ProtectedRoute>}>
              <Route path="/dashboard" element={<DashboardPage />} />
              
              {/* ACCPAY Routes */}
              <Route path="/accpay" element={<Navigate to="/accpay/all" replace />} />
              <Route path="/accpay/prepayments" element={<BillsAmortizationPage />} />
              <Route path="/accpay/all" element={<AccpayAllPage />} />
              
              {/* User Profile Pages */}
              <Route path="/account" element={<AccountPage />} />
              <Route path="/billing" element={<BillingPage />} />
              <Route path="/notifications" element={<NotificationsPage />} />
              
              {/* User Management */}
              <Route 
                path="/settings/users" 
                element={
                  <ProtectedRoute requiredRole="firm_admin">
                    <UserManagementPage />
                  </ProtectedRoute>
                } 
              />
              
              {/* Entity Management */}
              <Route path="/clients/:clientId/entities" element={<EntityManagement />} />
              <Route path="/clients/:clientId/entities/:entityId" element={<EntityManagement />} />
            </Route>

            {/* Legacy redirect for old prepayments route */}
            <Route
              path="/prepayments"
              element={<Navigate to="/accpay/prepayments" replace />}
            />

            {/* Protected routes without sidebar */}
            <Route
              path="/clients/:clientId/xero/select-organization"
              element={
                <ProtectedRoute>
                  <XeroOrganizationSelector />
                </ProtectedRoute>
              }
            />

            <Route
              path="/clients/:clientId/entities/:entityId/setup"
              element={
                <ProtectedRoute>
                  <EntitySetupWizard />
                </ProtectedRoute>
              }
            />

            {/* Xero Configure Redirect - handles OAuth callback redirects */}
            <Route
              path="/clients/:clientId/xero/configure"
              element={
                <ProtectedRoute>
                  <XeroConfigureRedirect />
                </ProtectedRoute>
              }
            />

            {/* Redirect root to dashboard */}
            <Route path="/" element={<Navigate to="/dashboard" replace />} />

            {/* Catch all - redirect to dashboard */}
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </Suspense>

        <Toaster position="top-right" />
      </AuthProvider>
    </BrowserRouter>
  );
}

export default App;
