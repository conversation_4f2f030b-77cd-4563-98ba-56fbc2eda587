import logging
from datetime import datetime, timezone, date
from typing import Dict, List, Any, Optional, Tuple
from calendar import monthrange

logger = logging.getLogger(__name__)

class AmortizationService:
    """
    Service for calculating amortization schedules using the hybrid approach:
    - Equal monthly distribution for amounts below materiality threshold
    - Day-based proportional distribution for amounts above materiality threshold
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def validate_period(self, start_date: date, end_date: date) -> Tuple[bool, str]:
        """
        Validate if the period qualifies for prepayment amortization.
        
        Returns:
            Tuple of (is_valid, error_message)
        """
        if start_date >= end_date:
            return False, "Start date must be before end date"
        
        num_days_in_period = (end_date - start_date).days + 1
        if num_days_in_period <= 32:
            return False, f"Period length ({num_days_in_period} days) is <= 32 days. Does not qualify as prepayment."
        
        return True, ""
    
    def calculate_preview(
        self, 
        amount: float, 
        start_date: date, 
        end_date: date, 
        entity_settings: Dict[str, Any],
        force_calculation_method: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Calculate amortization schedule preview without saving to database.
        
        Args:
            amount: Total amount to amortize
            start_date: Service period start date
            end_date: Service period end date
            entity_settings: Entity configuration including materiality threshold
            force_calculation_method: Optional override for calculation method ('day_based' or 'equal_monthly')
            
        Returns:
            Dictionary containing calculation results and monthly entries
        """
        # Validate period
        is_valid, error_msg = self.validate_period(start_date, end_date)
        if not is_valid:
            raise ValueError(error_msg)
        
        # Get materiality threshold from entity settings (default €1000)
        materiality_threshold = entity_settings.get("amortization_materiality_threshold", 1000.0)
        
        # Determine amortization method
        method_override_applied = False
        calculation_method_requested = force_calculation_method or "auto"
        
        if force_calculation_method and force_calculation_method != "auto":
            # Validate forced method
            if force_calculation_method not in ["day_based", "equal_monthly"]:
                raise ValueError(f"Invalid calculation method: {force_calculation_method}. Must be 'auto', 'day_based', or 'equal_monthly'")
            
            calculation_method = force_calculation_method
            use_day_based = (calculation_method == "day_based")
            method_override_applied = True
            self.logger.info(f"Using forced calculation method: {calculation_method}")
        else:
            # Use materiality threshold decision (auto mode)
            use_day_based = amount >= materiality_threshold
            calculation_method = "day_based" if use_day_based else "equal_monthly"
            method_override_applied = False
            self.logger.info(f"Using auto threshold-based method: {calculation_method}")
        
        self.logger.info(
            f"Calculating schedule: Amount €{amount}, "
            f"Threshold €{materiality_threshold}, Method: {calculation_method}"
        )
        
        if use_day_based:
            # Check entity setting for 50% rule (default True for backward compatibility)
            apply_50_percent_rule = entity_settings.get("amortization_apply_50_percent_rule", True)
            self.logger.info(f"50% rule setting: {apply_50_percent_rule} (type: {type(apply_50_percent_rule)})")
            monthly_entries = self._calculate_day_based(amount, start_date, end_date, apply_50_percent_rule)
        else:
            monthly_entries = self._calculate_equal_monthly(amount, start_date, end_date)
        
        # Calculate summary statistics
        total_calculated = sum(entry["amount"] for entry in monthly_entries)
        
        return {
            "calculation_method": calculation_method,
            "calculation_method_requested": calculation_method_requested,
            "materiality_threshold": materiality_threshold,
            "method_override_applied": method_override_applied,
            "total_amount": amount,
            "total_calculated": total_calculated,
            "total_months": len(monthly_entries),
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "monthly_entries": monthly_entries
        }
    
    def _calculate_day_based(self, amount: float, start_date: date, end_date: date, apply_50_percent_rule: bool = True) -> List[Dict[str, Any]]:
        """
        Calculate day-based proportional distribution with optional 50% rule.
        
        Args:
            amount: Total amount to amortize
            start_date: Service period start date
            end_date: Service period end date
            apply_50_percent_rule: Whether to apply the 50% rule for month boundary adjustment
            
        Returns:
            List of monthly entry dictionaries
        """
        monthly_entries = []
        running_total = 0
        
        # Conditionally apply 50% rule to determine effective start month
        if apply_50_percent_rule:
            days_in_start_month = monthrange(start_date.year, start_date.month)[1]
            days_from_start = start_date.day - 1  # Days from beginning of month (0-based)
            percentage_through_month = days_from_start / days_in_start_month
            
            if percentage_through_month > 0.5:
                # Push to next month
                if start_date.month == 12:
                    effective_start = datetime(start_date.year + 1, 1, 1).date()
                else:
                    effective_start = datetime(start_date.year, start_date.month + 1, 1).date()
                self.logger.info(f"50% rule - pushing start from {start_date} to {effective_start}")
            else:
                effective_start = datetime(start_date.year, start_date.month, 1).date()
                self.logger.info(f"50% rule - rounding down from {start_date} to {effective_start}")
        else:
            # Use exact start date - no 50% rule adjustment
            effective_start = start_date
            self.logger.info(f"Using exact start date {start_date} (50% rule disabled)")
        
        # Calculate months that overlap with service period
        months = []
        
        # For month iteration, use appropriate start date based on 50% rule setting
        if apply_50_percent_rule:
            temp_date = effective_start  # This is already month-aligned (1st of month)
        else:
            # When 50% rule is disabled, start from the exact start date
            temp_date = effective_start  # This is the exact start date when rule is disabled
        
        while temp_date <= end_date:
            month_start = datetime(temp_date.year, temp_date.month, 1).date()
            month_end = datetime(temp_date.year, temp_date.month, monthrange(temp_date.year, temp_date.month)[1]).date()
            
            # Calculate overlap with actual service period  
            # When 50% rule is disabled, use original start_date for service period calculation
            service_start = effective_start if apply_50_percent_rule else start_date
            overlap_start = max(service_start, month_start)
            overlap_end = min(end_date, month_end)
            
            if overlap_start <= overlap_end:
                overlap_days = (overlap_end - overlap_start).days + 1
                months.append({
                    'year': temp_date.year,
                    'month': temp_date.month,
                    'overlap_days': overlap_days
                })
            
            # Move to next month
            if temp_date.month == 12:
                temp_date = datetime(temp_date.year + 1, 1, 1).date()
            else:
                temp_date = datetime(temp_date.year, temp_date.month + 1, 1).date()
        
        # Calculate proportional amounts
        total_service_days = (end_date - start_date).days + 1
        actual_periods = len(months)
        
        for i, month_info in enumerate(months):
            # Proportional amount based on days in this month vs total service days
            proportion = month_info['overlap_days'] / total_service_days
            entry_amount = round(amount * proportion, 2)
            
            # Adjust last entry to ensure total matches
            if i == actual_periods - 1:
                entry_amount = round(amount - running_total, 2)
            
            # For month_date, use appropriate date based on 50% rule setting
            if apply_50_percent_rule:
                # Use last day of month when 50% rule is applied
                last_day = monthrange(month_info['year'], month_info['month'])[1]
                month_date = date(month_info['year'], month_info['month'], last_day)
            else:
                # When 50% rule is disabled, use actual start date for first month, end date for last month
                if i == 0:  # First month
                    month_date = start_date
                elif i == actual_periods - 1:  # Last month  
                    month_date = end_date
                else:  # Middle months
                    last_day = monthrange(month_info['year'], month_info['month'])[1]
                    month_date = date(month_info['year'], month_info['month'], last_day)
            
            monthly_entries.append({
                "month_date": month_date.isoformat(),
                "amount": entry_amount,
                "days_in_month": month_info['overlap_days'],
                "proportion": round(proportion, 4)
            })
            running_total += entry_amount
        
        return monthly_entries
    
    def _calculate_equal_monthly(self, amount: float, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """
        Calculate equal monthly distribution based on actual service period.
        
        Args:
            amount: Total amount to amortize
            start_date: Service period start date
            end_date: Service period end date
            
        Returns:
            List of monthly entry dictionaries
        """
        monthly_entries = []
        running_total = 0
        
        # Calculate actual number of months in the service period
        months_in_period = []
        temp_date = start_date
        
        while temp_date <= end_date:
            months_in_period.append({
                'year': temp_date.year,
                'month': temp_date.month
            })
            
            # Move to next month
            if temp_date.month == 12:
                temp_date = datetime(temp_date.year + 1, 1, 1).date()
            else:
                temp_date = datetime(temp_date.year, temp_date.month + 1, 1).date()
        
        actual_periods = len(months_in_period)
        monthly_amount = round(amount / actual_periods, 2)
        
        # Create entries for each month in the calculated period
        for i, month_info in enumerate(months_in_period):
            if i == actual_periods - 1:  # Last period
                entry_amount = round(amount - running_total, 2)
            else:
                entry_amount = monthly_amount
            
            # Use the last day of this month
            last_day = monthrange(month_info['year'], month_info['month'])[1]
            month_date = date(month_info['year'], month_info['month'], last_day)
            
            monthly_entries.append({
                "month_date": month_date.isoformat(),
                "amount": entry_amount,
                "days_in_month": None,  # Not applicable for equal monthly
                "proportion": round(1.0 / actual_periods, 4)
            })
            running_total += entry_amount
        
        return monthly_entries