# URL Synchronization System

This document describes the URL synchronization system used across the DRCR frontend application to manage state persistence and navigation.

## Overview

The URL sync system provides automatic synchronization between URL query parameters, Zustand store state, and localStorage defaults. It ensures consistent state management across page refreshes and browser navigation while providing proper validation and user feedback.

## Architecture

### State Precedence

The system follows a clear precedence hierarchy:

1. **URL Parameters** (highest priority) - Current URL query parameters
2. **Zustand Store State** (medium priority) - In-memory React state
3. **localStorage Defaults** (lowest priority) - Persisted fallback values

### Core Hook: `useURLSync`

The `useURLSync` hook is the central component that handles all synchronization logic.

```typescript
import { useURLSync } from '@/hooks/useURLSync';

const { isHydrated, announceMessage, clearAnnouncement } = useURLSync({
  currentState: {
    clientId: selectedClientId,
    entityId: selectedEntityId,
    statusFilters: selectedStatusFilters,
  },
  handlers: {
    onClientChange: setClientId,
    onEntityChange: setEntityId,
    onStatusFiltersChange: setStatusFilters,
  },
  validationLists: {
    clients: availableClients,
    entities: availableEntities,
    statusFilters: validStatusOptions,
  },
  enableDebug: import.meta.env.DEV,
});
```

## Features

### 1. Parameter Name Support

#### Canonical Names (Used in URLs)
- `clientId` - Selected client identifier
- `entityId` - Selected entity identifier  
- `status` - Comma-separated status filters

#### Legacy Support (Backward Compatibility)
- `client_id` → `clientId`
- `entity_id` → `entityId`
- `status_filter` → `status`
- `status_filters` → `status`

### 2. Validation System

#### Validation Lists
The system accepts validation lists to ensure URL parameters contain valid values:

```typescript
interface ValidationLists {
  clients?: Array<{ clientId: string }> | null;
  entities?: Array<{ entityId: string }> | null;
  statusFilters?: string[] | null;
}
```

#### Validation States
- **`undefined`** - Unknown yet, accept all values provisionally
- **`null`** - Known to be empty, reject all values  
- **`[]`** - Empty array, known to be empty, reject all values
- **`[...]`** - Populated array, validate against list

#### Cascading Reset Logic
When `enableCascadingReset: true` (default), invalid selections trigger cascading resets:

- Invalid `clientId` → Reset `entityId` and `statusFilters` to defaults
- Invalid `entityId` → Reset `statusFilters` to defaults
- Invalid `statusFilters` → Keep valid filters, reset invalid ones

### 3. Hydration System

#### isHydrated Flag
The hook provides an `isHydrated` boolean that:
- Starts as `false` during initial load
- Becomes `true` after URL parameters are read and state is synchronized
- Prevents premature API calls before state is properly initialized

```typescript
// Wait for hydration before making API calls
useEffect(() => {
  if (selectedClientId && isHydrated) {
    loadClientData(selectedClientId);
  }
}, [selectedClientId, isHydrated]);
```

#### Hydration Process
1. **Pre-hydration**: URL parameters are parsed
2. **Validation**: Parameters are validated against available options
3. **State Updates**: Zustand store is updated with validated values
4. **Micro-task Completion**: `Promise.resolve().then()` ensures state updates complete
5. **Hydration Complete**: `isHydrated` becomes `true`

### 4. URL Updates

#### Debounced Updates
URL updates are debounced (300ms default) to prevent browser history spam:

```typescript
// Multiple rapid state changes result in a single URL update
setClientId('client1');
setClientId('client2'); 
setClientId('client3');
// → Single URL update after 300ms with client3
```

#### URL Optimization
- Default values are omitted from URLs (e.g., `entityId: 'all'`)
- Empty arrays are omitted (e.g., `statusFilters: []`)
- Only changed parameters are included

### 5. Accessibility

#### Live Region Announcements
The system provides screen reader announcements for validation changes:

```tsx
{announceMessage && (
  <div 
    role="status"
    aria-live="polite" 
    aria-atomic="true" 
    className="sr-only"
    onAnimationEnd={clearAnnouncement}
  >
    {announceMessage}
  </div>
)}
```

#### ARIA Attributes
Components receive proper ARIA attributes:
- `aria-busy` for loading states
- `aria-label` for accessibility labels
- `role="status"` for announcements

## Usage Examples

### Basic Implementation

```typescript
export function MyPage() {
  const {
    selectedClientId,
    selectedEntityId,
    selectedStatusFilters,
    setClientId,
    setEntityId,
    setStatusFilters,
  } = useMyPageStore();

  const { isHydrated, announceMessage, clearAnnouncement } = useURLSync({
    currentState: {
      clientId: selectedClientId,
      entityId: selectedEntityId,
      statusFilters: selectedStatusFilters,
    },
    handlers: {
      onClientChange: setClientId,
      onEntityChange: setEntityId,
      onStatusFiltersChange: setStatusFilters,
    },
    validationLists: {
      clients: availableClients,
      entities: availableEntities,
      statusFilters: ALL_SCHEDULE_STATUSES,
    },
  });

  // Rest of component...
}
```

### Advanced Configuration

```typescript
const { isHydrated } = useURLSync({
  currentState: { /* ... */ },
  handlers: { /* ... */ },
  validationLists: { /* ... */ },
  
  // Custom defaults
  defaults: {
    entityId: 'specific-entity',
    statusFilters: ['pending_configuration', 'proposed'],
  },
  
  // Custom debounce timing
  debounceMs: 500,
  
  // Disable cascading resets
  enableCascadingReset: false,
  
  // Enable debug logging
  enableDebug: import.meta.env.DEV,
  
  // Custom parameter names
  paramNames: {
    clientId: 'client',
    entityId: 'entity',
    status: 'filters',
  },
});
```

### Integration with Selectors

```typescript
// Pass isHydrated to prevent premature rendering
<ClientSelector
  clients={clients}
  selectedClientId={selectedClientId}
  onClientSelect={setClientId}
  isHydrated={isHydrated}
  isLoading={isLoadingClients}
/>

<EntitySelector
  entities={entities}
  selectedEntityId={selectedEntityId}
  onEntitySelect={setEntityId}
  isHydrated={isHydrated}
  isLoading={isLoadingEntities}
/>
```

## Best Practices

### 1. Always Use isHydrated
Wait for hydration before making API calls or showing content:

```typescript
// ✅ Good
useEffect(() => {
  if (selectedClientId && isHydrated) {
    fetchData(selectedClientId);
  }
}, [selectedClientId, isHydrated]);

// ❌ Bad - may cause race conditions
useEffect(() => {
  if (selectedClientId) {
    fetchData(selectedClientId);
  }
}, [selectedClientId]);
```

### 2. Provide Validation Lists
Always provide validation lists when available to ensure data integrity:

```typescript
// ✅ Good - validates against known options
validationLists: {
  clients: availableClients,
  entities: availableEntities,
  statusFilters: ALL_SCHEDULE_STATUSES,
}

// ⚠️ Okay - accepts all values provisionally
validationLists: {
  clients: undefined, // Still loading
  entities: undefined,
  statusFilters: ALL_SCHEDULE_STATUSES,
}
```

### 3. Handle Announcements
Always render the announcement system for accessibility:

```tsx
{announceMessage && (
  <div 
    role="status"
    aria-live="polite" 
    aria-atomic="true" 
    className="sr-only"
    onAnimationEnd={clearAnnouncement}
  >
    {announceMessage}
  </div>
)}
```

### 4. Debug in Development
Enable debug logging in development to troubleshoot issues:

```typescript
enableDebug: import.meta.env.DEV
```

## Error Handling

### Common Issues

1. **Race Conditions**: Always wait for `isHydrated` before making API calls
2. **Invalid Parameters**: Provide proper validation lists to catch invalid values
3. **State Inconsistency**: Ensure Zustand store and URL sync use the same parameter names
4. **Performance**: Use debouncing to prevent excessive URL updates

### Debugging

Enable debug logging to see detailed synchronization steps:

```typescript
// Enable in development
enableDebug: import.meta.env.DEV

// Check browser console for detailed logs:
// [useURLSync] Starting hydration from URL: ?clientId=test
// [useURLSync] Parsed URL params: { clientId: 'test', entityId: null, ... }
// [useURLSync] Validated params: { clientId: 'test', hasResets: false, ... }
```

## Migration Guide

### From Manual URL Management

```typescript
// ❌ Old way - manual URL parameter handling
const [searchParams, setSearchParams] = useSearchParams();
const clientId = searchParams.get('clientId');

useEffect(() => {
  if (clientId) {
    setSelectedClientId(clientId);
  }
}, [clientId]);

useEffect(() => {
  if (selectedClientId) {
    setSearchParams(prev => {
      prev.set('clientId', selectedClientId);
      return prev;
    });
  }
}, [selectedClientId]);

// ✅ New way - automatic synchronization
const { isHydrated } = useURLSync({
  currentState: { clientId: selectedClientId, /* ... */ },
  handlers: { onClientChange: setSelectedClientId, /* ... */ },
  validationLists: { clients: availableClients, /* ... */ },
});
```

### From localStorage Management

```typescript
// ❌ Old way - manual localStorage
useEffect(() => {
  const saved = localStorage.getItem('selectedClientId');
  if (saved && !selectedClientId) {
    setSelectedClientId(saved);
  }
}, []);

useEffect(() => {
  if (selectedClientId) {
    localStorage.setItem('selectedClientId', selectedClientId);
  }
}, [selectedClientId]);

// ✅ New way - handled by Zustand store persistence
// URL sync reads from store, store handles localStorage
```

## Performance Considerations

- **Debouncing**: URL updates are debounced to prevent excessive browser history entries
- **Validation**: Validation is memoized and only runs when validation lists change
- **URL Building**: URL parameter construction is memoized to prevent unnecessary work
- **State Comparison**: Shallow equality checks prevent unnecessary updates

## Browser Compatibility

The URL sync system uses modern browser APIs:
- `URLSearchParams` for parameter parsing
- `Promise.resolve().then()` for micro-task scheduling
- `setTimeout` for debouncing

Minimum browser requirements:
- Chrome 49+
- Firefox 44+
- Safari 10.1+
- Edge 17+

## Testing

The system includes comprehensive Jest tests covering:
- Hydration behavior
- URL parameter parsing (canonical and legacy)
- Validation and cascading resets
- URL updates and debouncing
- Announcement system
- Edge cases and error conditions

Run tests with:
```bash
npm test -- useURLSync
```