"""
Comments Routes - FastAPI routes for comment system
Following DRCR routing patterns with client-scoped security integration
"""
from fastapi import APIRouter, Depends, HTTPException, Path, Query, Body, status
from typing import List, Optional, Dict, Any
import logging

from ..core.firebase_auth import get_current_user, AuthUser
from ..dependencies import get_db
from ..services.comment_service import CommentService
from ..schemas.comment_schemas import (
    CommentCreate, CommentUpdate, CommentOut, CommentsListResponse,
    CommentFilters, CommentStats, CommentErrorResponse, ParentType
)

logger = logging.getLogger(__name__)

router = APIRouter(tags=["Comments"])


# Dependency to get comment service instance
def get_comment_service(db = Depends(get_db)) -> CommentService:
    """Dependency to get comment service instance"""
    return CommentService(db)


@router.post("/", response_model=CommentOut, status_code=status.HTTP_201_CREATED)
async def create_comment(
    comment_data: CommentCreate = Body(...),
    current_user: AuthUser = Depends(get_current_user),
    comment_service: CommentService = Depends(get_comment_service)
):
    """
    Create a new comment on a record
    
    This endpoint allows users to create comments on any supported record type.
    The user must have access to the parent record through client permissions.
    
    - **parent_type**: Type of record being commented on (schedule, transaction, etc.)
    - **parent_id**: ID of the record being commented on
    - **text**: Comment text (max 5000 characters)
    - **mention_uids**: Optional array of user UIDs to mention (max 20)
    
    **Security**: Client-scoped access validation ensures users can only comment
    on records they have permission to view.
    """
    try:
        return await comment_service.create_comment(comment_data, current_user)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error creating comment: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while creating comment"
        )


@router.get("/{parent_type}/{parent_id}", response_model=CommentsListResponse)
async def get_comments_for_record(
    parent_type: ParentType = Path(..., description="Type of parent record"),
    parent_id: str = Path(..., description="ID of parent record"),
    page: int = Query(1, ge=1, description="Page number (1-based)"),
    limit: int = Query(25, ge=1, le=100, description="Items per page (max 100)"),
    show_deleted: bool = Query(False, description="Include soft-deleted comments"),
    created_by: Optional[str] = Query(None, description="Filter by author UID"),
    mentions_user: Optional[str] = Query(None, description="Filter comments mentioning user"),
    search_text: Optional[str] = Query(None, description="Search in comment text"),
    current_user: AuthUser = Depends(get_current_user),
    comment_service: CommentService = Depends(get_comment_service)
):
    """
    Get comments for a specific record with pagination and filtering
    
    This endpoint retrieves all comments for a given parent record with support
    for pagination and various filtering options.
    
    **Path Parameters:**
    - **parent_type**: The type of record (schedule, transaction, invoice, etc.)
    - **parent_id**: The unique identifier of the parent record
    
    **Query Parameters:**
    - **page**: Page number for pagination (default: 1)
    - **limit**: Number of comments per page (default: 25, max: 100)
    - **show_deleted**: Include soft-deleted comments (default: false)
    - **created_by**: Filter by specific author UID
    - **mentions_user**: Filter comments that mention a specific user
    - **search_text**: Search within comment text content
    
    **Security**: Users can only view comments on records they have access to
    through their client permissions.
    """
    try:
        filters = CommentFilters(
            show_deleted=show_deleted,
            created_by=created_by,
            mentions_user=mentions_user,
            search_text=search_text
        )
        
        return await comment_service.get_comments_for_record(
            parent_type=parent_type,
            parent_id=parent_id,
            current_user=current_user,
            filters=filters,
            page=page,
            limit=limit
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting comments: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while retrieving comments"
        )


@router.put("/{comment_id}", response_model=CommentOut)
async def update_comment(
    comment_id: str = Path(..., description="ID of comment to update"),
    update_data: CommentUpdate = Body(...),
    current_user: AuthUser = Depends(get_current_user),
    comment_service: CommentService = Depends(get_comment_service)
):
    """
    Update an existing comment
    
    This endpoint allows users to edit their own comments. Only the comment author
    can update their comments.
    
    **Path Parameters:**
    - **comment_id**: The unique identifier of the comment to update
    
    **Request Body:**
    - **text**: Updated comment text (max 5000 characters)
    - **mention_uids**: Updated array of mentioned user UIDs (max 20)
    
    **Security**: 
    - Only the comment author can update their comments
    - User must still have access to the parent record
    - Mentioned users must have access to the same client
    
    **Note**: If the text is identical to the current version, a 409 Conflict
    error will be returned.
    """
    try:
        return await comment_service.update_comment(comment_id, update_data, current_user)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error updating comment: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while updating comment"
        )


@router.delete("/{comment_id}", status_code=status.HTTP_200_OK)
async def delete_comment(
    comment_id: str = Path(..., description="ID of comment to delete"),
    current_user: AuthUser = Depends(get_current_user),
    comment_service: CommentService = Depends(get_comment_service)
):
    """
    Soft delete a comment
    
    This endpoint allows users to delete their own comments. Comments are soft
    deleted (marked as deleted) rather than permanently removed to maintain
    audit trails.
    
    **Path Parameters:**
    - **comment_id**: The unique identifier of the comment to delete
    
    **Security**: 
    - Only the comment author can delete their comments
    - User must still have access to the parent record
    
    **Note**: This is a soft delete operation. The comment data is retained
    but marked as deleted and hidden from normal queries.
    """
    try:
        return await comment_service.delete_comment(comment_id, current_user)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error deleting comment: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while deleting comment"
        )


@router.get("/{comment_id}", response_model=CommentOut)
async def get_comment_by_id(
    comment_id: str = Path(..., description="ID of comment to retrieve"),
    current_user: AuthUser = Depends(get_current_user),
    comment_service: CommentService = Depends(get_comment_service)
):
    """
    Get a specific comment by ID
    
    This endpoint retrieves a single comment by its unique identifier.
    Users can only access comments they have permission to view based on
    client access rules.
    
    **Path Parameters:**
    - **comment_id**: The unique identifier of the comment
    
    **Security**: Users can only view comments on records they have access to
    through their client permissions.
    """
    try:
        # Get the comment
        doc_ref = comment_service.db.collection("COMMENTS").document(comment_id)
        doc = await doc_ref.get()
        
        if not doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Comment not found"
            )
        
        comment_data = doc.to_dict()
        
        # Validate user has access to the parent record
        parent_type = ParentType(comment_data.get("parent_type"))
        parent_id = comment_data.get("parent_id")
        
        await comment_service._validate_parent_record_access(
            parent_type, parent_id, current_user
        )
        
        # Format and return the comment
        return await comment_service._format_comment_response(doc, current_user)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting comment: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while retrieving comment"
        )


@router.get("/stats/{parent_type}/{parent_id}", response_model=CommentStats)
async def get_comment_stats(
    parent_type: ParentType = Path(..., description="Type of parent record"),
    parent_id: str = Path(..., description="ID of parent record"),
    current_user: AuthUser = Depends(get_current_user),
    comment_service: CommentService = Depends(get_comment_service)
):
    """
    Get comment statistics for a record
    
    This endpoint provides analytics and statistics about comment activity
    for a specific record.
    
    **Path Parameters:**
    - **parent_type**: The type of record (schedule, transaction, etc.)
    - **parent_id**: The unique identifier of the parent record
    
    **Returns:**
    - Total comment count
    - Comments created today/this week
    - Top commenters for this record
    
    **Security**: Users can only view stats for records they have access to
    through their client permissions.
    """
    try:
        # Validate user has access to the parent record
        await comment_service._validate_parent_record_access(
            parent_type, parent_id, current_user
        )
        
        # Get basic comment counts
        base_query = comment_service.db.collection("COMMENTS").where(
            filter=comment_service.db.collection._firestore.FieldFilter("parent_type", "==", parent_type.value)
        ).where(
            filter=comment_service.db.collection._firestore.FieldFilter("parent_id", "==", parent_id)
        ).where(
            filter=comment_service.db.collection._firestore.FieldFilter("deleted", "==", False)
        )
        
        # Get total count
        total_docs = await base_query.count().get()
        total_comments = total_docs[0][0].value
        
        # For now, return basic stats - can be enhanced later
        return CommentStats(
            total_comments=total_comments,
            comments_today=0,  # TODO: Implement time-based filtering
            comments_this_week=0,  # TODO: Implement time-based filtering
            top_commenters=[],  # TODO: Implement aggregation
            most_commented_records=[]  # TODO: Implement cross-record stats
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error getting comment stats: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while retrieving comment statistics"
        )