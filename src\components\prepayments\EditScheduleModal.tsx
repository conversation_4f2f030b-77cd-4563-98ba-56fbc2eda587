import React, { useState, useEffect, useMemo } from 'react';
import { useToast } from '@/hooks/useToast';
import type { ScheduleData, MonthlyEntry } from '../../types/schedule.types';

// --- Shadcn/UI & Lucide Imports ---
import { Button } from '@/components/ui/button';
import {
    DraggableDialog,
    DraggableDialogContent,
    DraggableDialogDescription,
    DraggableDialogFooter,
    DraggableDialogHeader,
    DraggableDialogTitle,
    DraggableDialogClose,
} from '@/components/ui/draggable-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { StatusBadge } from '@/components/ui/status-badge';
import {
    Loader2,
    Save,
    AlertCircle,
    Calendar,
    DollarSign,
    Settings,
    CheckCircle,
    XCircle,
    Info,
    Calculator,
} from 'lucide-react';
import { 
    ScheduleStatus, 
    mapBackendScheduleStatus, 
    isEditableStatus, 
    isConfirmableStatus, 
    isSkippableStatus 
} from '@/types/schedule.types';

// --- Type Definitions ---


export interface ScheduleUpdateData {
    amortizationStartDate: string;
    amortizationEndDate: string;
    originalAmount: number;
    amortizationAccountCode: string;
    expenseAccountCode?: string;
    notes?: string;
    monthlyEntries?: MonthlyEntry[];
    // ✨ NEW
    custom_narration?: string;
}

// --- Component Props ---
interface EditScheduleModalProps {
    isOpen: boolean;
    onClose: () => void;
    scheduleData: ScheduleData | null;
    availableAmortizationAccounts: { code: string; name: string }[];
    availableExpenseAccounts: { code: string; name: string }[];
    onSave: (scheduleId: string, updateData: ScheduleUpdateData) => Promise<{
        message: string;
        schedule_id: string;
        status_progression?: {
            from: string;
            to: string;
        };
    } | void>;
    onConfirm: (scheduleId: string) => Promise<void>;
    onSkip: (scheduleId: string, reason: string) => Promise<void>;
    isSaving: boolean;
    saveError: string | null;
    context: 'line_item' | 'invoice_level';
    focusedMonthKey?: string; // Optional month to highlight (e.g., "2025-06")
}

// --- Helper Functions ---
const formatDateForInput = (isoDate: string): string => {
    if (!isoDate) return '';
    try {
        return isoDate.substring(0, 10);
    } catch {
        return '';
    }
};

const calculateEndDate = (startDateIso: string, periods: number): string => {
    try {
        const startDate = new Date(startDateIso);
        const endDate = new Date(startDate.getFullYear(), startDate.getMonth() + periods, startDate.getDate() - 1);
        return endDate.toISOString();
    } catch (e) {
        console.error("Error calculating end date:", e);
        return startDateIso;
    }
};

const generateMonthlyEntries = (startDate: string, periods: number, totalAmount: number): MonthlyEntry[] => {
    const entries: MonthlyEntry[] = [];
    const monthlyAmount = totalAmount / periods;
    const remainder = totalAmount - (monthlyAmount * periods);
    
    for (let i = 0; i < periods; i++) {
        const entryDate = new Date(startDate);
        entryDate.setMonth(entryDate.getMonth() + i);
        
        // Add remainder to the last entry to ensure total matches
        const amount = i === periods - 1 ? monthlyAmount + remainder : monthlyAmount;
        
        entries.push({
            amount: Math.round(amount * 100) / 100, // Round to 2 decimal places
            month_date: entryDate.toISOString(),
            status: 'pending_configuration',
            last_action_by_user_id: null,
            last_action_timestamp: null,
            match_confidence: null,
            posted_journal_id: null,
            posted_journal_line_id: null,
            posting_error: null,
        });
    }
    
    return entries;
};

// --- Edit Schedule Modal Component ---
export function EditScheduleModal({
    isOpen,
    onClose,
    scheduleData,
    availableAmortizationAccounts = [],
    availableExpenseAccounts = [],
    onSave,
    onConfirm,
    onSkip,
    isSaving,
    saveError,
    context,
    focusedMonthKey,
}: EditScheduleModalProps) {
    const showToast = useToast();

    const [formData, setFormData] = useState<{
        amortizationStartDate: string;
        amortizationEndDate: string;
        originalAmount: number;
        numberOfPeriods: number;
        amortizationAccountCode: string;
        expenseAccountCode: string;
        notes: string;
        // ✨ NEW
        custom_narration: string;
        monthlyEntries: MonthlyEntry[];
    }>({
        amortizationStartDate: '',
        amortizationEndDate: '',
        originalAmount: 0,
        numberOfPeriods: 12,
        amortizationAccountCode: '',
        expenseAccountCode: '',
        notes: '',
        // ✨ NEW
        custom_narration: '',
        monthlyEntries: [],
    });

    const [skipReason, setSkipReason] = useState('');
    const [showSkipDialog, setShowSkipDialog] = useState(false);
    const [recalculateEntries, setRecalculateEntries] = useState(false);

    // Initialize form data when schedule data changes
    useEffect(() => {
        console.log('🔄 EditScheduleModal: Initializing form data');
        console.log('📊 Schedule data:', scheduleData);
        console.log('📋 Available amortization accounts:', availableAmortizationAccounts);
        console.log('📋 Available expense accounts:', availableExpenseAccounts);

        if (scheduleData) {
            console.log('✅ Using actual schedule data');
            const formDataToSet = {
                amortizationStartDate: scheduleData.start_date,
                amortizationEndDate: scheduleData.end_date,
                originalAmount: scheduleData.amount,
                numberOfPeriods: scheduleData.number_of_periods,
                amortizationAccountCode: scheduleData.account_code || '',
                expenseAccountCode: scheduleData.expense_account_code || '',
                notes: scheduleData.description || '',
                // ✨ NEW support reading either key
                custom_narration: (scheduleData.custom_narration || (scheduleData as any).narration) || '',
                monthlyEntries: scheduleData.monthly_entries || [],
            };
            console.log('📝 Form data to set:', formDataToSet);
            setFormData(formDataToSet);
        } else {
            console.log('🆕 Creating new schedule form');
            // Reset form for new schedule
            setFormData({
                amortizationStartDate: '',
                amortizationEndDate: '',
                originalAmount: 0,
                numberOfPeriods: 12,
                amortizationAccountCode: availableAmortizationAccounts[0]?.code || '',
                expenseAccountCode: '',
                notes: '',
                // ✨ NEW
                custom_narration: '',
                monthlyEntries: [],
            });
        }
        setRecalculateEntries(false);
    }, [scheduleData, availableAmortizationAccounts, availableExpenseAccounts]);

    // Calculate total from monthly entries
    const calculatedTotal = useMemo(() => {
        return formData.monthlyEntries.reduce((sum, entry) => sum + entry.amount, 0);
    }, [formData.monthlyEntries]);

    // Check if totals match
    const totalsMatch = Math.abs(calculatedTotal - formData.originalAmount) < 0.01;

    // Handle form field changes
    const handleFieldChange = (field: string, value: any) => {
        setFormData(prev => {
            const updated = { ...prev, [field]: value };
            
            // Auto-calculate end date when start date or periods change
            if (field === 'amortizationStartDate' || field === 'numberOfPeriods') {
                if (updated.amortizationStartDate && updated.numberOfPeriods > 0) {
                    updated.amortizationEndDate = calculateEndDate(updated.amortizationStartDate, updated.numberOfPeriods);
                }
            }
            
            // Mark for recalculation if key fields change
            if (['amortizationStartDate', 'numberOfPeriods', 'originalAmount'].includes(field)) {
                setRecalculateEntries(true);
            }
            
            return updated;
        });
    };

    // Handle monthly entry amount change
    const handleMonthlyEntryChange = (index: number, amount: number) => {
        setFormData(prev => ({
            ...prev,
            monthlyEntries: prev.monthlyEntries.map((entry, i) => 
                i === index ? { ...entry, amount } : entry
            )
        }));
    };

    // Recalculate monthly entries
    const handleRecalculateEntries = () => {
        if (formData.amortizationStartDate && formData.numberOfPeriods > 0 && formData.originalAmount > 0) {
            const newEntries = generateMonthlyEntries(
                formData.amortizationStartDate,
                formData.numberOfPeriods,
                formData.originalAmount
            );
            setFormData(prev => ({ ...prev, monthlyEntries: newEntries }));
            setRecalculateEntries(false);
        }
    };

    // Handle save
    const handleSave = async () => {
        if (!scheduleData) return;

        // Validation
        if (!formData.amortizationStartDate || !formData.amortizationEndDate) {
            showToast.showError('Missing Dates', 'Please provide start and end dates.');
            return;
        }

        if (!formData.amortizationAccountCode) {
            showToast.showError('Missing Account', 'Please select an amortization account.');
            return;
        }

        if (!totalsMatch) {
            showToast.showError('Amount Mismatch', 'Monthly entries total must match the original amount.');
            return;
        }

        const updateData: ScheduleUpdateData = {
            amortizationStartDate: formData.amortizationStartDate,
            amortizationEndDate: formData.amortizationEndDate,
            originalAmount: formData.originalAmount,
            amortizationAccountCode: formData.amortizationAccountCode,
            expenseAccountCode: formData.expenseAccountCode || undefined,
            notes: formData.notes || undefined,
            // ✨ include narration if provided
            custom_narration: formData.custom_narration || undefined,
            monthlyEntries: formData.monthlyEntries,
        };

        try {
            const result = await onSave(scheduleData.schedule_id, updateData);

            // Handle automatic status progression
            if (result && result.status_progression) {
                console.log(`Status automatically advanced from ${result.status_progression.from} to ${result.status_progression.to}`);
                // The parent component will handle refreshing the data to show the new status
            }
        } catch (error) {
            console.error('Save failed:', error);
        }
    };

    // Handle confirm
    const handleConfirm = async () => {
        if (!scheduleData) return;
        
        if (!formData.expenseAccountCode) {
            showToast.showError('Missing Expense Account', 'Expense account is required before confirmation.');
            return;
        }
        
        // Save first, then confirm
        await handleSave();
        await onConfirm(scheduleData.schedule_id);
    };

    // Handle skip
    const handleSkipSubmit = async () => {
        if (!scheduleData || !skipReason.trim()) return;
        
        try {
            await onSkip(scheduleData.schedule_id, skipReason);
            setShowSkipDialog(false);
            setSkipReason('');
        } catch (error) {
            console.error('Skip failed:', error);
        }
    };

    const canEdit = scheduleData?.status && isEditableStatus(mapBackendScheduleStatus(scheduleData.status));
    const canConfirm = scheduleData?.status && isConfirmableStatus(mapBackendScheduleStatus(scheduleData.status));
    const canSkip = scheduleData?.status && isSkippableStatus(mapBackendScheduleStatus(scheduleData.status));

    return (
        <DraggableDialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
            <DraggableDialogContent className="flex flex-col p-0 gap-0 max-w-6xl max-h-[90vh]">
                <DraggableDialogHeader className="p-4 border-b flex-shrink-0">
                    <div className="flex items-center justify-between">
                        <div>
                            <DraggableDialogTitle>
                                Edit Amortization Schedule
                                {context === 'line_item' && ' (Line Item Level)'}
                                {focusedMonthKey && (
                                    <span className="text-blue-600">
                                        {' '}- Editing {new Date(focusedMonthKey + '-01').toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                                    </span>
                                )}
                            </DraggableDialogTitle>
                            <DraggableDialogDescription>
                                {scheduleData ? (
                                    <div className="flex items-center gap-2 mt-1">
                                        <span>Schedule ID: {scheduleData.schedule_id}</span>
                                        <StatusBadge status={mapBackendScheduleStatus(scheduleData.status)} />
                                        {scheduleData.detection_method && (
                                            <Badge variant="outline" className="text-xs">
                                                {scheduleData.detection_method === 'llm_only' && '🤖 LLM Detected'}
                                                {scheduleData.detection_method === 'gl_coding' && '📊 GL Coding'}
                                            </Badge>
                                        )}
                                    </div>
                                ) : 'Create a new amortization schedule'}
                            </DraggableDialogDescription>
                        </div>
                    </div>
                </DraggableDialogHeader>

                <div className="flex-grow grid grid-cols-1 lg:grid-cols-2 gap-0 overflow-hidden">
                    {/* Left Column: Schedule Details */}
                    <div className="flex flex-col border-r overflow-hidden">
                        <h3 className="text-sm font-semibold p-3 border-b bg-gray-50 flex-shrink-0 flex items-center">
                            <Settings className="h-4 w-4 mr-2" />
                            Schedule Configuration
                        </h3>
                        <ScrollArea className="flex-grow p-4">
                            <div className="space-y-4">
                                {/* Basic Information */}
                                <div className="space-y-3">
                                    <div className="grid grid-cols-2 gap-3">
                                        <div className="space-y-1">
                                            <Label htmlFor="startDate">Start Date</Label>
                                            <Input
                                                id="startDate"
                                                type="date"
                                                value={formatDateForInput(formData.amortizationStartDate)}
                                                onChange={(e) => handleFieldChange('amortizationStartDate', e.target.value ? e.target.value + 'T00:00:00Z' : '')}
                                                disabled={!canEdit}
                                            />
                                        </div>
                                        <div className="space-y-1">
                                            <Label htmlFor="periods">Number of Periods</Label>
                                            <Input
                                                id="periods"
                                                type="number"
                                                min="1"
                                                max="60"
                                                value={formData.numberOfPeriods || ''}
                                                onChange={(e) => handleFieldChange('numberOfPeriods', parseInt(e.target.value, 10) || 0)}
                                                disabled={!canEdit}
                                            />
                                        </div>
                                    </div>

                                    <div className="space-y-1">
                                        <Label>Calculated End Date</Label>
                                        <Input
                                            value={formatDateForInput(formData.amortizationEndDate)}
                                            disabled
                                            className="bg-muted text-sm"
                                        />
                                    </div>

                                    <div className="space-y-1">
                                        <Label htmlFor="originalAmount">Original Amount</Label>
                                        <Input
                                            id="originalAmount"
                                            type="number"
                                            step="0.01"
                                            min="0"
                                            value={formData.originalAmount || ''}
                                            onChange={(e) => handleFieldChange('originalAmount', parseFloat(e.target.value) || 0)}
                                            disabled={!canEdit}
                                        />
                                    </div>
                                </div>

                                {/* Account Configuration */}
                                <div className="space-y-3 pt-4 border-t">
                                    <h4 className="text-sm font-medium">Account Configuration</h4>
                                    
                                    <div className="space-y-1">
                                        <Label htmlFor="amortizationAccount">Amortization Account (Asset)</Label>
                                        {availableAmortizationAccounts.length === 0 ? (
                                            <div className="flex items-center justify-center py-2">
                                                <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
                                                <span className="ml-2 text-xs text-muted-foreground">Loading accounts...</span>
                                            </div>
                                        ) : (
                                            <Select
                                                value={formData.amortizationAccountCode}
                                                onValueChange={(value) => handleFieldChange('amortizationAccountCode', value)}
                                                disabled={!canEdit}
                                            >
                                                <SelectTrigger id="amortizationAccount">
                                                    <SelectValue placeholder="Select account..." />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {/* Show current value if it's not in the available accounts */}
                                                    {formData.amortizationAccountCode && 
                                                     !availableAmortizationAccounts.some(acc => acc.code === formData.amortizationAccountCode) && (
                                                        <SelectItem value={formData.amortizationAccountCode}>
                                                            {formData.amortizationAccountCode} - (Current)
                                                        </SelectItem>
                                                    )}
                                                    {availableAmortizationAccounts.map(acc => (
                                                        <SelectItem key={acc.code} value={acc.code}>
                                                            {acc.code} - {acc.name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        )}
                                        <p className="text-xs text-muted-foreground">
                                            Current: {formData.amortizationAccountCode || 'None selected'}
                                        </p>
                                    </div>

                                    <div className="space-y-1">
                                        <Label htmlFor="expenseAccount">Expense Account</Label>
                                        {availableExpenseAccounts.length === 0 ? (
                                            <div className="flex items-center justify-center py-2">
                                                <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
                                                <span className="ml-2 text-xs text-muted-foreground">Loading accounts...</span>
                                            </div>
                                        ) : (
                                            <Select
                                                value={formData.expenseAccountCode}
                                                onValueChange={(value) => handleFieldChange('expenseAccountCode', value)}
                                                disabled={!canEdit}
                                            >
                                                <SelectTrigger id="expenseAccount" className={`${!formData.expenseAccountCode && canConfirm ? 'border-red-500' : ''}`}>
                                                    <SelectValue placeholder="Select account..." />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {/* Show current value if it's not in the available accounts */}
                                                    {formData.expenseAccountCode && 
                                                     !availableExpenseAccounts.some(acc => acc.code === formData.expenseAccountCode) && (
                                                        <SelectItem value={formData.expenseAccountCode}>
                                                            {formData.expenseAccountCode} - (Current)
                                                        </SelectItem>
                                                    )}
                                                    {availableExpenseAccounts.map(acc => (
                                                        <SelectItem key={acc.code} value={acc.code}>
                                                            {acc.code} - {acc.name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        )}
                                        <div className="flex justify-between items-center">
                                            <p className="text-xs text-muted-foreground">
                                                Current: {formData.expenseAccountCode || 'None selected'}
                                            </p>
                                            {!formData.expenseAccountCode && canConfirm && (
                                                <p className="text-xs text-red-600">Required for confirmation</p>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                {/* Notes */}
                                <div className="space-y-1 pt-4 border-t">
                                    <Label htmlFor="notes">Notes</Label>
                                    <Textarea
                                        id="notes"
                                        value={formData.notes}
                                        onChange={(e) => handleFieldChange('notes', e.target.value)}
                                        placeholder="Optional notes about this schedule..."
                                        disabled={!canEdit}
                                        rows={3}
                                    />
                                </div>
                                {/* ✨ Custom Narration */}
                                <div className="space-y-1 pt-4 border-t">
                                    <Label htmlFor="customNarration">Narration&nbsp;
                                        <span className="text-muted-foreground text-xs">(max 500 characters)</span>
                                    </Label>
                                    <Textarea
                                        id="customNarration"
                                        value={formData.custom_narration}
                                        onChange={(e) => handleFieldChange('custom_narration', e.target.value.slice(0, 500))}
                                        placeholder="Leave blank to use the default auto-generated narration pattern"
                                        disabled={!canEdit}
                                        rows={3}
                                    />
                                    <p className="text-xs text-muted-foreground text-right">
                                        {formData.custom_narration.length} / 500
                                    </p>
                                </div>

                                {/* Schedule Info */}
                                {scheduleData && (
                                    <div className="space-y-2 pt-4 border-t">
                                        <h4 className="text-sm font-medium">Schedule Information</h4>
                                        <div className="text-xs text-muted-foreground space-y-1">
                                            <div>Transaction ID: {scheduleData.transaction_id}</div>
                                            {scheduleData.line_item_id && <div>Line Item ID: {scheduleData.line_item_id}</div>}
                                            <div>Created: {new Date(scheduleData.created_at).toLocaleString()}</div>
                                            <div>Updated: {new Date(scheduleData.updated_at).toLocaleString()}</div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </ScrollArea>
                    </div>

                    {/* Right Column: Monthly Entries */}
                    <div className="flex flex-col overflow-hidden">
                        <div className="p-3 border-b bg-gray-50 flex-shrink-0">
                            <div className="flex items-center justify-between">
                                <h3 className="text-sm font-semibold flex items-center">
                                    <Calendar className="h-4 w-4 mr-2" />
                                    Monthly Breakdown
                                </h3>
                                <div className="flex items-center gap-2">
                                    {recalculateEntries && (
                                        <Button
                                            size="sm"
                                            variant="outline"
                                            onClick={handleRecalculateEntries}
                                            className="text-xs"
                                        >
                                            <Calculator className="h-3 w-3 mr-1" />
                                            Recalculate
                                        </Button>
                                    )}
                                    <div className={`text-xs px-2 py-1 rounded ${totalsMatch ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>
                                        Total: ${calculatedTotal.toFixed(2)}
                                        {!totalsMatch && ` (Expected: $${formData.originalAmount.toFixed(2)})`}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <ScrollArea className="flex-grow">
                            {formData.monthlyEntries.length > 0 ? (
                                <Table className="text-xs">
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead className="w-24">Month</TableHead>
                                            <TableHead className="w-20">Amount</TableHead>
                                            <TableHead className="w-20">Status</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {formData.monthlyEntries.map((entry, index) => {
                                            // Check if this entry matches the focused month
                                            const entryMonthKey = new Date(entry.month_date).toISOString().substring(0, 7); // YYYY-MM format
                                            const isFocused = focusedMonthKey && entryMonthKey === focusedMonthKey;
                                            
                                            return (
                                                <TableRow 
                                                    key={index} 
                                                    className={isFocused ? 'bg-blue-50 border-blue-200' : ''}
                                                >
                                                    <TableCell className="py-2">
                                                        <div className="flex items-center gap-2">
                                                            {new Date(entry.month_date).toLocaleDateString('en-US', { 
                                                                month: 'short', 
                                                                year: 'numeric' 
                                                            })}
                                                            {isFocused && (
                                                                <Badge variant="outline" className="text-xs bg-blue-100 text-blue-700 border-blue-300">
                                                                    Focused
                                                                </Badge>
                                                            )}
                                                        </div>
                                                    </TableCell>
                                                    <TableCell className="py-2">
                                                        <Input
                                                            type="number"
                                                            step="0.01"
                                                            min="0"
                                                            value={entry.amount}
                                                            onChange={(e) => handleMonthlyEntryChange(index, parseFloat(e.target.value) || 0)}
                                                            className={`h-7 text-xs ${isFocused ? 'border-blue-300 focus:border-blue-500' : ''}`}
                                                            disabled={!canEdit}
                                                        />
                                                    </TableCell>
                                                    <TableCell className="py-2">
                                                        <Badge 
                                                            variant={entry.status === 'posted' ? 'default' : 'outline'} 
                                                            className="text-xs"
                                                        >
                                                            {entry.status.replace('_', ' ')}
                                                        </Badge>
                                                    </TableCell>
                                                </TableRow>
                                            );
                                        })}
                                    </TableBody>
                                </Table>
                            ) : (
                                <div className="p-4 text-center text-muted-foreground">
                                    <Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
                                    <p className="text-sm">No monthly entries yet</p>
                                    <p className="text-xs">Configure the schedule details and click "Recalculate"</p>
                                </div>
                            )}
                        </ScrollArea>
                    </div>
                </div>

                {saveError && (
                    <div className="px-4 pt-2 flex-shrink-0">
                        <Alert variant="destructive">
                            <AlertCircle className="h-4 w-4" />
                            <AlertTitle>Save Failed</AlertTitle>
                            <AlertDescription>{saveError}</AlertDescription>
                        </Alert>
                    </div>
                )}

                {!totalsMatch && formData.monthlyEntries.length > 0 && (
                    <div className="px-4 pt-2 flex-shrink-0">
                        <Alert>
                            <Info className="h-4 w-4" />
                            <AlertTitle>Total Mismatch</AlertTitle>
                            <AlertDescription>
                                Monthly entries total (${calculatedTotal.toFixed(2)}) doesn't match original amount (${formData.originalAmount.toFixed(2)}).
                                Please adjust the amounts or recalculate.
                            </AlertDescription>
                        </Alert>
                    </div>
                )}

                <DraggableDialogFooter className="p-4 border-t flex-shrink-0">
                    <div className="flex items-center justify-between w-full">
                        <div className="flex gap-2">
                            {canSkip && (
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => setShowSkipDialog(true)}
                                    className="text-red-600 border-red-300 hover:bg-red-50"
                                >
                                    <XCircle className="mr-2 h-4 w-4" />
                                    Skip
                                </Button>
                            )}
                        </div>
                        <div className="flex gap-2">
                            <DraggableDialogClose asChild>
                                <Button type="button" variant="outline">
                                    Cancel
                                </Button>
                            </DraggableDialogClose>
                            {canEdit && (
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={handleSave}
                                    disabled={isSaving || !totalsMatch}
                                >
                                    {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                    <Save className="mr-2 h-4 w-4" />
                                    Save Changes
                                </Button>
                            )}
                            {canConfirm && (
                                <Button
                                    type="button"
                                    onClick={handleConfirm}
                                    disabled={isSaving || !formData.expenseAccountCode || !totalsMatch}
                                    className="bg-green-600 hover:bg-green-700"
                                >
                                    {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                    <CheckCircle className="mr-2 h-4 w-4" />
                                    Confirm Schedule
                                </Button>
                            )}
                        </div>
                    </div>
                </DraggableDialogFooter>
            </DraggableDialogContent>

            {/* Skip Dialog */}
            {showSkipDialog && (
                <DraggableDialog open={showSkipDialog} onOpenChange={setShowSkipDialog}>
                    <DraggableDialogContent>
                        <DraggableDialogHeader>
                            <DraggableDialogTitle>Skip Schedule</DraggableDialogTitle>
                            <DraggableDialogDescription>
                                Please provide a reason for skipping this amortization schedule.
                            </DraggableDialogDescription>
                        </DraggableDialogHeader>
                        <div className="p-4">
                            <Textarea
                                value={skipReason}
                                onChange={(e) => setSkipReason(e.target.value)}
                                placeholder="Reason for skipping..."
                                rows={3}
                            />
                        </div>
                        <DraggableDialogFooter>
                            <Button variant="outline" onClick={() => setShowSkipDialog(false)}>
                                Cancel
                            </Button>
                            <Button
                                onClick={handleSkipSubmit}
                                disabled={!skipReason.trim() || isSaving}
                                className="bg-red-600 hover:bg-red-700"
                            >
                                {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                Skip Schedule
                            </Button>
                        </DraggableDialogFooter>
                    </DraggableDialogContent>
                </DraggableDialog>
            )}
        </DraggableDialog>
    );
} 