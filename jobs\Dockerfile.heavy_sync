FROM python:3.11-slim

WORKDIR /app

# Install dependencies
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Copy only necessary directories
COPY rest_api/ ./rest_api/
COPY jobs/ ./jobs/
COPY jobs/.env .env
COPY drcr_shared_logic/ ./drcr_shared_logic/

# If other modules are needed, add COPY for them here

# Entry point for the heavy_sync_job
CMD ["python", "-m", "jobs.heavy_sync_job"] 