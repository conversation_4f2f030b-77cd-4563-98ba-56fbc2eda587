import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Loader2, 
  CheckCircle, 
  ArrowRight, 
  ArrowLeft,
  Building2,
  FileText,
  Settings,
  Zap,
  AlertCircle
} from 'lucide-react';
import { toast } from 'sonner';
import { ApiClient } from '@/lib/api';

const apiClient = new ApiClient();

interface WizardAnalysis {
  entity_id: string;
  entity_name: string;
  organization_info: {
    name: string;
    base_currency: string;
    financial_year_start?: string;
  };
  bills_analysis: {
    bills_with_attachments: number;
    total_bills: number;
    scanning_cost_estimate: string;
    expected_prepayments: string;
    financial_year_start: string;
  };
  accounts_analysis: {
    prepayment_asset_accounts: Array<{
      code: string;
      name: string;
      recommended: boolean;
    }>;
    relevant_expense_accounts: Array<{
      code: string;
      name: string;
    }>;
    suggested_exclusions: Array<{
      code: string;
      name: string;
    }>;
  };
  recommendations: {
    sync_start_date: string;
    sync_frequency: string;
    enable_ai_scanning: boolean;
    selected_asset_accounts: string[];
    excluded_accounts: string[];
    base_currency: string;
  };
}

interface SetupData {
  enable_ai_scanning: boolean;
  sync_settings: {
    sync_start_date: string;
    sync_frequency: string;
    auto_sync_enabled: boolean;
    sync_invoices: boolean;
    sync_bills: boolean;
    sync_payments: boolean;
    sync_bank_transactions: boolean;
    sync_journal_entries: boolean;
    sync_spend_money: boolean;
  };
  account_settings: {
    prepayment_asset_accounts: string[];
    excluded_accounts: string[];
    default_expense_account?: string;
    base_currency: string;
  };
}

export function EntitySetupWizard() {
  const { clientId, entityId } = useParams<{ clientId: string; entityId: string }>();
  const navigate = useNavigate();
  
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [analysis, setAnalysis] = useState<WizardAnalysis | null>(null);
  const [setupData, setSetupData] = useState<SetupData>({
    enable_ai_scanning: false,
    sync_settings: {
      sync_start_date: '',
      sync_frequency: 'daily',
      auto_sync_enabled: true,
      sync_invoices: true,
      sync_bills: true,
      sync_payments: true,
      sync_bank_transactions: true,
      sync_journal_entries: true,
      sync_spend_money: true,
    },
    account_settings: {
      prepayment_asset_accounts: [],
      excluded_accounts: [],
      base_currency: 'GBP',
    }
  });
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (entityId) {
      loadWizardData();
    }
  }, [entityId]);

  const loadWizardData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await apiClient.getEntityWizardAnalysis(entityId!);
      setAnalysis(data);
      
      // Set smart defaults based on analysis
      setSetupData(prev => ({
        ...prev,
        enable_ai_scanning: data.recommendations.enable_ai_scanning,
        sync_settings: {
          ...prev.sync_settings,
          sync_start_date: data.recommendations.sync_start_date,
          sync_frequency: data.recommendations.sync_frequency,
        },
        account_settings: {
          ...prev.account_settings,
          prepayment_asset_accounts: data.recommendations.selected_asset_accounts,
          excluded_accounts: data.recommendations.excluded_accounts,
          base_currency: data.recommendations.base_currency,
        }
      }));
      
    } catch (error: any) {
      console.error('Failed to load wizard data:', error);
      setError('Failed to load entity data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleNext = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleCompleteSetup = async () => {
    try {
      setSubmitting(true);
      
      await apiClient.completeEntitySetup(entityId!, setupData);
      
      toast.success('Entity setup completed successfully!');
      navigate(`/clients/${clientId}/entities`);
      
    } catch (error: any) {
      console.error('Failed to complete setup:', error);
      toast.error('Failed to complete setup. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const updateSetupData = (section: keyof SetupData, data: any) => {
    setSetupData(prev => ({
      ...prev,
      [section]: { ...(prev[section] as any), ...data }
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Analyzing your entity data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-destructive" />
              Setup Error
            </CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => navigate(`/clients/${clientId}/entities`)}>
              Back to Entities
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!analysis) {
    return null;
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold mb-2">Setup {analysis.entity_name}</h1>
        <p className="text-muted-foreground">
          Configure your entity settings and preferences
        </p>
      </div>

      {/* Progress Indicator */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          {[1, 2, 3].map((step) => (
            <div 
              key={step}
              className={`flex items-center ${step < 3 ? 'flex-1' : ''}`}
            >
              <div className={`
                w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                ${currentStep >= step 
                  ? 'bg-primary text-primary-foreground' 
                  : 'bg-muted text-muted-foreground'
                }
              `}>
                {currentStep > step ? <CheckCircle className="h-4 w-4" /> : step}
              </div>
              {step < 3 && (
                <div className={`h-0.5 flex-1 mx-4 ${
                  currentStep > step ? 'bg-primary' : 'bg-muted'
                }`} />
              )}
            </div>
          ))}
        </div>
        <div className="flex justify-between text-sm text-muted-foreground">
          <span>Document Scanning</span>
          <span>Sync Settings</span>
          <span>Complete</span>
        </div>
      </div>

      {/* Step Content */}
      <Card>
        {currentStep === 1 && (
          <>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                AI Document Scanning
              </CardTitle>
              <CardDescription>
                Automatically detect prepayments from your existing documents
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 bg-muted rounded-lg">
                  <h3 className="font-medium mb-2">Documents Found</h3>
                  <p className="text-2xl font-bold text-primary">
                    {analysis.bills_analysis.bills_with_attachments}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    bills with attachments since {analysis.bills_analysis.financial_year_start}
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    (out of {analysis.bills_analysis.total_bills} total bills)
                  </p>
                </div>
                
                <div className="p-4 bg-muted rounded-lg">
                  <h3 className="font-medium mb-2">Expected Results</h3>
                  <p className="text-2xl font-bold text-green-600">
                    {analysis.bills_analysis.expected_prepayments}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    prepayments likely to be found
                  </p>
                </div>
              </div>

              <Alert>
                <Zap className="h-4 w-4" />
                <AlertDescription>
                  <strong>Cost:</strong> {analysis.bills_analysis.scanning_cost_estimate}
                  <br />
                  Most common prepayments: Insurance, software subscriptions, rent, licenses
                </AlertDescription>
              </Alert>

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="enable_scanning"
                    checked={setupData.enable_ai_scanning}
                    onChange={(e) => setSetupData(prev => ({
                      ...prev,
                      enable_ai_scanning: e.target.checked
                    }))}
                    className="rounded"
                  />
                  <label htmlFor="enable_scanning" className="text-sm font-medium">
                    Enable AI document scanning (Recommended)
                  </label>
                </div>
                
                {!setupData.enable_ai_scanning && (
                  <p className="text-sm text-muted-foreground ml-6">
                    You can enable this later in entity settings
                  </p>
                )}
              </div>
            </CardContent>
          </>
        )}

        {currentStep === 2 && (
          <>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Sync Configuration
              </CardTitle>
              <CardDescription>
                Configure how data is synchronized from {analysis.organization_info.name}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Sync Period Start</label>
                    <input
                      type="date"
                      value={setupData.sync_settings.sync_start_date}
                      onChange={(e) => updateSetupData('sync_settings', { sync_start_date: e.target.value })}
                      className="mt-1 block w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Auto-detected: Financial year start
                    </p>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Sync Frequency</label>
                    <select
                      value={setupData.sync_settings.sync_frequency}
                      onChange={(e) => updateSetupData('sync_settings', { sync_frequency: e.target.value })}
                      className="mt-1 block w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    >
                      <option value="daily">Daily (Recommended)</option>
                      <option value="weekly">Weekly</option>
                      <option value="manual">Manual Only</option>
                    </select>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium mb-3 block">Data Types to Sync</label>
                    <div className="space-y-2">
                      {[
                        { key: 'sync_bills', label: 'Bills & Purchases', recommended: true },
                        { key: 'sync_invoices', label: 'Sales Invoices' },
                        { key: 'sync_bank_transactions', label: 'Bank Transactions' },
                        { key: 'sync_journal_entries', label: 'Journal Entries' },
                      ].map(({ key, label, recommended }) => (
                        <div key={key} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id={key}
                            checked={setupData.sync_settings[key as keyof typeof setupData.sync_settings] as boolean}
                            onChange={(e) => updateSetupData('sync_settings', { [key]: e.target.checked })}
                            className="rounded"
                          />
                          <label htmlFor={key} className="text-sm flex items-center gap-2">
                            {label}
                            {recommended && <Badge variant="secondary" className="text-xs">Recommended</Badge>}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {analysis.accounts_analysis.prepayment_asset_accounts.length > 0 && (
                <div>
                  <label className="text-sm font-medium mb-3 block">Prepayment Asset Accounts</label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {analysis.accounts_analysis.prepayment_asset_accounts.map((account) => (
                      <div key={account.code} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={`asset_${account.code}`}
                          checked={setupData.account_settings.prepayment_asset_accounts.includes(account.code)}
                          onChange={(e) => {
                            const current = setupData.account_settings.prepayment_asset_accounts;
                            const updated = e.target.checked
                              ? [...current, account.code]
                              : current.filter(code => code !== account.code);
                            updateSetupData('account_settings', { prepayment_asset_accounts: updated });
                          }}
                          className="rounded"
                        />
                        <label htmlFor={`asset_${account.code}`} className="text-sm">
                          [{account.code}] {account.name}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </>
        )}

        {currentStep === 3 && (
          <>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                Review & Complete
              </CardTitle>
              <CardDescription>
                Review your configuration and complete the setup
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h3 className="font-medium">AI Document Scanning</h3>
                    <p className="text-sm text-muted-foreground">
                      {setupData.enable_ai_scanning ? (
                        <span className="text-green-600">✓ Enabled - Will scan {analysis.bills_analysis.bills_with_attachments} documents</span>
                      ) : (
                        <span className="text-orange-600">○ Disabled - Can be enabled later</span>
                      )}
                    </p>
                  </div>

                  <div>
                    <h3 className="font-medium">Sync Configuration</h3>
                    <p className="text-sm text-muted-foreground">
                      {setupData.sync_settings.sync_frequency} sync from {setupData.sync_settings.sync_start_date}
                    </p>
                  </div>

                  <div>
                    <h3 className="font-medium">Prepayment Accounts</h3>
                    <p className="text-sm text-muted-foreground">
                      {setupData.account_settings.prepayment_asset_accounts.length} accounts configured
                    </p>
                  </div>
                </div>

                <div className="space-y-4">
                  <Alert>
                    <Zap className="h-4 w-4" />
                    <AlertDescription>
                      <strong>What happens next:</strong>
                      <ul className="mt-2 space-y-1 text-xs">
                        <li>• Data sync will begin (5-15 minutes)</li>
                        {setupData.enable_ai_scanning && <li>• AI will scan documents for prepayments</li>}
                        <li>• You'll be notified when complete</li>
                        <li>• Review results in the entity dashboard</li>
                      </ul>
                    </AlertDescription>
                  </Alert>
                </div>
              </div>
            </CardContent>
          </>
        )}
      </Card>

      {/* Navigation */}
      <div className="flex justify-between mt-8">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={currentStep === 1}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => navigate(`/clients/${clientId}/entities`)}
          >
            Cancel
          </Button>

          {currentStep < 3 ? (
            <Button onClick={handleNext}>
              Next
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          ) : (
            <Button 
              onClick={handleCompleteSetup}
              disabled={submitting}
            >
              {submitting ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <CheckCircle className="h-4 w-4 mr-2" />
              )}
              Complete Setup
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}