# DRCR Firestore Database Schema

**Complete validated schema for all 21 Firestore collections in actual use**
*Last Updated: 2025-07-23 | Validated against actual backend implementation*

## Schema Overview

**Database Type:** Google Cloud Firestore (NoSQL document database)
**Field Access:** Use canonical field access helpers in `rest_api/utils/field_access.py`
**Collections in Use:** 21 collections (7 documented in `firestore_schema.yml` + 14 discovered in code audit)

## Field Access Guidelines

### Critical Field Access Rules
1. **Always use canonical field names** (snake_case) in new code
2. **Use helper functions** for reading potentially aliased data:
   - `get_field(data, "canonical_name")` - General field access with alias resolution
   - `get_account_code(line_data)` - Account code access
   - `get_journal_lines(journal_data)` - Journal lines access
3. **NEVER use direct patterns** like `data.get("field") or data.get("Field")`
4. **Handle null/undefined values** gracefully with defaults

### Common Field Aliases
```python
# Account codes - most common source of confusion
"AccountCode" → "account_code"
"accountCode" → "account_code"

# Date fields - multiple variations from Xero
"Date" → "date_issued"
"journalDate" → "journal_date"
"DueDate" → "date_due"

# Line item arrays
"JournalLines" → "lines"  
"LineItems" → "line_items"

# Amount fields
"LineAmount" → "line_amount"
"Total" → "total_amount"
```

## Core Business Collections

### FIRMS
**Purpose:** Accounting firms registered on the platform
```json
{
  "firm_id": "string (required)",
  "name": "string (required)",
  "contact_email": "string",
  "phone": "string",
  "address": "object",
  "subscription_status": "string",
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

### FIRM_USERS  
**Purpose:** User accounts associated with firms and their roles
```json
{
  "user_id": "string (required)",
  "firm_id": "string (required)", 
  "firebase_uid": "string (required)",
  "email": "string (required)",
  "role": "string", // firm_admin, staff
  "status": "string", // invited, active, inactive, suspended
  "assigned_client_ids": "array",
  "created_at": "timestamp",
  "last_invite_sent": "timestamp",
  "created_by": "string",
  "updated_by": "string"
}
```

### CLIENTS
**Purpose:** Client businesses that accounting firms serve
```json
{
  "client_id": "string (required)",
  "firm_id": "string (required)",
  "name": "string (required)",
  "client_type": "string",
  "industry": "string", 
  "status": "string",
  "contact_email": "string",
  "contact_phone": "string",
  "address": "object",
  "created_at": "timestamp",
  "updated_at": "timestamp",
  "created_by": "string"
}
```

### ENTITIES
**Purpose:** Business entities (Xero orgs, QBO companies) linked to clients
```json
{
  "entity_id": "string (required)",
  "client_id": "string (required)",
  "entity_name": "string (required)",
  "type": "string", // xero, qbo, manual
  "status": "string", // active, inactive, error
  "connection_details": {
    "status": "string", // active, error, disconnected
    "connected_by": "string",
    "xero_tenant_id": "string",
    "last_sync": "timestamp",
    "error_message": "string"
  },
  "base_currency": "string", // Primary source for entity currency (e.g., "GBP", "USD")
  "country_code": "string", // ISO country code (e.g., "GB", "US")
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

### ENTITY_SETTINGS
**Purpose:** Configuration settings for each entity
```json
{
  "entity_id": "string (required)",
  "client_id": "string (required)",
  "prepayment_asset_account_codes": "array",
  "excluded_pnl_account_codes": "array", 
  "default_expense_account_code": "string",
  "default_amortization_months": "number",
  "amortization_materiality_threshold": "number",
  "amortization_apply_50_percent_rule": "boolean", // Apply 50% rule for day-based method (default: true)
  "auto_sync_enabled": "boolean",
  "sync_frequency": "string", // hourly, daily, weekly, manual
  "sync_bills": "boolean",
  "sync_invoices": "boolean", 
  "sync_payments": "boolean",
  "sync_bank_transactions": "boolean",
  "sync_journal_entries": "boolean",
  "sync_spend_money": "boolean",
  "transaction_sync_start_date": "string",
  "enable_llm_prepayment_detection": "boolean",
  "enable_prepayment_release_detector": "boolean",
  "scanning_amount_threshold": "number",
  "auto_post_proposed_journals": "boolean",
  "base_currency_code": "string", // NOTE: Read-only, not user-editable. Use ENTITIES.base_currency instead
  "initial_sync_completed": "boolean",
  "last_full_sync_date": "timestamp",
  "_system_lastSyncTimestampUtc_Bills": "timestamp",
  "_system_lastSyncTimestampUtc_Invoices": "timestamp"
}
```

## Financial Data Collections

### TRANSACTIONS
**Purpose:** Bills, invoices, and financial transactions from external systems
```json
{
  "transaction_id": "string (required)",
  "entity_id": "string (required)", 
  "client_id": "string (required)",
  "document_number": "string",
  "document_type": "string", // BILL, INVOICE
  "transaction_type": "string", // ACCPAY, ACCREC  
  "total_amount": "number",
  "date_issued": "timestamp",
  "date_due": "timestamp", 
  "currency_code": "string",
  "contact_id": "string",
  "contact_name": "string",
  "line_items": "array", // Array of line item objects
  "status": "string",
  "processing_status": "string", // completed, failed, skipped, metadata_loaded
  "attachments": "array",
  "created_at": "timestamp",
  "updated_at": "timestamp",
  "raw_data": "object" // Original data from external system
}
```

### AMORTIZATION_SCHEDULES  
**Purpose:** Prepayment amortization schedules
```json
{
  "schedule_id": "string (required)",
  "transaction_id": "string (required)",
  "entity_id": "string (required)",
  "client_id": "string (required)",
  "status": "string", // pending_configuration, pending_confirmation, confirmed, posted, cancelled
  "detection_method": "string", // gl_coding, llm_detected, external_mj
  "original_amount": "number",
  "amortization_start_date": "timestamp",
  "amortization_end_date": "timestamp", 
  "number_of_periods": "number",
  "amortization_account_code": "string",
  "expense_account_code": "string",
  "amortization_method": "string", // equal_monthly, daily_based
  "monthly_entries": "array", // Array of monthly amortization entries
  "created_at": "timestamp",
  "updated_at": "timestamp",
  "posted_entries_count": "number",
  "remaining_balance": "number"
}
```

### MANUAL_JOURNALS
**Purpose:** Manual journal entries from external systems
```json
{
  "journal_id": "string (required)",
  "entity_id": "string (required)",
  "client_id": "string (required)", 
  "journal_date": "timestamp",
  "narration": "string",
  "status": "string", // POSTED, DRAFT
  "lines": "array", // Array of journal line objects
  "created_at": "timestamp",
  "updated_at": "timestamp",
  "raw_data": "object"
}
```

### PREPAYMENT_RELEASES_DETECTED
**Purpose:** AI-detected manual journal releases of prepayments  
```json
{
  "detection_id": "string (required)",
  "entity_id": "string (required)",
  "client_id": "string (required)",
  "journal_id": "string (required)", 
  "detection_confidence": "string", // high, medium, low
  "linked_transaction_id": "string",
  "synthetic_schedule_id": "string",
  "asset_account_code": "string",
  "expense_account_code": "string",
  "amount": "number",
  "journal_date": "timestamp",
  "detection_flags": "object",
  "created_at": "timestamp"
}
```

## Operational Collections

### SYNC_JOBS
**Purpose:** Synchronization job status and progress tracking
```json
{
  "sync_job_id": "string (required)",
  "entity_id": "string (required)",
  "client_id": "string (required)",
  "status": "string", // pending, processing, completed, failed
  "stage": "string", // metadata_loading, heavy_processing
  "progress_percent": "number", // 0-100
  "total_bills": "number",
  "processed_bills": "number", 
  "failed_bills": "number",
  "started_at": "timestamp",
  "updated_at": "timestamp",
  "completed_at": "timestamp",
  "started_by": "string", // cloud-function, user, api
  "document_types": "array", // Bills, Invoices, etc.
  "token_usage": {
    "openai_tokens": "number",
    "mistral_tokens": "number", 
    "total_cost": "number",
    "operations": "array" // Capped at 200 operations
  },
  "token_breakdown_count": "number",
  "credit_usage": {
    "credits_used": "number",
    "credits_used_openai": "number",
    "credits_used_mistral": "number"
  },
  "error_details": "object"
}
```

### SYNC_JOBS_USAGE_EVENTS (Sub-collection)
**Purpose:** Detailed token usage events for sync jobs
**Parent:** SYNC_JOBS collection
```json
{
  "event_id": "string (required)",
  "provider": "string", // openai, mistral
  "model": "string", // gpt-4o, mistral-large-latest
  "operation": "string", // ocr, analysis, validation, extraction
  "tokens": "number",
  "cost": "number",
  "timestamp": "timestamp",
  "bill_id": "string",
  "attachment_id": "string"
}
```

### AUDIT_LOG
**Purpose:** System audit trail and user action logging
```json
{
  "audit_id": "string (required)",
  "entity_id": "string",
  "client_id": "string",
  "user_id": "string",
  "event_type": "string",
  "event_category": "string",
  "event_description": "string",
  "resource_type": "string",
  "resource_id": "string", 
  "changes": "object",
  "ip_address": "string",
  "user_agent": "string",
  "timestamp": "timestamp",
  "status": "string"
}
```

## Reference Data Collections

### CHART_OF_ACCOUNTS
**Purpose:** Cached chart of accounts from external systems
```json
{
  "account_id": "string (required)",
  "entity_id": "string (required)",
  "code": "string (required)",
  "name": "string (required)",
  "type": "string", // ASSET, EXPENSE, LIABILITY, EQUITY, REVENUE
  "class": "string", // CURRENT, NONCURRENT
  "status": "string", // ACTIVE, ARCHIVED
  "description": "string",
  "tax_type": "string",
  "enable_payments_to_account": "boolean",
  "bank_account_number": "string",
  "currency_code": "string",
  "reporting_code": "string",
  "reporting_code_name": "string",
  "has_attachments": "boolean",
  "updated_date_utc": "timestamp",
  "created_at": "timestamp"
}
```

### CONTACTS  
**Purpose:** Contacts, vendors, customers from external systems
```json
{
  "contact_id": "string (required)",
  "entity_id": "string (required)",
  "client_id": "string (required)",
  "name": "string (required)",
  "contact_type": "string", // SUPPLIER, CUSTOMER
  "email": "string",
  "phone": "string",
  "addresses": "array",
  "tax_number": "string",
  "account_number": "string", 
  "contact_status": "string", // ACTIVE, ARCHIVED
  "default_currency": "string",
  "purchase_tracking_categories": "array",
  "sales_tracking_categories": "array",
  "payment_terms": "object",
  "contact_groups": "array",
  "updated_date_utc": "timestamp",
  "created_at": "timestamp"
}
```

### COMMENTS
**Purpose:** Universal commenting system with mentions and notifications
```json
{
  "comment_id": "string (required)", // UUID v4 as document ID
  "parent_type": "string (required)", // schedule, transaction, invoice, entity, etc.
  "parent_id": "string (required)", // ID of the record being commented on
  "client_id": "string (required)", // Client ID for security scoping
  "entity_id": "string", // Entity ID if applicable
  "text": "string (required)", // Comment text (max 5000 characters)
  "mentions": "array", // Array of user UIDs mentioned in comment (max 20)
  "created_by": "string (required)", // Author UID
  "created_at": "timestamp (required)",
  "updated_at": "timestamp", // Set when comment is edited
  "deleted": "boolean", // Soft delete flag (default: false)
  "reply_to_comment_id": "string" // For threaded replies (future feature)
}
```

**Firestore Indexes:**
- `(parent_type ASC, parent_id ASC, created_at DESC)` - List comments for record
- `(client_id ASC, created_at DESC)` - Client-scoped comment streams
- `(mentions ARRAY_CONTAINS, created_at DESC)` - Mention notifications
- `(parent_type ASC, parent_id ASC, deleted ASC, created_at DESC)` - Filtered queries
- `(parent_type ASC, parent_id ASC, created_by ASC, created_at DESC)` - Author filtering

**Security Model:**
- Comments are scoped by `client_id` and respect existing firm/client access controls
- Users can only comment on records they have permission to view
- Only comment authors can edit or delete their own comments
- Mentioned users must have access to the same client

**Integration:**
- Cloud Function `comment_notification_processor` handles mention notifications
- Comments can be attached to any record type in the system
- Frontend `CommentsPanel` component provides universal UI

### ATTACHMENTS
**Purpose:** Document attachments metadata and storage references
```json
{
  "attachment_id": "string (required)",
  "entity_id": "string (required)",
  "client_id": "string (required)",
  "transaction_id": "string",
  "file_name": "string",
  "url": "string",
  "mime_type": "string",
  "file_size": "number",
  "include_online": "boolean",
  "storage_url": "string", // GCS URL
  "processing_status": "string",
  "llm_extracted_data": "object",
  "ocr_text": "string",
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

## System Collections

### XERO_APP_TENANT_CONNECTIONS
**Purpose:** Xero OAuth connection management
```json
{
  "connection_id": "string (required)",
  "client_id": "string (required)",
  "entity_id": "string",
  "xero_tenant_id": "string (required)",
  "access_token": "string (encrypted)",
  "refresh_token": "string (encrypted)", 
  "expires_at": "timestamp",
  "created_at": "timestamp",
  "updated_at": "timestamp",
  "scopes": "array",
  "organization_name": "string",
  "organization_type": "string"
}
```

### TEMP_OAUTH_TOKENS
**Purpose:** Temporary OAuth tokens during authentication flow
```json
{
  "temp_token_id": "string (required)",
  "state": "string (required)",
  "access_token": "string",
  "refresh_token": "string",
  "expires_at": "timestamp",
  "tenant_info": "array",
  "created_at": "timestamp"
}
```

### USERS (Legacy)
**Purpose:** Legacy user collection (being migrated to FIRM_USERS)
```json
{
  "user_id": "string (required)",
  "firebase_uid": "string (required)",
  "email": "string", 
  "display_name": "string",
  "firm_id": "string",
  "created_at": "timestamp"
}
```

### CLIENT_USERS (Legacy)
**Purpose:** Legacy client-user relationships (being migrated)
```json
{
  "client_user_id": "string (required)",
  "client_id": "string (required)",
  "user_id": "string (required)",
  "role": "string",
  "created_at": "timestamp"
}
```

### COUNTERPARTIES (Alternative name for CONTACTS)
**Purpose:** Alternative collection name for contacts/vendors
*Structure same as CONTACTS collection*

### TEST
**Purpose:** Test collection for development and testing
*Structure varies based on testing needs*

## Data Type Guidelines

### Timestamps
- **Storage:** Firestore Timestamp objects
- **Not:** ISO strings or Unix timestamps  
- **Access:** Use `.toDate()` to convert to JavaScript Date

### Account Codes
- **Storage:** Always strings
- **Format:** May be numeric ("620") or alphanumeric ("6-2000")
- **Access:** Use `get_account_code()` helper for consistent access

### Amounts  
- **Storage:** Numbers (float)
- **Not:** Strings or formatted currency
- **Precision:** Maintain decimal precision for financial calculations

### Arrays
- **Empty State:** Empty arrays `[]` when no data
- **Not:** null or undefined
- **Validation:** Always check `.length` before processing

### Booleans
- **Storage:** true/false 
- **Not:** Strings like "true"/"false" or numbers like 0/1
- **Defaults:** Explicitly handle undefined values

## Collection Relationships

### Primary Relationships
```
FIRMS → FIRM_USERS (1:many)
FIRMS → CLIENTS (1:many)
CLIENTS → ENTITIES (1:many)
ENTITIES → ENTITY_SETTINGS (1:1)
ENTITIES → TRANSACTIONS (1:many)
TRANSACTIONS → AMORTIZATION_SCHEDULES (1:many)
TRANSACTIONS → ATTACHMENTS (1:many)
```

### Reference Relationships
```
ENTITIES → CHART_OF_ACCOUNTS (1:many)
ENTITIES → CONTACTS (1:many)
ENTITIES → MANUAL_JOURNALS (1:many)
ENTITIES → SYNC_JOBS (1:many)
SYNC_JOBS → SYNC_JOBS_USAGE_EVENTS (1:many)
```

## Migration Notes

### Active Migrations
1. **USERS → FIRM_USERS**: User management centralization
2. **CLIENT_USERS → FIRM_USERS**: Role assignment consolidation
3. **Field standardization**: Legacy field names to canonical names

### Migration Strategy
- **Dual-write:** Write to both old and new collections during transition
- **Gradual migration:** Batch update historical data
- **Telemetry tracking:** Monitor alias usage before deprecation

## Performance Considerations

### Indexes
Required indexes managed in `firestore.indexes.json`:
- Composite indexes for common query patterns
- Single field indexes for frequently filtered fields

### Query Patterns
- **Pagination:** Use `limit()` and `offset()` for large result sets
- **Filtering:** Leverage compound indexes for multi-field queries  
- **Ordering:** Consider index requirements for `orderBy()` queries

### Data Volume
- **TRANSACTIONS:** High volume, partition by entity_id
- **SYNC_JOBS_USAGE_EVENTS:** Sub-collection pattern for scalability
- **AUDIT_LOG:** Automatic cleanup of old records

This comprehensive schema covers all 20 collections currently in use with validated field structures and access patterns.