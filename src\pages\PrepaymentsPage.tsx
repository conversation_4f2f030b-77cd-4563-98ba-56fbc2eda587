// @ts-nocheck
import React, { useState, useMemo, useEffect, useRef, useCallback } from 'react';
import { useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import { useAuthStore } from '../store/auth.store';
import { useAccpaySelections } from '../store/navigation.store';
import { useFirmName } from '../hooks/useFirmName';
import { ClientSelector } from '../components/ui/client-selector';
import { EntitySelector } from '../components/ui/entity-selector';
import { StatusFilterSelector } from '../components/ui/status-filter-selector';
import { useURLSync, type ValidationLists } from '../hooks/useURLSync';
import { useConfirmDialog } from '../hooks/useConfirmDialog';
import { useToast } from '../hooks/useToast';
import { useBulkCreateModal } from '../hooks/useBulkCreateModal';
import { useBulkEditModal } from '../hooks/useBulkEditModal';
import { filterAccountsForPrepayments } from '../utils/accountFiltering';
import { SideBySideReview } from '../components/prepayments/SideBySideReview';
import { EditScheduleModal, type ScheduleData, type ScheduleUpdateData } from '../components/prepayments/EditScheduleModal';
import { BulkEditScheduleModal, type BulkLineItem, type BulkEditData } from '../components/prepayments/BulkEditScheduleModal';
import { api } from '@/lib/api';
import { DEFAULT_STATUS_FILTERS } from '@/constants/status-filters';
import { PrepaymentsService, type PrepaymentsFilters } from '@/services/prepayments.service';
import { 
    SupplierStatus, 
    InvoiceStatus, 
    ScheduleStatus, 
    EntryStatus,
    isActionNeededStatus,
    isEditableStatus,
    isConfirmableStatus,
    isSkippableStatus,
    mapBackendScheduleStatus,
} from '@/types/schedule.types';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '../components/ui/breadcrumb';
import { Separator } from '../components/ui/separator';
import { SidebarTrigger } from '../components/ui/sidebar';

// --- Shadcn/UI & Lucide Imports ---
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { StatusBadge } from '@/components/ui/status-badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import {
    Users,
    LogOut,
    Building,
    Briefcase,
    Menu,
    ChevronDown,
    FileText,
    AlertTriangle,
    CheckCircle2,
    Loader2,
    XCircle,
    Edit,
    X,
    Search,
    Filter,
    Eye,
    ChevronRight,
    Scissors,
    Ban,
    CheckCheck,
    CheckSquare,
    GripVertical,
    Settings2,
} from 'lucide-react';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';

// --- Types ---
type UserInfo = { userId: string; displayName: string; email: string; firmName?: string; role: string; avatarUrl?: string; };
type Client = { clientId: string; clientName: string; };
type Entity = { entityId: string; entityName: string; type: string; connectionStatus: string; lastSync?: string; };

// Import types from service to ensure consistency
import type { SupplierData, InvoiceData, AmortizableLineItem, PaginationData } from '@/services/prepayments.service';

// --- Real API Functions ---
const fetchClients = async (): Promise<Client[]> => {
    try {
        const startTime = performance.now();
        const response = await api.getClients();
        const endTime = performance.now();
        console.log(`🚀 fetchClients: ${Math.round(endTime - startTime)}ms`);

        return response.clients.map(client => ({
            clientId: client.client_id,
            clientName: client.name // Now matches the updated interface
        }));
    } catch (error) {
        console.error('Error fetching clients:', error);
        throw new Error('Failed to fetch clients');
    }
};

const fetchEntities = async (clientId: string): Promise<Entity[]> => {
    try {
        const response = await api.getEntitiesForClient(clientId);
        console.log('DEBUG: Raw entities response:', response.entities);
        return response.entities.map(entity => ({
            entityId: entity.entity_id,
            entityName: entity.entity_name,
            type: entity.type || 'unknown',
            connectionStatus: entity.connection_status || 'unknown',
            lastSync: entity.last_sync
        }));
    } catch (error) {
        console.error('Error fetching entities:', error);
        throw new Error('Failed to fetch entities');
    }
};

interface DashboardFilters {
    dateRange?: string;
    status?: string;
    supplier?: string;
    statusFilters?: string[];
    supplierFilter?: string;
    page?: number;
    pageSize?: number;
}

interface ScheduleDetails {
    id?: string;
    name: string;
    frequency: string;
    startDate: string;
    endDate?: string;
    amount: number;
    description?: string;
}

const fetchDashboardData = async (clientId: string, entityId: string | 'all', filters: DashboardFilters): Promise<{ suppliers: SupplierData[], pagination: PaginationData }> => {
    try {
        const prepaymentsFilters: PrepaymentsFilters = {
            client_id: clientId,
            entity_id: entityId !== 'all' ? entityId : undefined,
            page: filters.page || 1,
            limit: filters.pageSize || 10,
            supplier_filter: filters.supplierFilter,
            status_filters: filters.statusFilters,
        };

        const response = await PrepaymentsService.getPrepaymentsData(prepaymentsFilters);

        return {
            suppliers: response.suppliers,
            pagination: {
                currentPage: response.pagination.currentPage,
                pageSize: response.pagination.pageSize,
                totalItems: response.pagination.totalItems,
                totalPages: response.pagination.totalPages,
            }
        };
    } catch (error) {
        console.error('Error fetching dashboard data:', error);
        throw new Error('Failed to fetch dashboard data');
    }
};

// --- Real API Actions ---
const handleLineConfirm = async (scheduleId: string, updateScheduleInGrid: (scheduleId: string, updatedSchedule: ScheduleData) => void, refreshData: () => void, showToast: ReturnType<typeof useToast>, setLoadingSchedules: React.Dispatch<React.SetStateAction<Record<string, 'confirming' | 'skipping'>>>) => {
    setLoadingSchedules(prev => ({ ...prev, [scheduleId]: 'confirming' }));
    const loadingToast = showToast.showLoading('Confirming schedule...');
    try {
        await PrepaymentsService.confirmSchedule(scheduleId);
        showToast.dismiss(loadingToast);
        showToast.showSuccess('Schedule confirmed successfully');
        
        // Update grid in place with latest schedule data
        try {
            const updatedSchedule = await PrepaymentsService.getSchedule(scheduleId);
            updateScheduleInGrid(scheduleId, updatedSchedule);
            console.log('✅ Grid updated in place after line confirmation');
        } catch (fetchError) {
            console.error('Failed to fetch updated schedule, falling back to full refresh:', fetchError);
            refreshData();
        }
    } catch (error) {
        console.error('Error confirming schedule:', error);
        showToast.dismiss(loadingToast);
        showToast.showError('Failed to confirm schedule', 'Please try again or contact support if the problem persists.');
    } finally {
        setLoadingSchedules(prev => {
            const newState = { ...prev };
            delete newState[scheduleId];
            return newState;
        });
    }
};

const handleLineSkip = async (scheduleId: string, updateScheduleInGrid: (scheduleId: string, updatedSchedule: ScheduleData) => void, refreshData: () => void, showToast: ReturnType<typeof useToast>, openDialog: ReturnType<typeof useConfirmDialog>['openDialog'], setLoadingSchedules: React.Dispatch<React.SetStateAction<Record<string, 'confirming' | 'skipping'>>>) => {
    openDialog({
        title: 'Skip Schedule',
        description: 'Are you sure you want to skip this schedule? This action cannot be undone.',
        confirmText: 'Skip Schedule',
        cancelText: 'Cancel',
        variant: 'warning',
        onConfirm: async () => {
            setLoadingSchedules(prev => ({ ...prev, [scheduleId]: 'skipping' }));
            // For now, use a simple reason. In the future, this could be enhanced with a form
            const reason = 'Skipped via user action';
            const loadingToast = showToast.showLoading('Skipping schedule...');
            try {
                await PrepaymentsService.skipSchedule(scheduleId, reason);
                showToast.dismiss(loadingToast);
                showToast.showSuccess('Schedule skipped successfully');
                
                // Update grid in place with latest schedule data
                try {
                    const updatedSchedule = await PrepaymentsService.getSchedule(scheduleId);
                    updateScheduleInGrid(scheduleId, updatedSchedule);
                    console.log('✅ Grid updated in place after line skip');
                } catch (fetchError) {
                    console.error('Failed to fetch updated schedule, falling back to full refresh:', fetchError);
                    refreshData();
                }
            } catch (error) {
                console.error('Error skipping schedule:', error);
                showToast.dismiss(loadingToast);
                showToast.showError('Failed to skip schedule', 'Please try again or contact support if the problem persists.');
                throw error; // Re-throw to keep dialog open on error
            } finally {
                setLoadingSchedules(prev => {
                    const newState = { ...prev };
                    delete newState[scheduleId];
                    return newState;
                });
            }
        }
    });
};

// Removed handleCellEdit function - no longer needed

const handleLogout = (showToast: ReturnType<typeof useToast>) => { 
    showToast.showInfo('Logout functionality not yet implemented'); 
};
const getInitials = (name: string) => name.split(' ').map(n => n[0]).slice(0, 2).join('').toUpperCase();
const handleSupplierConfirmAll = (supplierId: string, entityId: string | 'all', showToast: ReturnType<typeof useToast>) => { 
    showToast.showInfo('Bulk confirm functionality coming soon', `Supplier ${supplierId} in Entity ${entityId}`);
};
const handleInvoiceConfirmAll = async (invoiceId: string, showToast: ReturnType<typeof useToast>, bulkConfirmHandler: any, dashboardSuppliers: any[], refreshData: () => Promise<void>) => { 
    try {
        // Find the invoice and get all confirmable schedules
        let targetInvoice = null;
        for (const supplier of dashboardSuppliers) {
            const invoice = supplier.invoices.find(inv => inv.invoiceId === invoiceId);
            if (invoice) {
                targetInvoice = invoice;
                break;
            }
        }
        
        if (!targetInvoice) {
            showToast.showError('Invoice not found', 'Could not find invoice data. Please try again.');
            return;
        }
        
        // Get all confirmable line items
        const confirmableLines = targetInvoice.amortizableLineItems.filter(line => 
 
            line.overallStatus === 'proposed'
        );
        
        if (confirmableLines.length === 0) {
            showToast.showInfo('No items to confirm', 'No amortizable line items are ready for confirmation.');
            return;
        }
        
        // Extract schedule IDs
        const scheduleIds = confirmableLines.map(line => line.scheduleId);
        
        console.log(`🚀 Confirming ${scheduleIds.length} schedules for invoice ${targetInvoice.reference}`);
        
        // Use the bulk confirm handler
        await bulkConfirmHandler(scheduleIds);
        
        // Refresh data after successful confirmation
        await refreshData();
        
        showToast.showSuccess(`Confirmed ${scheduleIds.length} schedule${scheduleIds.length !== 1 ? 's' : ''} for invoice ${targetInvoice.reference}`);
        
    } catch (error) {
        console.error('Invoice confirm all failed:', error);
        showToast.showError('Confirm failed', 'Failed to confirm schedules. Please try again.');
    }
};
const handleInvoiceSkip = (invoiceId: string, showToast: ReturnType<typeof useToast>) => { 
    showToast.showInfo('Bulk skip functionality coming soon', `Invoice ${invoiceId}`);
};

function PrepaymentsGrid({
    initialClientId = null, // Let it auto-select the first client
    initialEntityId = null, // Let it auto-select the first entity
    initialStatusFilters = null, // Let it auto-select the first status filter
    currentUser = { userId: "user-admin-1", displayName: "Admin User", email: "<EMAIL>", firmName: "Example Firm", role: "firm_admin" }
 } : {
    initialClientId: string | null,
    initialEntityId: string | null,
    initialStatusFilters: string[] | null,
    currentUser: UserInfo;
 }) {
    const { openDialog, dialogElement } = useConfirmDialog();
    const showToast = useToast();
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();

    // Currency formatting helper
    const formatCurrency = (amount: number, currencyCode: string = 'USD') => {
        // Simple currency symbol mapping - can be expanded as needed
        const currencySymbols: Record<string, string> = {
            'USD': '$',
            'EUR': '€',
            'GBP': '£',
            'CAD': 'C$',
            'AUD': 'A$',
            'JPY': '¥',
        };
        const symbol = currencySymbols[currencyCode] || currencyCode;
        return `${symbol}${amount.toFixed(2)}`;
    };
    const { firmName, isLoading: firmNameLoading } = useFirmName();
    const [clients, setClients] = useState<Client[]>([]);
    const [entities, setEntities] = useState<Entity[]>([]);
    const [supplierFilter, setSupplierFilter] = useState<string>('');
    
    // Get navigation state from Zustand store
    const { 
        selectedClientId, 
        selectedEntityId, 
        selectedStatusFilters,
        setClientId, 
        setEntityId, 
        setStatusFilters 
    } = useAccpaySelections();
    
    // Prepare validation lists for URL sync
    const validationLists: ValidationLists = {
        clients: clients.length > 0 ? clients : undefined,
        entities: entities.length > 0 ? entities : undefined,
        statusFilters: ['pending_configuration', 'proposed', 'confirmed', 'posted', 'partially_posted', 'skipped', 'cancelled', 'error', 'excluded']
    };
    
    // URL synchronization with proper precedence
    const { isHydrated, announceMessage, clearAnnouncement } = useURLSync({
        currentState: {
            clientId: selectedClientId,
            entityId: selectedEntityId,
            statusFilters: selectedStatusFilters,
        },
        handlers: {
            onClientChange: setClientId,
            onEntityChange: setEntityId,
            onStatusFiltersChange: setStatusFilters,
        },
        validationLists,
        enableDebug: import.meta.env.DEV,
    });
    const [dashboardSuppliers, setDashboardSuppliers] = useState<SupplierData[]>([]);
    const [pagination, setPagination] = useState<PaginationData>({ currentPage: 1, totalItems: 0, totalPages: 0 });
    const [isLoadingClients, setIsLoadingClients] = useState<boolean>(false);
    const [isLoadingEntities, setIsLoadingEntities] = useState<boolean>(false);
    const [isLoadingData, setIsLoadingData] = useState<boolean>(false);
    const [expandedSuppliers, setExpandedSuppliers] = useState<Record<string, boolean>>({});
    const [expandedInvoices, setExpandedInvoices] = useState<Record<string, boolean>>({});
    const [loadingSchedules, setLoadingSchedules] = useState<Record<string, 'confirming' | 'skipping'>>({});

    // SideBySideReview modal state
    const [reviewModalOpen, setReviewModalOpen] = useState<boolean>(false);
    const [selectedInvoiceForReview, setSelectedInvoiceForReview] = useState<InvoiceData | null>(null);
    const [selectedScheduleForReview, setSelectedScheduleForReview] = useState<ScheduleData | null>(null);
    const [reviewModalLoading, setReviewModalLoading] = useState<boolean>(false);
    const [reviewModalError, setReviewModalError] = useState<string | null>(null);
    // Raw Xero line items for the pop-up (includes item code, qty, unit price, etc.)
    const [rawLineItemsForReview, setRawLineItemsForReview] = useState<any[] | null>(null);

    // EditScheduleModal state
    const [editScheduleModalOpen, setEditScheduleModalOpen] = useState<boolean>(false);
    const [selectedScheduleForEdit, setSelectedScheduleForEdit] = useState<ScheduleData | null>(null);
    const [editScheduleLoading, setEditScheduleLoading] = useState<boolean>(false);
    const [editScheduleError, setEditScheduleError] = useState<string | null>(null);
    const [availableAccounts, setAvailableAccounts] = useState<{ amortization: { code: string; name: string }[], expense: { code: string; name: string }[] }>({ amortization: [], expense: [] });
    const [focusedMonthKey, setFocusedMonthKey] = useState<string | undefined>(undefined);
    const [editContext, setEditContext] = useState<'line_item' | 'invoice_level'>('line_item');

    // Load accounts for modal
    const loadAccountsForModal = useCallback(async () => {
        if (selectedEntityId && selectedEntityId !== 'all') {
            await fetchAccountsForEntity(selectedEntityId);
        }
    }, [selectedEntityId]);

    // Use bulk edit modal hook for editing existing schedules
    const { 
        isOpen: bulkEditModalOpen, 
        selectedInvoice: selectedInvoiceForBulkEdit, 
        isLoading: bulkEditLoading, 
        error: bulkEditError,
        openModal: openBulkModal,
        closeModal: closeBulkModal,
        handleSave: bulkSaveHandler,
        handleConfirm: bulkConfirmHandler,
        handleSkip: bulkSkipHandler
    } = useBulkEditModal();

    const [monthColWidths, setMonthColWidths] = useState<Record<string, number>>({});
    const isResizing = useRef<number | null>(null);
    const startX = useRef<number>(0);
    const startWidth = useRef<number>(0);
    const tableRef = useRef<HTMLTableElement>(null);

    const MIN_COL_WIDTH = 90;
    const DEFAULT_FIRST_COL_WIDTH = 400;

    // Function to refresh data
    const refreshData = useCallback(() => {
        if (selectedClientId) {
            setIsLoadingData(true);
            const filters = {
                statusFilters: selectedStatusFilters.length > 0 ? selectedStatusFilters : undefined,
                supplierFilter: supplierFilter || null,
                page: pagination.currentPage || 1,
            };
            fetchDashboardData(selectedClientId, selectedEntityId, filters)
                .then(data => {
                    setDashboardSuppliers(data.suppliers);
                    setPagination(data.pagination);
                })
                .catch(error => {
                    console.error('Error refreshing data:', error);
                    showToast.showError('Failed to refresh data', 'Please try again.');
                })
                .finally(() => setIsLoadingData(false));
        }
    }, [selectedClientId, selectedEntityId, selectedStatusFilters, supplierFilter, pagination.currentPage, showToast]);

    // Smart update function to update specific schedule data without reloading entire grid
    const updateScheduleInGrid = useCallback((scheduleId: string, updatedSchedule: ScheduleData) => {
        setDashboardSuppliers(prevSuppliers => {
            return prevSuppliers.map(supplier => ({
                ...supplier,
                invoices: supplier.invoices.map(invoice => ({
                    ...invoice,
                    amortizableLineItems: invoice.amortizableLineItems.map(line => {
                        if (line.scheduleId === scheduleId) {
                            // Update the line item with new schedule data
                            const newMonthlyBreakdown: Record<string, any> = {};
                            
                            // Convert monthly entries back to breakdown format
                            updatedSchedule.monthlyEntries?.forEach(entry => {
                                const monthKey = entry.monthDate.substring(0, 7); // YYYY-MM format
                                newMonthlyBreakdown[monthKey] = {
                                    amount: entry.amount,
                                    status: entry.status === 'pending_configuration' ? 'proposed' : entry.status,
                                    journalId: entry.postedJournalId,
                                    error: entry.postingError
                                };
                            });

                            return {
                                ...line,
                                overallStatus: updatedSchedule.status,
                                prepaymentAccountCode: updatedSchedule.amortizationAccountCode,
                                expenseAccountCode: updatedSchedule.expenseAccountCode,
                                monthlyBreakdown: newMonthlyBreakdown,
                                lineAmount: updatedSchedule.originalAmount
                            };
                        }
                        return line;
                    })
                }))
            }));
        });
    }, []);

    // Effects
    useEffect(() => {
        setIsLoadingClients(true);
        fetchClients()
        .then(fetchedClients => {
            console.log('DEBUG: Fetched clients:', fetchedClients);
            setClients(fetchedClients);
            // Only auto-select first client if no URL selection and not already selected
            if (!selectedClientId && fetchedClients.length > 0 && isHydrated && !initialClientId) {
                console.log('DEBUG: Setting selectedClientId to:', fetchedClients[0].clientId);
                setClientId(fetchedClients[0].clientId);
            }
            // Set initial client if provided and not overridden by URL
            if (initialClientId && !selectedClientId && isHydrated) {
                setClientId(initialClientId);
            }
        })
        .catch(error => {
            console.error('DEBUG: Error fetching clients:', error);
        })
        .finally(() => setIsLoadingClients(false));
    }, [isHydrated]); // Wait for URL hydration before auto-selecting

    useEffect(() => {
        if (selectedClientId && isHydrated) {
            setIsLoadingEntities(true);
            setEntities([]);
            setDashboardSuppliers([]);
            setPagination({ currentPage: 1, totalItems: 0, totalPages: 0 });
            setExpandedSuppliers({});
            setExpandedInvoices({});
            fetchEntities(selectedClientId)
                .then(fetchedEntities => {
                    console.log('DEBUG: Fetched entities for client', selectedClientId, ':', fetchedEntities);
                    setEntities(fetchedEntities);
                    // Don't auto-reset entity - let URL sync handle it
                })
                .catch(error => {
                    console.error('DEBUG: Error fetching entities:', error);
                })
                .finally(() => setIsLoadingEntities(false));
        } else if (!selectedClientId) {
            setEntities([]);
            setDashboardSuppliers([]);
            setPagination({ currentPage: 1, totalItems: 0, totalPages: 0 });
            setExpandedSuppliers({});
            setExpandedInvoices({});
        }
    }, [selectedClientId, isHydrated]);

    useEffect(() => {
        const currentPage = pagination.currentPage || 1;
        if (selectedClientId && isHydrated) {
            setIsLoadingData(true);
            const filters = {
                statusFilters: selectedStatusFilters.length > 0 ? selectedStatusFilters : undefined,
                supplierFilter: supplierFilter || null,
                page: currentPage,
            }
            console.log('🚀 Starting dashboard data fetch...');
            const startTime = performance.now();

            fetchDashboardData(selectedClientId, selectedEntityId, filters)
                .then(data => {
                    const endTime = performance.now();
                    const duration = Math.round(endTime - startTime);
                    console.log(`✅ Dashboard data fetch completed: ${duration}ms`);

                    setDashboardSuppliers(data.suppliers);
                    setPagination(data.pagination);
                    if (filters.page === 1) {
                        // Auto-expand all suppliers and invoices
                        const expandedSuppliersMap: Record<string, boolean> = {};
                        const expandedInvoicesMap: Record<string, boolean> = {};
                        
                        data.suppliers.forEach(supplier => {
                            expandedSuppliersMap[supplier.supplierId] = true;
                            supplier.invoices.forEach(invoice => {
                                expandedInvoicesMap[invoice.invoiceId] = true;
                            });
                        });
                        
                        setExpandedSuppliers(expandedSuppliersMap);
                        setExpandedInvoices(expandedInvoicesMap);
                    }
                 })
                .catch(error => {
                    console.error('DEBUG: fetchDashboardData error:', error);
                })
                .finally(() => setIsLoadingData(false));
        } else {
            setDashboardSuppliers([]);
            setPagination({ currentPage: 1, totalItems: 0, totalPages: 0 });
            setExpandedSuppliers({});
            setExpandedInvoices({});
        }
    }, [selectedClientId, selectedEntityId, supplierFilter, selectedStatusFilters, pagination.currentPage, isHydrated]);

    // Reset pagination when filters change (but not on initial load)
    const prevFiltersRef = useRef({ selectedEntityId, supplierFilter, selectedStatusFilters: selectedStatusFilters.join(',') });
    useEffect(() => {
        const prev = prevFiltersRef.current;
        const current = { selectedEntityId, supplierFilter, selectedStatusFilters: selectedStatusFilters.join(',') };

        // Only reset if filters actually changed (not on initial render)
        if (prev.selectedEntityId !== current.selectedEntityId ||
            prev.supplierFilter !== current.supplierFilter ||
            prev.selectedStatusFilters !== current.selectedStatusFilters) {
            setPagination(p => ({ ...p, currentPage: 1 }));
        }

        prevFiltersRef.current = current;
    }, [selectedEntityId, supplierFilter, selectedStatusFilters]);

    // Note: Status filters are now persisted automatically via Zustand store

    const monthColumns = useMemo(() => {
        const allMonthKeys = new Set<string>();
        dashboardSuppliers?.forEach(s => s.invoices?.forEach(i => i.amortizableLineItems?.forEach(l => Object.keys(l.monthlyBreakdown).forEach(k => allMonthKeys.add(k)))));

        const sortedKeys = Array.from(allMonthKeys).sort((a, b) => {
            const [yearA, monthA] = a.split('-').map(Number);
            const [yearB, monthB] = b.split('-').map(Number);
            if (yearA !== yearB) return yearA - yearB;
            return monthA - monthB;
        });

        setMonthColWidths(currentWidths => {
            const newWidths = { ...currentWidths };
            let changed = false;
            sortedKeys.forEach(key => {
                if (newWidths[key] === undefined) {
                    newWidths[key] = 150;
                    changed = true;
                }
            });
            Object.keys(newWidths).forEach(key => {
                if (!allMonthKeys.has(key)) {
                    delete newWidths[key];
                    changed = true;
                }
            });
            return changed ? newWidths : currentWidths;
        });

        if (sortedKeys.length === 0) {
             const months = []; const today = new Date();
             for (let i = 0; i < 6; i++) { const d=new Date(today.getFullYear(),today.getMonth()+i,1); months.push({ key: `${d.getFullYear()}-${(d.getMonth()+1).toString().padStart(2,'0')}`, label: d.toLocaleDateString('en-US',{month:'short',year:'numeric'})}); }
             return months;
        }

        return sortedKeys.map(k => {
            const [y,m]=k.split('-');
            const d=new Date(parseInt(y),parseInt(m)-1,1);
            return {key:k, label:d.toLocaleDateString('en-US',{month:'short',year:'numeric'})};
        });
    }, [dashboardSuppliers]);

    const handleMouseDown = useCallback((index: number, event: React.MouseEvent<HTMLDivElement>) => {
        isResizing.current = index;
        startX.current = event.clientX;

        const monthKey = monthColumns[index as number]?.key;
        if (monthKey) {
            startWidth.current = monthColWidths[monthKey] || MIN_COL_WIDTH;
        } else {
            isResizing.current = null;
            return;
        }

        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = 'col-resize';
        document.body.style.userSelect = 'none';

    }, [monthColumns, monthColWidths]);

    const handleMouseMove = useCallback((event: MouseEvent) => {
        if (isResizing.current === null) return;

        const currentX = event.clientX;
        const deltaX = currentX - startX.current;
        const newWidth = Math.max(MIN_COL_WIDTH, startWidth.current + deltaX);

        const monthKey = monthColumns[isResizing.current as number]?.key;
        if (monthKey) {
            setMonthColWidths(prevWidths => ({
                ...prevWidths,
                [monthKey]: newWidth,
            }));
        }

    }, [monthColumns]);

    const handleMouseUp = useCallback(() => {
        isResizing.current = null;
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
    }, [handleMouseMove]);

    useEffect(() => {
        return () => {
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        };
    }, [handleMouseMove, handleMouseUp]);

    const toggleSupplier = (id: string) => setExpandedSuppliers(p => ({...p, [id]: !p[id]}));
    const toggleInvoice = (id: string) => setExpandedInvoices(p => ({...p, [id]: !p[id]}));

    // Handle viewing attachment - opens SideBySideReview modal
    const handleViewAttachment = async (attachmentId: string, invoice: InvoiceData) => {
        setSelectedInvoiceForReview(invoice);
        setReviewModalLoading(true);
        setReviewModalError(null);
        setReviewModalOpen(true);
        
        try {
            // Ensure we have accounts loaded for display names
            if (selectedEntityId && selectedEntityId !== 'all') {
                await fetchAccountsForEntity(selectedEntityId);
            } else {
                // Set fallback accounts if no specific entity
                setAvailableAccounts({
                    amortization: [
                        { code: '620', name: 'Prepaid Expenses' },
                        { code: '621', name: 'Prepaid Insurance' },
                        { code: '622', name: 'Prepaid Rent' }
                    ],
                    expense: [
                        { code: '5000', name: 'Office Expenses' },
                        { code: '5100', name: 'Insurance Expense' },
                        { code: '5200', name: 'Rent Expense' }
                    ]
                });
            }
            
            // Fetch real schedule data for the first amortizable line item
            const firstAmortizableItem = invoice.amortizableLineItems.find(line => 
                line.overallStatus !== 'excluded' && line.overallStatus !== 'validation_failed'
            );
            
            if (firstAmortizableItem?.scheduleId) {
                console.log('🔍 Fetching real schedule data for Side-by-Side Review:', firstAmortizableItem.scheduleId);
                const realScheduleData = await PrepaymentsService.getSchedule(firstAmortizableItem.scheduleId);
                console.log('✅ Real schedule data loaded:', realScheduleData);
                
                // Small delay to ensure accounts state is updated before rendering
                await new Promise(resolve => setTimeout(resolve, 100));
                
                // Store the real schedule data for use in the modal
                setSelectedScheduleForReview(realScheduleData);
            }

            // Fetch full transaction to obtain raw line items
            try {
                const txn = await api.getTransaction(invoice.invoiceId);
                const rawItems = txn.line_items || txn.lineItems || txn.raw_xero_data?.LineItems || [];
                const mapped = rawItems.map((item: any, idx: number) => ({
                    lineItemId: item.LineItemID || item.line_item_id || `idx-${idx}`,
                    itemCode: item.ItemCode       || item.item_code       || '',
                    description: item.Description  || item.description    || '',
                    quantity:    item.Quantity      ?? item.quantity       ?? 1,
                    unitPrice:   item.UnitAmount    ?? item.unit_amount    ?? item.UnitPrice ?? 0,
                    accountCode: item.AccountCode   || item.account_code   || '',
                    taxType:     item.TaxType       || item.tax_type       || '',
                    lineAmount:  item.LineAmount    ?? item.line_amount    ?? 0,
                }));
                setRawLineItemsForReview(mapped);
            } catch (e) {
                console.warn('⚠️  Could not fetch raw transaction details:', e);
                setRawLineItemsForReview(null);
            }
        } catch (error) {
            console.error('❌ Failed to fetch data for review:', error);
            setReviewModalError('Failed to load schedule details');
        } finally {
            setReviewModalLoading(false);
        }
    };

    // Fetch accounts for the selected entity
    const fetchAccountsForEntity = async (entityId: string) => {
        console.log('🔍 Fetching accounts for entity:', entityId);
        try {
            const response = await api.getEntityAccounts(entityId);
            console.log('📊 Raw accounts response:', response);
            if (!response || !response.accounts) {
                throw new Error('No accounts in response');
            }
            const allAccounts = response.accounts;
            console.log('📋 All accounts:', allAccounts.length);
            // Filter accounts using shared utility
            const filteredAccounts = filterAccountsForPrepayments(allAccounts);
            setAvailableAccounts({ 
                amortization: filteredAccounts.amortization, 
                expense: filteredAccounts.expense 
            });
            console.log('✅ Filtered accounts set:', filteredAccounts);
        } catch (error) {
            console.error('❌ Error fetching accounts:', error);
            setAvailableAccounts({ amortization: [], expense: [] });
        }
    };

    // Handle editing schedule - opens EditScheduleModal
    const handleEditSchedule = async (line: AmortizableLineItem, invoice: InvoiceData) => {
        setFocusedMonthKey(undefined);
        setEditContext('line_item');
        setEditScheduleModalOpen(true); // Open modal immediately
        if (selectedEntityId && selectedEntityId !== 'all') {
            fetchAccountsForEntity(selectedEntityId); // Fetch accounts in background
        }
        try {
            const scheduleData = await PrepaymentsService.getSchedule(line.scheduleId);
            setSelectedScheduleForEdit(scheduleData);
        } catch (error) {
            setEditScheduleError('Failed to load schedule data');
            // fallback logic if needed
        }
    };

    // Handle saving schedule changes
    const handleSaveSchedule = async (scheduleId: string, updateData: ScheduleUpdateData) => {
        setEditScheduleLoading(true);
        setEditScheduleError(null);

        try {
            const result = await PrepaymentsService.updateSchedule(scheduleId, updateData);

            // Log status progression if it occurred
            if (result.status_progression) {
                console.log(`✅ Status automatically advanced from ${result.status_progression.from} to ${result.status_progression.to}`);
            }

            // Fetch the updated schedule data to get complete information
            try {
                const updatedSchedule = await PrepaymentsService.getSchedule(scheduleId);
                updateScheduleInGrid(scheduleId, updatedSchedule);
                console.log('✅ Grid updated in place with new schedule data');
            } catch (fetchError) {
                console.error('Failed to fetch updated schedule, falling back to full refresh:', fetchError);
                refreshData();
            }

            setEditScheduleModalOpen(false);
            setSelectedScheduleForEdit(null);
            setFocusedMonthKey(undefined); // Clear focused month

            return result;
        } catch (error) {
            console.error('Error saving schedule:', error);
            setEditScheduleError('Failed to save schedule');
        } finally {
            setEditScheduleLoading(false);
        }
    };

    // Handle confirming schedule
    const handleConfirmSchedule = async (scheduleId: string) => {
        setEditScheduleLoading(true);
        setEditScheduleError(null);

        try {
            await PrepaymentsService.confirmSchedule(scheduleId);
            
            // Fetch the updated schedule data and update grid in place
            try {
                const updatedSchedule = await PrepaymentsService.getSchedule(scheduleId);
                updateScheduleInGrid(scheduleId, updatedSchedule);
                console.log('✅ Grid updated in place after confirmation');
            } catch (fetchError) {
                console.error('Failed to fetch updated schedule, falling back to full refresh:', fetchError);
                refreshData();
            }
            
            setEditScheduleModalOpen(false);
            setSelectedScheduleForEdit(null);
            setFocusedMonthKey(undefined); // Clear focused month
        } catch (error) {
            console.error('Error confirming schedule:', error);
            setEditScheduleError('Failed to confirm schedule');
        } finally {
            setEditScheduleLoading(false);
        }
    };

    // Handle skipping schedule
    const handleSkipSchedule = async (scheduleId: string, reason: string) => {
        setEditScheduleLoading(true);
        setEditScheduleError(null);

        try {
            await PrepaymentsService.skipSchedule(scheduleId, reason);
            
            // Fetch the updated schedule data and update grid in place
            try {
                const updatedSchedule = await PrepaymentsService.getSchedule(scheduleId);
                updateScheduleInGrid(scheduleId, updatedSchedule);
                console.log('✅ Grid updated in place after skipping');
            } catch (fetchError) {
                console.error('Failed to fetch updated schedule, falling back to full refresh:', fetchError);
                refreshData();
            }
            
            setEditScheduleModalOpen(false);
            setSelectedScheduleForEdit(null);
            setFocusedMonthKey(undefined); // Clear focused month
        } catch (error) {
            console.error('Error skipping schedule:', error);
            setEditScheduleError('Failed to skip schedule');
        } finally {
            setEditScheduleLoading(false);
        }
    };

    // Handle invoice-level edit - opens BulkEditScheduleModal for all amortizable line items
    const handleInvoiceEdit = (invoiceId: string) => {
        console.log('📝 Opening bulk edit modal for invoice:', invoiceId);
        
        // Find the invoice and all its amortizable line items
        let targetInvoice: InvoiceData | null = null;
        
        // Search through all suppliers to find the invoice
        for (const supplier of dashboardSuppliers) {
            const invoice = supplier.invoices.find(inv => inv.invoiceId === invoiceId);
            if (invoice) {
                targetInvoice = invoice;
                break;
            }
        }
        
        if (!targetInvoice) {
            console.error('❌ Could not find invoice:', invoiceId);
            showToast.showError('Invoice not found', 'Could not find invoice data. Please try again.');
            return;
        }
        
        // Filter to only amortizable line items (not excluded or validation failed)
        const amortizableLines = targetInvoice.amortizableLineItems.filter(line => 
            line.overallStatus !== 'excluded' && line.overallStatus !== 'validation_failed'
        );
        
        if (amortizableLines.length === 0) {
            console.error('❌ No amortizable line items found for invoice:', invoiceId);
            showToast.showError('No amortizable items', 'No amortizable line items found for this invoice.');
            return;
        }
        
        // Convert to InvoiceData format for the bulk edit hook
        const invoiceData = {
            invoiceId: targetInvoice.invoiceId,
            reference: targetInvoice.reference,
            lineItems: amortizableLines.map(line => ({
                scheduleId: line.scheduleId,
                lineItemId: line.lineItemId,
                description: line.description,
                lineAmount: line.lineAmount,
                currentAmortizationAccountCode: line.amortizationAccountCode,
                currentExpenseAccountCode: line.expenseAccountCode,
                status: line.overallStatus
            }))
        };
        
        // Use the bulk edit hook to open modal
        openBulkModal(invoiceData);
    };


    const renderMonthlyCell = (line: AmortizableLineItem, monthKey: string, currencyCode: string = 'USD') => {
        const monthData = line.monthlyBreakdown[monthKey];
        if (!monthData) return <span className="text-muted-foreground">-</span>;
        
        // Use schedule status (line.overallStatus) instead of entry status (monthData.status)
        const scheduleStatus = line.overallStatus;
        const mappedStatus = mapBackendScheduleStatus(scheduleStatus);
        
        const getScheduleStatusStyles = () => {
            switch (mappedStatus) {
                case ScheduleStatus.PENDING_CONFIGURATION:
                    return { badgeVariant: 'outline', badgeClass: 'text-orange-600 border-orange-300 bg-orange-50', icon: <AlertTriangle className="h-3 w-3 mr-1" />, cellBg: 'bg-orange-50', cellBorder: 'border-orange-300' };
                case ScheduleStatus.PENDING_REVIEW:
                case ScheduleStatus.PROPOSED: // Legacy
                    return { badgeVariant: 'outline', badgeClass: 'text-yellow-600 border-yellow-300 bg-yellow-50', icon: <AlertTriangle className="h-3 w-3 mr-1" />, cellBg: 'bg-yellow-50', cellBorder: 'border-yellow-300' };
                case ScheduleStatus.PENDING_CONFIRMATION:
                    return { badgeVariant: 'outline', badgeClass: 'text-blue-600 border-blue-300 bg-blue-50', icon: <CheckCircle2 className="h-3 w-3 mr-1" />, cellBg: 'bg-blue-50', cellBorder: 'border-blue-300' };
                case ScheduleStatus.CONFIRMED:
                    return { badgeVariant: 'default', badgeClass: 'bg-blue-100 text-blue-700', icon: <CheckCircle2 className="h-3 w-3 mr-1" />, cellBg: 'bg-blue-50', cellBorder: 'border-blue-300' };
                case ScheduleStatus.POSTED:
                case ScheduleStatus.FULLY_POSTED: // Legacy
                    return { badgeVariant: 'default', badgeClass: 'bg-green-600 text-white', icon: <CheckCheck className="h-3 w-3 mr-1" />, cellBg: 'bg-green-50', cellBorder: 'border-green-300' };
                case ScheduleStatus.PARTIALLY_POSTED:
                    return { badgeVariant: 'default', badgeClass: 'bg-green-100 text-green-700', icon: <CheckCheck className="h-3 w-3 mr-1" />, cellBg: 'bg-green-50', cellBorder: 'border-green-300' };
                case ScheduleStatus.SKIPPED:
                    return { badgeVariant: 'secondary', badgeClass: 'text-gray-600', icon: <Ban className="h-3 w-3 mr-1" />, cellBg: 'bg-gray-50', cellBorder: 'border-gray-300' };
                case ScheduleStatus.CANCELLED:
                    return { badgeVariant: 'secondary', badgeClass: 'text-gray-600', icon: <X className="h-3 w-3 mr-1" />, cellBg: 'bg-gray-50', cellBorder: 'border-gray-300' };
                case ScheduleStatus.ERROR:
                case ScheduleStatus.VALIDATION_FAILED: // Legacy
                case ScheduleStatus.ERROR_POSTING: // Legacy
                    return { badgeVariant: 'destructive', badgeClass: '', icon: <XCircle className="h-3 w-3 mr-1" />, cellBg: 'bg-red-50', cellBorder: 'border-red-300' };
                case ScheduleStatus.EXCLUDED:
                    return { badgeVariant: 'secondary', badgeClass: 'text-gray-500', icon: <Ban className="h-3 w-3 mr-1" />, cellBg: 'bg-gray-50', cellBorder: 'border-gray-300' };
                default:
                    return { badgeVariant: 'secondary', badgeClass: '', icon: null, cellBg: 'bg-white', cellBorder: 'border-transparent' };
            }
        };

        const { badgeVariant, badgeClass, icon, cellBg, cellBorder } = getScheduleStatusStyles();
        
        // Enhanced tooltip with both schedule and entry information
        const entryStatus = monthData.status.charAt(0).toUpperCase() + monthData.status.slice(1).replace('_', ' ');
        const tooltipContent = `Schedule: ${scheduleStatus.replace('_', ' ')} | Entry: ${entryStatus} | Amount: ${formatCurrency(monthData.amount, currencyCode)}${monthData.journalId ? ` | Journal: ${monthData.journalId}` : ''}${monthData.error ? ` | Error: ${monthData.error}` : ''}`;
        
        // Display schedule status (not entry status)
        const displayStatus = scheduleStatus.charAt(0).toUpperCase() + scheduleStatus.slice(1).replace('_', ' ');
        const buttonPlaceholderHeight = 'h-6';
        
        // Check for entry-level errors to display
        const isError = monthData.status === 'posting_error' || !!monthData.error;

        return (
            <TooltipProvider delayDuration={200}>
                <Tooltip>
                    <TooltipTrigger asChild>
                        <div 
                            className={`relative flex flex-col items-center justify-center p-1.5 border ${cellBorder} ${cellBg} rounded h-[40px] w-full cursor-help transition-all hover:shadow-sm hover:border-gray-400`}
                        >
                            {/* Main amount - clean and compact */}
                            <div className="text-sm font-semibold text-center leading-tight">
                                {formatCurrency(monthData.amount, currencyCode)}
                            </div>
                            
                            {/* Simplified status indicator - small dot only */}
                            <div className={`absolute top-1 right-1 w-2 h-2 rounded-full`} style={{
                                backgroundColor: 
                                    mappedStatus === ScheduleStatus.PENDING_CONFIGURATION ? '#ea580c' :
                                    mappedStatus === ScheduleStatus.PENDING_REVIEW ? '#ca8a04' :
                                    mappedStatus === ScheduleStatus.PENDING_CONFIRMATION ? '#2563eb' :
                                    mappedStatus === ScheduleStatus.CONFIRMED ? '#1d4ed8' :
                                    mappedStatus === ScheduleStatus.POSTED ? '#16a34a' :
                                    mappedStatus === ScheduleStatus.PARTIALLY_POSTED ? '#22c55e' :
                                    mappedStatus === ScheduleStatus.SKIPPED ? '#6b7280' :
                                    mappedStatus === ScheduleStatus.ERROR ? '#dc2626' : '#9ca3af'
                            }} />
                            
                            {/* Error indicator if present - more subtle */}
                            {isError && (
                                <div className="absolute bottom-1 right-1">
                                    <XCircle className="h-3 w-3 text-red-500" />
                                </div>
                            )}
                            
                            {/* Journal posted indicator - simple checkmark */}
                            {monthData.journalId && !isError && (
                                <div className="absolute bottom-1 right-1">
                                    <CheckCircle2 className="h-3 w-3 text-green-600" />
                                </div>
                            )}
                        </div>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="max-w-xs">
                        <div className="space-y-1 text-sm">
                            <div className="font-semibold">Monthly Entry Details</div>
                            <div className="text-xs opacity-90">
                                <div>Amount: {formatCurrency(monthData.amount, currencyCode)}</div>
                                <div>Entry Status: {entryStatus}</div>
                                <div>Schedule Status: {displayStatus}</div>
                                {monthData.journalId && <div>Journal ID: {monthData.journalId}</div>}
                                {monthData.error && <div className="text-red-400">Error: {monthData.error}</div>}
                            </div>
                        </div>
                    </TooltipContent>
                </Tooltip>
            </TooltipProvider>
        );
    };

    // Note: Initial values are now handled by URL sync hook with proper precedence

    return (
        <>
            <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
                <SidebarTrigger className="-ml-1" />
                <Separator orientation="vertical" className="mr-2 h-4" />
                <Breadcrumb>
                    <BreadcrumbList>
                        <BreadcrumbItem className="hidden md:block">
                            <BreadcrumbLink
                                href="#"
                                onClick={(e) => {
                                    e.preventDefault();
                                    navigate('/dashboard');
                                }}
                                className="cursor-pointer"
                            >
                                {firmNameLoading ? 'Loading...' : firmName}
                            </BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator className="hidden md:block" />
                        <BreadcrumbItem>
                            <BreadcrumbLink
                                href="#"
                                onClick={(e) => {
                                    e.preventDefault();
                                    navigate('/accpay/all');
                                }}
                                className="cursor-pointer"
                            >
                                ACCPAY
                            </BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator className="hidden md:block" />
                        <BreadcrumbItem>
                            <BreadcrumbPage>Prepayments</BreadcrumbPage>
                        </BreadcrumbItem>
                    </BreadcrumbList>
                </Breadcrumb>
            </header>

            <div className="flex-1 overflow-auto">
                <div className="space-y-4 h-full flex flex-col p-4">
                    {/* Compact Filter Controls - All in One Line */}
                    <div className="flex flex-row gap-2 items-center p-2 border-b bg-white flex-shrink-0 rounded-lg shadow-sm">
            <ClientSelector
              clients={clients}
              selectedClientId={selectedClientId}
              onClientSelect={setClientId}
              isLoading={isLoadingClients}
              placeholder="Select Client"
              isHydrated={isHydrated}
            />
            
            <EntitySelector
              entities={entities}
              selectedEntityId={selectedEntityId}
              onEntitySelect={setEntityId}
              isLoading={isLoadingEntities}
              disabled={!selectedClientId}
              placeholder="Select Entity"
              showAllOption={true}
              clientName={clients?.find(c => c.clientId === selectedClientId)?.clientName}
              isHydrated={isHydrated}
            />

            <div className="flex-grow"></div>

            <div className="relative">
                 <Search className="absolute left-2 top-2 h-3 w-3 text-muted-foreground" />
                 <Input type="search" placeholder="Search suppliers..." value={supplierFilter} onChange={(e) => setSupplierFilter(e.target.value)} className="pl-6 w-[200px] h-8 text-xs" />
            </div>

            <StatusFilterSelector
              selectedStatusFilters={selectedStatusFilters}
              onStatusFiltersChange={setStatusFilters}
            />
            
            {/* Accessibility announcements for filter changes */}
            {announceMessage && (
                <div 
                    role="status"
                    aria-live="polite" 
                    aria-atomic="true" 
                    className="sr-only"
                    onAnimationEnd={clearAnnouncement}
                >
                    {announceMessage}
                </div>
            )}
        </div>

            <div className="border rounded-lg overflow-x-auto overflow-y-auto bg-white shadow-sm flex-grow">
                <Table ref={tableRef} className="min-w-full border-collapse" style={{ tableLayout: 'fixed' }}>
                     <colgroup>
                        <col style={{ width: `${DEFAULT_FIRST_COL_WIDTH}px` }} />
                        {monthColumns.map((month, index) => (
                            <col key={month.key} style={{ width: `${monthColWidths[month.key] || MIN_COL_WIDTH}px` }} />
                        ))}
                    </colgroup>
                    <TableHeader className="sticky top-0 z-10 bg-gray-50">
                        <TableRow>
                            <TableHead className="sticky left-0 z-20 bg-gray-50 text-xs uppercase text-muted-foreground border-r">
                                Supplier / Invoice / Line Item
                            </TableHead>
                            {monthColumns && monthColumns.map((month, index) => (
                                <TableHead key={month.key} className="text-center min-w-[90px] text-xs uppercase text-muted-foreground relative group border-l">
                                    {month.label}
                                    <div
                                        onMouseDown={(e) => handleMouseDown(index, e)}
                                        className="absolute top-0 right-0 w-2 h-full cursor-col-resize bg-transparent group-hover:bg-blue-200 opacity-0 group-hover:opacity-50"
                                        title="Resize column"
                                    >
                                      <GripVertical className="h-4 w-4 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-gray-400" />
                                    </div>
                                </TableHead>
                            ))}
                            <TableHead className="sticky right-0 z-20 bg-gray-50 text-xs uppercase text-muted-foreground border-l w-[120px]">
                                Actions
                            </TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {isLoadingData && ( <TableRow><TableCell colSpan={2 + (monthColumns?.length || 0)} className="h-24 text-center"><Loader2 className="h-6 w-6 animate-spin inline mr-2"/> Loading data...</TableCell></TableRow> )}
                        {!isLoadingData && (!dashboardSuppliers || dashboardSuppliers.length === 0) && ( <TableRow><TableCell colSpan={2 + (monthColumns?.length || 0)} className="h-24 text-center">No transactions found matching criteria.</TableCell></TableRow> )}

                        {!isLoadingData && dashboardSuppliers && dashboardSuppliers.map((supplier) => (
                            <React.Fragment key={supplier.supplierId}>
                                <TableRow className="bg-gray-50 hover:bg-gray-100 border-t border-b">
                                    <TableCell className="sticky left-0 z-10 bg-gray-50 font-semibold cursor-pointer border-r p-3" onClick={() => toggleSupplier(supplier.supplierId)}>
                                        <div className="flex items-center">
                                            {expandedSuppliers[supplier.supplierId] ? <ChevronDown className="h-4 w-4 inline mr-2 opacity-50" /> : <ChevronRight className="h-4 w-4 inline mr-2 opacity-50" />}
                                            <span>{supplier.supplierName}</span>
                                        </div>
                                    </TableCell>
                                    {monthColumns.map(month => <TableCell key={month.key}></TableCell>)}
                                    <TableCell className="sticky right-0 z-10 bg-gray-50 border-l p-2">
                                        <div className="flex justify-center">
                                            {supplier.overallStatus === SupplierStatus.ACTION_NEEDED && supplier.invoices.some(inv => inv.overallStatus === InvoiceStatus.ACTION_NEEDED) && (
                                                <TooltipProvider>
                                                    <Tooltip>
                                                        <TooltipTrigger asChild>
                                                            <Button variant="ghost" size="icon" className="h-6 w-6 text-green-600 hover:bg-green-100" onClick={(e) => { e.stopPropagation(); handleSupplierConfirmAll(supplier.supplierId, selectedEntityId, showToast); }}>
                                                                <CheckSquare className="h-4 w-4" />
                                                            </Button>
                                                        </TooltipTrigger>
                                                        <TooltipContent><p>Confirm all proposed for {supplier.supplierName}</p></TooltipContent>
                                                    </Tooltip>
                                                </TooltipProvider>
                                            )}
                                        </div>
                                    </TableCell>
                                </TableRow>
                                {expandedSuppliers[supplier.supplierId] && supplier.invoices?.map((invoice) => {
                                    const amortizableLineCount = invoice.amortizableLineItems.filter(l => l.overallStatus !== 'excluded' && l.overallStatus !== 'validation_failed').length;
                                    const totalAmortizableAmount = invoice.amortizableLineItems.reduce((sum, line) => (line.overallStatus !== 'excluded' && line.overallStatus !== 'validation_failed' ? sum + line.lineAmount : sum), 0);

                                    return (
                                    <React.Fragment key={invoice.invoiceId}>
                                        <TableRow className="hover:bg-muted/50">
                                             <TableCell className="sticky left-0 z-10 bg-white pl-8 cursor-pointer border-r p-2" onClick={() => toggleInvoice(invoice.invoiceId)}>
                                                <div className="flex items-center justify-between">
                                                    {/* Left: Invoice info */}
                                                    <div className="flex items-center gap-2">
                                                        {expandedInvoices[invoice.invoiceId] ? <ChevronDown className="h-4 w-4 opacity-50" /> : <ChevronRight className="h-4 w-4 opacity-50" />}
                                                        <span className="text-sm font-medium">{invoice.reference}</span>
                                                        {invoice.ocrWarningMessage && <TooltipProvider><Tooltip><TooltipTrigger asChild><AlertTriangle className="h-3 w-3 text-destructive" /></TooltipTrigger><TooltipContent><p>{invoice.ocrWarningMessage}</p></TooltipContent></Tooltip></TooltipProvider>}
                                                        
                                                    </div>
                                                    
                                                    {/* Right: Amounts */}
                                                    <div className="flex items-center gap-2 text-xs">
                                                        <TooltipProvider>
                                                            <Tooltip>
                                                                <TooltipTrigger asChild>
                                                                    <span className="font-medium">{formatCurrency(invoice.totalAmount, invoice.currencyCode)}</span>
                                                                </TooltipTrigger>
                                                                <TooltipContent><p>Total invoice amount</p></TooltipContent>
                                                            </Tooltip>
                                                        </TooltipProvider>
                                                        {amortizableLineCount > 0 && (
                                                            <TooltipProvider>
                                                                <Tooltip>
                                                                    <TooltipTrigger asChild>
                                                                        <span className="font-medium text-blue-600">{formatCurrency(totalAmortizableAmount, invoice.currencyCode)}</span>
                                                                    </TooltipTrigger>
                                                                    <TooltipContent><p>Amortizing {amortizableLineCount} line{amortizableLineCount !== 1 ? 's' : ''}</p></TooltipContent>
                                                                </Tooltip>
                                                            </TooltipProvider>
                                                        )}
                                                    </div>
                                                </div>
                                             </TableCell>
                                             {monthColumns.map(month => <TableCell key={month.key}></TableCell>)}
                                             <TableCell className="sticky right-0 z-10 bg-white border-l p-2">
                                                 <div className="flex justify-center space-x-1">
                                                     {(((invoice.overallStatus === InvoiceStatus.ACTION_NEEDED || invoice.overallStatus === InvoiceStatus.PARTIALLY_POSTED) && invoice.amortizableLineItems.some(l => isActionNeededStatus(l.overallStatus))) || invoice.amortizableLineItems.some(l => isConfirmableStatus(l.overallStatus))) && (
                                                        <>
                                                            <TooltipProvider>
                                                                <Tooltip>
                                                                    <TooltipTrigger asChild>
                                                                        <Button variant="ghost" size="icon" className="h-6 w-6 text-green-600 hover:bg-green-100" onClick={(e) => { e.stopPropagation(); handleInvoiceConfirmAll(invoice.invoiceId, showToast, bulkConfirmHandler, dashboardSuppliers, refreshData); }}>
                                                                            <CheckSquare className="h-4 w-4" />
                                                                        </Button>
                                                                    </TooltipTrigger>
                                                                    <TooltipContent><p>Confirm all proposed for invoice {invoice.reference}</p></TooltipContent>
                                                                </Tooltip>
                                                            </TooltipProvider>
                                                            <TooltipProvider>
                                                                <Tooltip>
                                                                    <TooltipTrigger asChild>
                                                                        <Button variant="ghost" size="icon" className="h-6 w-6 text-blue-600 hover:bg-blue-100" onClick={(e) => { e.stopPropagation(); handleInvoiceEdit(invoice.invoiceId); }}>
                                                                            <Edit className="h-4 w-4" />
                                                                        </Button>
                                                                    </TooltipTrigger>
                                                                    <TooltipContent><p>Bulk edit all amortizable line items for invoice {invoice.reference}</p></TooltipContent>
                                                                </Tooltip>
                                                            </TooltipProvider>
                                                            <TooltipProvider>
                                                                <Tooltip>
                                                                    <TooltipTrigger asChild>
                                                                        <Button
                                                                            variant="outline"
                                                                            size="sm"
                                                                            className="h-7 px-2 text-xs bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100 hover:border-purple-300 hover:text-purple-800"
                                                                            onClick={(e) => {
                                                                                e.stopPropagation();
                                                                                if (invoice.hasAttachment && invoice.attachmentId) {
                                                                                    handleViewAttachment(invoice.attachmentId!, invoice);
                                                                                } else {
                                                                                    showToast.showInfo('No attachment available', 'This invoice does not have an associated document to view.');
                                                                                }
                                                                            }}
                                                                            disabled={!invoice.hasAttachment || !invoice.attachmentId}
                                                                        >
                                                                            <FileText className="h-3 w-3 mr-1" />
                                                                            View
                                                                        </Button>
                                                                    </TooltipTrigger>
                                                                    <TooltipContent>
                                                                        <p>{invoice.hasAttachment && invoice.attachmentId ? 'View document attachment' : 'No attachment available'}</p>
                                                                    </TooltipContent>
                                                                </Tooltip>
                                                            </TooltipProvider>
                                                            <TooltipProvider>
                                                                <Tooltip>
                                                                    <TooltipTrigger asChild>
                                                                        <Button variant="ghost" size="icon" className="h-6 w-6 text-red-600 hover:bg-red-100" onClick={(e) => { e.stopPropagation(); handleInvoiceSkip(invoice.invoiceId, showToast); }}>
                                                                            <Ban className="h-4 w-4" />
                                                                        </Button>
                                                                    </TooltipTrigger>
                                                                    <TooltipContent><p>Skip all proposed schedules for invoice {invoice.reference}</p></TooltipContent>
                                                                </Tooltip>
                                                            </TooltipProvider>
                                                        </>
                                                     )}
                                                 </div>
                                             </TableCell>
                                        </TableRow>
                                         {expandedInvoices[invoice.invoiceId] && invoice.amortizableLineItems?.map((line) => (
                                             <TableRow key={line.lineItemId} className="hover:bg-muted/50">
                                                 <TableCell
                                                     className="sticky left-0 z-10 bg-white border-r p-2"
                                                     style={{ paddingLeft: '3rem' }}
                                                 >
                                                     <div className="flex items-center justify-between leading-tight py-1">
                                                         {/* Left: Description + DR/CR codes + Status */}
                                                         <div className="flex items-center gap-1.5 flex-1 min-w-0">
                                                             <TooltipProvider>
                                                                 <Tooltip>
                                                                     <TooltipTrigger asChild>
                                                                         <span className="text-xs font-medium leading-tight truncate cursor-help">
                                                                             {line.description}
                                                                         </span>
                                                                     </TooltipTrigger>
                                                                     <TooltipContent>
                                                                         <div className="text-sm">
                                                                             <div className="font-medium">{line.description}</div>
                                                                             {line.lineItemId && line.scheduleId && (
                                                                                 <div className="text-xs mt-1">
                                                                                     DR: {line.expenseAccountCode || 'Not configured'} | CR: {line.prepaymentAccountCode || 'Not configured'}
                                                                                 </div>
                                                                             )}
                                                                         </div>
                                                                     </TooltipContent>
                                                                 </Tooltip>
                                                             </TooltipProvider>
                                                             {/* Status badge without icons */}
                                                             <StatusBadge 
                                                                 status={line.overallStatus} 
                                                                 size="sm" 
                                                             />
                                                         </div>
                                                         
                                                         {/* Right: Total + Amortized amounts */}
                                                         <div className="flex items-center gap-2 flex-shrink-0">
                                                             <TooltipProvider>
                                                                 <Tooltip>
                                                                     <TooltipTrigger asChild>
                                                                         <span className="text-xs font-semibold">{formatCurrency(line.lineAmount, invoice.currencyCode)}</span>
                                                                     </TooltipTrigger>
                                                                     <TooltipContent><p>Total line amount</p></TooltipContent>
                                                                 </Tooltip>
                                                             </TooltipProvider>
                                                             {(() => {
                                                                 const totalAmortized = Object.values(line.monthlyBreakdown).reduce((sum, month) => {
                                                                     // Only count amounts that have been actually posted (have journalId)
                                                                     return month.journalId ? sum + (month.amount || 0) : sum;
                                                                 }, 0);
                                                                 return totalAmortized > 0 ? (
                                                                     <TooltipProvider>
                                                                         <Tooltip>
                                                                             <TooltipTrigger asChild>
                                                                                 <span className="text-xs font-medium text-blue-600">
                                                                                     {formatCurrency(totalAmortized, invoice.currencyCode)}
                                                                                 </span>
                                                                             </TooltipTrigger>
                                                                             <TooltipContent><p>Amount posted to accounting system</p></TooltipContent>
                                                                         </Tooltip>
                                                                     </TooltipProvider>
                                                                 ) : null;
                                                             })()}
                                                         </div>
                                                     </div>
                                                 </TableCell>
                                                 {monthColumns && monthColumns.map(month => ( <TableCell key={month.key} className="text-center p-0.5 align-top min-w-[90px]">{renderMonthlyCell(line, month.key, invoice.currencyCode)}</TableCell>))}
                                                 <TableCell className="sticky right-0 z-10 bg-white border-l p-2">
                                                     <div className="flex justify-center space-x-1">
                                                         {/* Line Item Action Buttons */}
                                                         {isActionNeededStatus(line.overallStatus) && (
                                                             <>
                                                                 {isConfirmableStatus(line.overallStatus) &&
                                                                     <TooltipProvider><Tooltip><TooltipTrigger asChild><Button variant="ghost" size="icon" className="h-6 w-6 text-green-600 hover:bg-green-100" disabled={loadingSchedules[line.scheduleId] === 'confirming'} onClick={() => handleLineConfirm(line.scheduleId, updateScheduleInGrid, refreshData, showToast, setLoadingSchedules)}>{loadingSchedules[line.scheduleId] === 'confirming' ? <Loader2 className="h-4 w-4 animate-spin" /> : <CheckSquare className="h-4 w-4" />}</Button></TooltipTrigger><TooltipContent><p>Confirm Schedule</p></TooltipContent></Tooltip></TooltipProvider>
                                                                 }
                                                                 {isEditableStatus(line.overallStatus) &&
                                                                     <TooltipProvider><Tooltip><TooltipTrigger asChild><Button variant="ghost" size="icon" className="h-6 w-6 text-orange-600 hover:bg-orange-100" onClick={() => handleEditSchedule(line, invoice)}><Edit className="h-4 w-4" /></Button></TooltipTrigger><TooltipContent><p>Edit Schedule</p></TooltipContent></Tooltip></TooltipProvider>
                                                                 }
                                                                 {isSkippableStatus(line.overallStatus) &&
                                                                     <TooltipProvider><Tooltip><TooltipTrigger asChild><Button variant="ghost" size="icon" className="h-6 w-6 text-red-600 hover:bg-red-100" disabled={loadingSchedules[line.scheduleId] === 'skipping'} onClick={() => handleLineSkip(line.scheduleId, updateScheduleInGrid, refreshData, showToast, openDialog, setLoadingSchedules)}>{loadingSchedules[line.scheduleId] === 'skipping' ? <Loader2 className="h-4 w-4 animate-spin" /> : <Ban className="h-4 w-4" />}</Button></TooltipTrigger><TooltipContent><p>Skip Schedule</p></TooltipContent></Tooltip></TooltipProvider>
                                                                 }
                                                             </>
                                                         )}
                                                     </div>
                                                 </TableCell>
                                             </TableRow>
                                         ))}
                                    </React.Fragment>
                                    );
                                })}
                            </React.Fragment>
                        ))}
                    </TableBody>
                </Table>
            </div>
             {!isLoadingData && pagination.totalPages > 1 && (
                 <div className="flex items-center justify-between py-6 mx-4 flex-shrink-0">
                    <div className="text-sm text-muted-foreground">
                        Page {pagination.currentPage} of {pagination.totalPages} ({pagination.totalItems} items)
                    </div>
                    <Pagination>
                        <PaginationContent>
                            <PaginationItem>
                                <PaginationPrevious
                                    onClick={() => setPagination(p => ({...p, currentPage: p.currentPage - 1}))}
                                    className={pagination.currentPage <= 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                                />
                            </PaginationItem>

                            {/* First page */}
                            {pagination.currentPage > 2 && (
                                <PaginationItem>
                                    <PaginationLink
                                        onClick={() => setPagination(p => ({...p, currentPage: 1}))}
                                        className="cursor-pointer"
                                    >
                                        1
                                    </PaginationLink>
                                </PaginationItem>
                            )}

                            {/* Ellipsis before current page */}
                            {pagination.currentPage > 3 && (
                                <PaginationItem>
                                    <PaginationEllipsis />
                                </PaginationItem>
                            )}

                            {/* Previous page */}
                            {pagination.currentPage > 1 && (
                                <PaginationItem>
                                    <PaginationLink
                                        onClick={() => setPagination(p => ({...p, currentPage: p.currentPage - 1}))}
                                        className="cursor-pointer"
                                    >
                                        {pagination.currentPage - 1}
                                    </PaginationLink>
                                </PaginationItem>
                            )}

                            {/* Current page */}
                            <PaginationItem>
                                <PaginationLink isActive className="cursor-default">
                                    {pagination.currentPage}
                                </PaginationLink>
                            </PaginationItem>

                            {/* Next page */}
                            {pagination.currentPage < pagination.totalPages && (
                                <PaginationItem>
                                    <PaginationLink
                                        onClick={() => setPagination(p => ({...p, currentPage: p.currentPage + 1}))}
                                        className="cursor-pointer"
                                    >
                                        {pagination.currentPage + 1}
                                    </PaginationLink>
                                </PaginationItem>
                            )}

                            {/* Ellipsis after current page */}
                            {pagination.currentPage < pagination.totalPages - 2 && (
                                <PaginationItem>
                                    <PaginationEllipsis />
                                </PaginationItem>
                            )}

                            {/* Last page */}
                            {pagination.currentPage < pagination.totalPages - 1 && (
                                <PaginationItem>
                                    <PaginationLink
                                        onClick={() => setPagination(p => ({...p, currentPage: pagination.totalPages}))}
                                        className="cursor-pointer"
                                    >
                                        {pagination.totalPages}
                                    </PaginationLink>
                                </PaginationItem>
                            )}

                            <PaginationItem>
                                <PaginationNext
                                    onClick={() => setPagination(p => ({...p, currentPage: p.currentPage + 1}))}
                                    className={pagination.currentPage >= pagination.totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                                />
                            </PaginationItem>
                        </PaginationContent>
                    </Pagination>
                 </div>
             )}

            {/* SideBySideReview Modal */}
            <SideBySideReview
                isOpen={reviewModalOpen}
                onClose={() => {
                    setReviewModalOpen(false);
                    setSelectedScheduleForReview(null);
                    setRawLineItemsForReview(null);
                }}
                transactionId={selectedInvoiceForReview?.invoiceId || null}
                transactionData={selectedInvoiceForReview ? {
                    transactionId: selectedInvoiceForReview.invoiceId,
                    reference: selectedInvoiceForReview.reference,
                    counterpartyName: dashboardSuppliers.find(s =>
                        s.invoices.some(inv => inv.invoiceId === selectedInvoiceForReview.invoiceId)
                    )?.supplierName || 'Unknown Supplier',
                    hasAttachment: selectedInvoiceForReview.hasAttachment,
                    attachmentId: selectedInvoiceForReview.attachmentId,
                    lineItems: rawLineItemsForReview ?? selectedInvoiceForReview.amortizableLineItems.map(line => ({
                        lineItemId: line.lineItemId,
                        description: line.description,
                        lineAmount: line.lineAmount,
                        accountCode: line.prepaymentAccountCode || line.expenseAccountCode || '',
                    })),
                    currencyCode: selectedInvoiceForReview.currencyCode,
                    xeroUrl: `https://go.xero.com/AccountsPayable/View.aspx?InvoiceID=${selectedInvoiceForReview.invoiceId}`
                } : null}
                scheduleData={selectedScheduleForReview ? (() => {
                    // Helper function to get account display name
                    const getAccountDisplay = (code: string, accountType: 'amortization' | 'expense') => {
                        const accounts = accountType === 'amortization' ? availableAccounts.amortization : availableAccounts.expense;
                        const account = accounts.find(acc => acc.code === code);
                        return account ? `${code} - ${account.name}` : code;
                    };
                    
                    return {
                        status: (() => {
                            switch (selectedScheduleForReview.status) {
                                case 'pending_configuration':
                                case 'proposed':
                                case 'confirmed':
                                    return 'proposed';
                                case 'posted':
                                    return 'fully_posted';
                                case 'partially_posted':
                                    return 'partially_posted';
                                case 'skipped':
                                    return 'skipped';
                                case 'error':
                                    return 'error_posting';
                                default:
                                    return 'proposed';
                            }
                        })(),
                        originalAmount: selectedScheduleForReview.originalAmount,
                        amortizationStartDate: selectedScheduleForReview.amortizationStartDate,
                        amortizationEndDate: selectedScheduleForReview.amortizationEndDate,
                        numberOfPeriods: selectedScheduleForReview.numberOfPeriods,
                        amortizationAccountCode: getAccountDisplay(selectedScheduleForReview.amortizationAccountCode, 'amortization'),
                        expenseAccountCode: selectedScheduleForReview.expenseAccountCode ? 
                            getAccountDisplay(selectedScheduleForReview.expenseAccountCode, 'expense') : null,
                        monthlyEntries: selectedScheduleForReview.monthly_entries || []
                    };
                })() : null}
                attachmentUrl={null}
                isLoading={reviewModalLoading}
                error={reviewModalError}
                entityId={selectedEntityId !== 'all' ? selectedEntityId : undefined}
            />

            {/* EditScheduleModal */}
            <EditScheduleModal
                isOpen={editScheduleModalOpen}
                onClose={() => {
                    setEditScheduleModalOpen(false);
                    setFocusedMonthKey(undefined); // Clear focused month on close
                    setEditContext('line_item'); // Reset context to default
                }}
                scheduleData={selectedScheduleForEdit}
                availableAmortizationAccounts={availableAccounts.amortization}
                availableExpenseAccounts={availableAccounts.expense}
                onSave={handleSaveSchedule}
                onConfirm={handleConfirmSchedule}
                onSkip={handleSkipSchedule}
                isSaving={editScheduleLoading}
                saveError={editScheduleError}
                context={editContext}
                focusedMonthKey={focusedMonthKey}
            />

            {/* BulkEditScheduleModal */}
            <BulkEditScheduleModal
                isOpen={bulkEditModalOpen}
                onClose={closeBulkModal}
                mode="edit"
                invoiceData={selectedInvoiceForBulkEdit}
                availableAmortizationAccounts={availableAccounts.amortization}
                availableExpenseAccounts={availableAccounts.expense}
                onBulkSave={bulkSaveHandler}
                onBulkConfirm={bulkConfirmHandler}
                onBulkSkip={bulkSkipHandler}
                isSaving={bulkEditLoading}
                saveError={bulkEditError}
            />
            
            {/* Confirmation Dialog */}
            {dialogElement}
                    </div>
            </div>
        </>
    );
}

export function PrepaymentsPage() {
    const navigate = useNavigate();
    const location = useLocation();
    const { user } = useAuthStore();
    const { firmName, isLoading: firmNameLoading } = useFirmName();

    // Parse query params
    const searchParams = new URLSearchParams(location.search);
    const clientIdFromQuery = searchParams.get('clientId') || searchParams.get('client_id');
    const entityIdFromQuery = searchParams.get('entityId') || searchParams.get('entity_id');
    let statusFiltersFromQuery: string[] | null = null;
    const statusFilterParam = searchParams.get('status_filter') || searchParams.get('status');
    if (statusFilterParam) {
        // Support comma-separated or single value
        statusFiltersFromQuery = statusFilterParam.split(',').map(s => s.trim()).filter(Boolean);
    }

    return (
        <PrepaymentsGrid
            initialClientId={clientIdFromQuery || null}
            initialEntityId={entityIdFromQuery || null}
            initialStatusFilters={statusFiltersFromQuery || null}
            currentUser={{
                userId: user?.uid || "unknown",
                displayName: user?.displayName || "User",
                email: user?.email || "",
                firmName: firmName || "Firm",
                role: "firm_admin"
            }}
        />
    );
}
