import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Loader2,
  FileText,
  Building,
  Calendar,
  DollarSign,
  Plus,
  MessageSquare,
} from 'lucide-react';
import { 
  getRecommendationConfig, 
  getConfidenceRange, 
  formatConfidenceScore 
} from '@/constants/transaction-filters';

interface Transaction {
  id: string;
  document_number: string;
  type: string;
  contact_id: string;
  date_issued: string;
  date_due: string;
  total: number;
  amount_due: number;
  amount_paid: number;
  currency: string;
  status: string;
  has_amortization_schedules?: boolean;
  skip_reason?: string;
  metadata: {
    contact_name: string;
    recommended_action?: string;
    confidence_score?: number;
    has_prepayment_line_items?: boolean;
    gl_based_analysis_completed?: boolean;
    llm_based_analysis_completed?: boolean;
    llm_detections?: any[];
    detection_methods?: any[];
    prepayment_line_items?: any[];
    skip_reason?: string;
  };
  line_items: Array<{
    id: string | number;
    description: string;
    amount?: number;
    unit_amount: number;
    account_code?: string;
    account_id?: string;
    quantity: number;
    tax_amount?: number;
    tax_type?: string;
  }>;
  notes: string;
  source: string;
  source_id: string;
  subtotal: number;
  tax_total: number;
  attachment_id?: string;
  has_attachments?: boolean;
  created_at: string;
  updated_at: string;
}

interface TransactionTableProps {
  transactions: Transaction[];
  isLoading: boolean;
  onCreatePrepayment: (transaction: Transaction) => void;
  onOpenComments?: (transactionId: string) => void;
}

const getStatusBadge = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'authorised':
      return <Badge variant="default" className="bg-green-100 text-green-700">Authorised</Badge>;
    case 'paid':
      return <Badge variant="default" className="bg-blue-100 text-blue-700">Paid</Badge>;
    case 'draft':
      return <Badge variant="outline">Draft</Badge>;
    default:
      return <Badge variant="secondary">{status}</Badge>;
  }
};

const getRecommendationBadge = (transaction: Transaction) => {
  // Handle skipped transactions
  if (transaction.skip_reason || transaction.metadata?.skip_reason) {
    const config = getRecommendationConfig('skipped');
    return (
      <Badge variant={config.badgeVariant} className={config.badgeClassName}>
        {config.label}
      </Badge>
    );
  }
  
  const action = transaction.metadata?.recommended_action;
  if (!action) {
    const config = getRecommendationConfig('analysis_failed');
    return (
      <Badge variant={config.badgeVariant} className={config.badgeClassName}>
        {config.label}
      </Badge>
    );
  }
  
  const config = getRecommendationConfig(action);
  return (
    <Badge variant={config.badgeVariant} className={config.badgeClassName}>
      {config.label}
    </Badge>
  );
};

const getConfidenceDisplay = (score: number) => {
  if (!score || score === 0) return null;
  
  const formattedScore = formatConfidenceScore(score);
  const range = getConfidenceRange(score);
  
  return (
    <span className={`text-xs ${range.color}`}>
      Confidence: {formattedScore}
    </span>
  );
};

const getSkipReasonDisplay = (skipReason: string) => {
  if (!skipReason) return null;
  
  const reasonMap: Record<string, string> = {
    'below_scanning_threshold': 'Below scanning threshold',
    'existing_release_journal_detected': 'Existing release journal detected',
    'all_line_items_excluded': 'All line items excluded'
  };
  
  const displayReason = reasonMap[skipReason] || skipReason.replace(/_/g, ' ');
  
  return (
    <span className="text-xs text-gray-600">
      {displayReason}
    </span>
  );
};

const shouldShowCreateButton = (transaction: Transaction): boolean => {
  // Show button if:
  // 1. No existing schedules AND
  // 2. Analysis completed (either GL or LLM) AND
  // 3. Recommendation is 'no_prepayment' (user can override AI decision)
  const metadata = transaction.metadata;
  if (!metadata) return false;
  
  const hasAnalysis = metadata.gl_based_analysis_completed === true || metadata.llm_based_analysis_completed === true;
  
  return (
    !transaction.has_amortization_schedules &&
    hasAnalysis &&
    metadata.recommended_action === 'no_prepayment'
  );
};

export function TransactionTable({ transactions, isLoading, onCreatePrepayment, onOpenComments }: TransactionTableProps) {
  const navigate = useNavigate();

  return (
    <div className="border rounded-lg overflow-auto bg-white shadow-sm flex-grow">
      <Table>
        <TableHeader className="sticky top-0 z-10 bg-gray-50">
          <TableRow>
            <TableHead className="text-xs uppercase text-muted-foreground">Supplier</TableHead>
            <TableHead className="text-xs uppercase text-muted-foreground">Document</TableHead>
            <TableHead className="text-xs uppercase text-muted-foreground">Date</TableHead>
            <TableHead className="text-xs uppercase text-muted-foreground">Amount</TableHead>
            <TableHead className="text-xs uppercase text-muted-foreground">Status</TableHead>
            <TableHead className="text-xs uppercase text-muted-foreground">Recommendation</TableHead>
            <TableHead className="text-xs uppercase text-muted-foreground">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading && (
            <TableRow>
              <TableCell colSpan={7} className="h-24 text-center">
                <Loader2 className="h-6 w-6 animate-spin inline mr-2"/> 
                Loading transactions...
              </TableCell>
            </TableRow>
          )}
          
          {!isLoading && (!transactions || transactions.length === 0) && (
            <TableRow>
              <TableCell colSpan={7} className="h-24 text-center">
                No ACCPAY transactions found.
              </TableCell>
            </TableRow>
          )}
          
          {!isLoading && transactions && transactions.map((transaction) => (
            <TableRow key={transaction.id} className="hover:bg-muted/50">
              <TableCell>
                <div className="flex items-center">
                  <Building className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>{transaction.metadata?.contact_name || 'Unknown Supplier'}</span>
                </div>
              </TableCell>
              
              <TableCell>
                <div className="flex flex-col">
                  <span className="font-medium">{transaction.document_number || 'N/A'}</span>
                  <span className="text-xs text-muted-foreground">{transaction.type || 'N/A'}</span>
                  {transaction.has_amortization_schedules && (
                    <Badge variant="outline" className="mt-1 w-fit text-xs">
                      Has Schedules
                    </Badge>
                  )}
                </div>
              </TableCell>
              
              <TableCell>
                <div className="flex flex-col">
                  <div className="flex items-center text-sm">
                    <Calendar className="h-4 w-4 mr-1 text-muted-foreground" />
                    {transaction.date_issued ? new Date(transaction.date_issued).toLocaleDateString() : 'N/A'}
                  </div>
                  {transaction.date_due && (
                    <span className="text-xs text-muted-foreground">
                      Due: {new Date(transaction.date_due).toLocaleDateString()}
                    </span>
                  )}
                </div>
              </TableCell>
              
              <TableCell>
                <div className="flex items-center">
                  <div className="flex flex-col">
                    <span className="font-medium">
                      {(transaction.total || 0).toFixed(2)} {transaction.currency || 'USD'}
                    </span>
                    {(transaction.amount_due || 0) > 0 && (
                      <span className="text-xs text-muted-foreground">
                        Due: {(transaction.amount_due || 0).toFixed(2)}
                      </span>
                    )}
                  </div>
                </div>
              </TableCell>
              
              <TableCell>
                {getStatusBadge(transaction.status)}
              </TableCell>
              
              <TableCell>
                <div className="flex flex-col gap-1">
                  {getRecommendationBadge(transaction)}
                  {(transaction.skip_reason || transaction.metadata?.skip_reason) ? 
                    getSkipReasonDisplay(transaction.skip_reason || transaction.metadata?.skip_reason) :
                    getConfidenceDisplay(transaction.metadata?.confidence_score || 0)
                  }
                </div>
              </TableCell>
              
              <TableCell>
                <div className="flex items-center gap-2">
                  {transaction.has_amortization_schedules ? (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => navigate(`/prepayments?transaction_id=${transaction.id}`)}
                      className="text-blue-600 border-blue-300 hover:bg-blue-50"
                    >
                      <FileText className="h-4 w-4 mr-1" />
                      View Schedules
                    </Button>
                  ) : shouldShowCreateButton(transaction) ? (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        onCreatePrepayment(transaction);
                      }}
                      className="text-green-600 border-green-300 hover:bg-green-50"
                      title="Override AI recommendation and create prepayment schedule"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Create Schedule
                    </Button>
                  ) : (
                    <span className="text-xs text-muted-foreground px-2">
                      {/* Empty cell for actions */}
                    </span>
                  )}
                  
                  {/* Comment button - always visible */}
                  {onOpenComments && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        onOpenComments(transaction.id);
                      }}
                      className="text-muted-foreground hover:text-foreground"
                      title="View and add comments for this transaction"
                    >
                      <MessageSquare className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}