"""
Error Handling Utilities

Centralized error handling and logging for the email poller
"""

import os
import logging
from typing import Dict, Any
from datetime import datetime
from google.cloud import firestore

logger = logging.getLogger(__name__)

class ErrorHandler:
    """Centralized error handling and logging"""
    
    def __init__(self):
        project_id = os.getenv("GCP_PROJECT_ID")
        if project_id:
            try:
                self.db = firestore.Client(project=project_id)
                self.firestore_enabled = True
            except Exception:
                self.firestore_enabled = False
                logger.warning("Firestore not available for error logging")
        else:
            self.firestore_enabled = False
    
    def log_error(self, error_type: str, error_message: str, context: Dict[str, Any] = None) -> None:
        """
        Log error with context information
        
        Args:
            error_type: Type of error (e.g., 'polling_failure', 'api_error')
            error_message: Error message
            context: Additional context information
        """
        
        # Always log to standard logging
        logger.error(f"[{error_type}] {error_message} | Context: {context or {}}")
        
        # Also log to Firestore for monitoring if available
        if self.firestore_enabled:
            try:
                self._log_to_firestore(error_type, error_message, context)
            except Exception as e:
                logger.warning(f"Failed to log error to Firestore: {str(e)}")
    
    def _log_to_firestore(self, error_type: str, error_message: str, context: Dict[str, Any] = None) -> None:
        """Log error to Firestore for monitoring"""
        
        error_doc = {
            'error_type': error_type,
            'error_message': error_message,
            'context': context or {},
            'timestamp': datetime.now(),
            'service': 'nylas_email_poller',
            'severity': 'error'
        }
        
        # Store in SYSTEM_LOGS collection for monitoring
        self.db.collection('SYSTEM_LOGS').add(error_doc)
    
    def log_warning(self, warning_type: str, warning_message: str, context: Dict[str, Any] = None) -> None:
        """
        Log warning with context information
        
        Args:
            warning_type: Type of warning
            warning_message: Warning message  
            context: Additional context information
        """
        
        logger.warning(f"[{warning_type}] {warning_message} | Context: {context or {}}")
        
        if self.firestore_enabled:
            try:
                warning_doc = {
                    'warning_type': warning_type,
                    'warning_message': warning_message,
                    'context': context or {},
                    'timestamp': datetime.now(),
                    'service': 'nylas_email_poller',
                    'severity': 'warning'
                }
                
                self.db.collection('SYSTEM_LOGS').add(warning_doc)
            except Exception as e:
                logger.warning(f"Failed to log warning to Firestore: {str(e)}")
    
    def log_info(self, info_type: str, info_message: str, context: Dict[str, Any] = None) -> None:
        """
        Log informational message
        
        Args:
            info_type: Type of info
            info_message: Info message
            context: Additional context information
        """
        
        logger.info(f"[{info_type}] {info_message} | Context: {context or {}}")
        
        # Only log significant info events to Firestore to avoid spam
        if info_type in ['polling_success', 'batch_processed', 'system_health']:
            if self.firestore_enabled:
                try:
                    info_doc = {
                        'info_type': info_type,
                        'info_message': info_message,
                        'context': context or {},
                        'timestamp': datetime.now(),
                        'service': 'nylas_email_poller',
                        'severity': 'info'
                    }
                    
                    self.db.collection('SYSTEM_LOGS').add(info_doc)
                except Exception:
                    pass  # Don't fail on info logging errors