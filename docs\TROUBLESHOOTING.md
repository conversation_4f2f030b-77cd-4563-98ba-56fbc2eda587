# Troubleshooting Guide

*Last updated: 2025-01-15*

## Common Issues & Solutions

### Authentication Issues

#### 1. "Firebase configuration not found"
**Symptoms:** Login page shows Firebase configuration errors
**Cause:** Missing or incorrect environment variables

**Solution:**
```bash
# Check .env file exists and has correct variables
cat .env

# Required variables (all must start with VITE_):
VITE_FIREBASE_API_KEY=your-api-key
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
VITE_FIREBASE_APP_ID=1:123456789:web:abcdef123456
```

#### 2. "401 Unauthorized" on API calls
**Symptoms:** API requests fail with 401 error
**Cause:** Token expired or invalid

**Solution:**
1. Check browser console for authentication errors
2. Verify user is logged in: `auth.currentUser` should not be null
3. Check token manually in Network tab
4. Try logging out and back in

**Debug Steps:**
```typescript
// Add to component for debugging
import { auth } from '@/lib/firebase';

useEffect(() => {
  const checkAuth = async () => {
    const user = auth.currentUser;
    if (user) {
      const token = await user.getIdToken();
      console.log('User:', user.uid);
      console.log('Token (first 20 chars):', token.substring(0, 20));
    } else {
      console.log('No user logged in');
    }
  };
  checkAuth();
}, []);
```

#### 3. Infinite login redirect loop
**Symptoms:** Page keeps redirecting to login
**Cause:** Firebase auth state not properly initialized

**Solution:**
```typescript
// Check AuthProvider initialization
const { user, isLoading } = useAuthStore();

// Don't render protected content until auth is loaded
if (isLoading) return <LoadingSpinner />;
```

### API & Network Issues

#### 1. "Network request failed" or connection errors
**Symptoms:** API calls fail with network errors
**Cause:** Backend server not running or incorrect URL

**Solution:**
1. Verify backend server is running on correct port
2. Check API base URL in environment variables
3. Test API directly with curl:
```bash
curl http://localhost:8081/health
```

#### 2. Slow API responses (>5 seconds)
**Symptoms:** Long loading times, timeouts
**Cause:** Various network or server issues

**Solution:**
1. Check browser Network tab for slow requests
2. Verify API timeout settings (default: 15 seconds)
3. Check server logs for performance issues
4. Consider disabling cache: `{ cache: false }`

#### 3. CORS errors
**Symptoms:** "Access-Control-Allow-Origin" errors
**Cause:** Backend CORS configuration

**Solution:**
1. Verify backend CORS settings allow frontend origin
2. Check if running on correct ports (frontend: 3000, backend: 8081)
3. Ensure credentials are properly configured

### Data & State Issues

#### 1. Empty data or "No data found"
**Symptoms:** Components show empty state when data should exist
**Cause:** API filters, permissions, or data transformation issues

**Debug Steps:**
```typescript
// Add logging to service calls
const fetchData = async () => {
  console.log('Fetching data with filters:', filters);
  const result = await SomeService.getData(filters);
  console.log('API response:', result);
  console.log('Transformed data:', transformedData);
};
```

**Common Causes:**
- Incorrect client_id or entity_id in filters
- Status filters too restrictive
- Data transformation issues
- Cache serving stale data

#### 2. Stale data / cache issues
**Symptoms:** Data doesn't update after changes
**Cause:** API caching or local state not updating

**Solution:**
1. Clear API cache: `api.clearCache()`
2. Disable caching temporarily: `{ cache: false }`
3. Force refresh by changing dependency array
4. Check local state updates

#### 3. State synchronization issues
**Symptoms:** UI doesn't reflect actual data state
**Cause:** State management issues

**Solution:**
```typescript
// Check Zustand store state
const store = useAuthStore.getState();
console.log('Current store state:', store);

// Force state update
useAuthStore.setState({ user: newUser });
```

#### 4. "Unknown or unhandled status" errors in console
**Symptoms:** Backend logs show "Unknown or unhandled status 'status_name' for schedule ID. Defaulting to ERROR."
**Cause:** Backend transformers.py missing status mapping for new schedule statuses

**Solution:**
1. Check if status exists in backend ScheduleStatus enum
2. Add missing status mapping in `rest_api/utils/transformers.py`:
```python
elif normalized_status == schedule_models.ScheduleStatus.NEW_STATUS.value:
    api_status = schedule_models.ScheduleStatus.NEW_STATUS
```
3. Common missing statuses: `skipped`, `excluded`, `partially_posted`
4. Deploy backend after adding mapping

**Prevention:** When adding new schedule statuses, always update both the enum and transformers mapping.

### Component & UI Issues

#### 1. "Component not found" or import errors
**Symptoms:** Import errors in components
**Cause:** Incorrect import paths or missing components

**Solution:**
1. Check component exists at specified path
2. Verify import syntax:
```typescript
// ✅ Correct
import { Button } from '@/components/ui/button';

// ❌ Incorrect
import { Button } from '@/components/Button';
```

#### 2. Styling issues / Tailwind not working
**Symptoms:** Components don't have expected styling
**Cause:** Tailwind CSS configuration or build issues

**Solution:**
1. Verify Tailwind classes are valid
2. Check `tailwind.config.js` configuration
3. Restart development server
4. Use browser dev tools to inspect applied styles

#### 3. Form validation errors
**Symptoms:** Forms don't validate properly
**Cause:** Zod schema or form configuration issues

**Debug Steps:**
```typescript
// Check form state
const form = useForm({...});
console.log('Form errors:', form.formState.errors);
console.log('Form values:', form.getValues());

// Test schema manually
const result = schema.safeParse(formData);
console.log('Schema validation:', result);
```

### Performance Issues

#### 1. Slow page loading
**Symptoms:** Pages take >3 seconds to load
**Cause:** Large bundles, unnecessary re-renders, or API delays

**Solution:**
1. Check bundle size: `npm run build`
2. Use React DevTools Profiler
3. Implement code splitting:
```typescript
const LazyComponent = lazy(() => import('./HeavyComponent'));
```

#### 2. Memory leaks
**Symptoms:** Browser becomes sluggish over time
**Cause:** Event listeners, timers, or subscriptions not cleaned up

**Solution:**
```typescript
// Clean up in useEffect
useEffect(() => {
  const interval = setInterval(() => {
    // Do something
  }, 1000);

  return () => clearInterval(interval);
}, []);
```

### Development Environment Issues

#### 1. Hot reload not working
**Symptoms:** Changes don't reflect without manual refresh
**Cause:** Vite HMR issues

**Solution:**
1. Restart development server
2. Check for syntax errors in console
3. Clear browser cache
4. Verify file changes are saved

#### 2. TypeScript errors
**Symptoms:** TypeScript compilation errors
**Cause:** Type mismatches or missing types

**Solution:**
1. Check TypeScript errors in terminal
2. Verify import statements and types
3. Update type definitions if needed
4. Use `// @ts-ignore` as temporary workaround

#### 3. Build failures
**Symptoms:** `npm run build` fails
**Cause:** Various build-time issues

**Solution:**
1. Check build logs for specific errors
2. Verify all dependencies are installed
3. Fix TypeScript errors
4. Check for circular dependencies

## Debugging Tools & Techniques

### Browser Developer Tools

#### Console Debugging
```typescript
// Add strategic console logs
console.log('Component rendered with props:', props);
console.log('API call result:', result);
console.log('State updated:', newState);

// Use console.table for arrays
console.table(data);

// Use console.trace for call stacks
console.trace('Function called from:');
```

#### Network Tab
1. Check API request/response details
2. Verify request headers include auth token
3. Check response status codes and timing
4. Monitor for failed requests

#### React Developer Tools
1. Install React DevTools browser extension
2. Use Profiler to identify performance issues
3. Inspect component state and props
4. Check context providers and consumers

### Service Layer Debugging

#### Add Service Logging
```typescript
// In service methods
static async getData(filters: any) {
  console.log('Service called with filters:', filters);
  
  try {
    const response = await api.get('/endpoint', { params: filters });
    console.log('Service response:', response);
    return response;
  } catch (error) {
    console.error('Service error:', error);
    throw error;
  }
}
```

#### API Client Debugging
```typescript
// Enable detailed API logging
const api = new ApiClient(baseURL);

// Check cache state
console.log('API cache:', api.cache);

// Clear cache for testing
api.clearCache();
```

### Component Debugging

#### State Debugging
```typescript
// Track state changes
const [data, setData] = useState(null);

useEffect(() => {
  console.log('Data state changed:', data);
}, [data]);
```

#### Props Debugging
```typescript
// Log props changes
useEffect(() => {
  console.log('Props changed:', { prop1, prop2, prop3 });
}, [prop1, prop2, prop3]);
```

## Common Error Messages & Solutions

### "Cannot read property 'x' of undefined"
**Cause:** Accessing property on null/undefined object
**Solution:** Add null checks or optional chaining
```typescript
// ✅ Good
const name = user?.profile?.name || 'Unknown';

// ❌ Bad
const name = user.profile.name;
```

### "Invalid hook call"
**Cause:** Hooks called outside React component
**Solution:** Ensure hooks are called at component top level
```typescript
// ✅ Good
function MyComponent() {
  const [state, setState] = useState(null);
  // ...
}

// ❌ Bad
function MyComponent() {
  if (condition) {
    const [state, setState] = useState(null); // Hook in conditional
  }
}
```

### "Module not found"
**Cause:** Incorrect import paths
**Solution:** Verify file exists and path is correct
```typescript
// Check if file exists
ls src/components/MyComponent.tsx

// Fix import path
import { MyComponent } from '@/components/MyComponent';
```

## Environment-Specific Issues

### Development Environment
- **Port conflicts:** Change port in `vite.config.ts`
- **Environment variables:** Must start with `VITE_`
- **Cache issues:** Clear browser cache and restart server

### Production Environment
- **Build errors:** Check for development-only imports
- **Environment variables:** Set production values
- **HTTPS issues:** Verify SSL certificate configuration

## Getting Help

### Debug Information to Collect
When reporting issues, include:
1. **Error message** (exact text)
2. **Browser console logs** (errors and warnings)
3. **Network requests** (failed API calls)
4. **Environment details** (browser, OS, versions)
5. **Steps to reproduce** (specific actions taken)

### Debug Commands
```bash
# Check versions
npm ls react react-dom typescript

# Check build output
npm run build

# Check bundle size
npm run build && ls -la dist/

# Check for TypeScript errors
npx tsc --noEmit

# Check for linting errors
npm run lint
```

### Useful Browser Console Commands
```javascript
// Check React version
React.version

// Check current user
auth.currentUser

// Check store state
useAuthStore.getState()

// Clear all storage
localStorage.clear()
sessionStorage.clear()
```

## Prevention Tips

### Code Quality
1. **Use TypeScript strictly** - Enable strict mode
2. **Add error boundaries** - Catch component errors
3. **Implement proper loading states** - Show loading indicators
4. **Handle errors gracefully** - User-friendly error messages
5. **Use consistent patterns** - Follow established conventions

### Testing
1. **Write unit tests** - Test component logic
2. **Test error scenarios** - Test error handling
3. **Test edge cases** - Empty data, long strings, etc.
4. **Test user interactions** - Click handlers, form submissions

### Monitoring
1. **Add logging** - Log important operations
2. **Monitor performance** - Track slow operations
3. **Track errors** - Use error tracking service
4. **Monitor API calls** - Track failed requests

## Related Documentation

- **API Reference:** `docs/API_REFERENCE.md` - API-specific troubleshooting
- **Service Layer Guide:** `docs/SERVICES_GUIDE.md` - Service debugging
- **Component Architecture:** `docs/COMPONENT_ARCHITECTURE.md` - Component best practices
- **Development Guidelines:** `docs/DEVELOPMENT_GUIDELINES.md` - Development standards