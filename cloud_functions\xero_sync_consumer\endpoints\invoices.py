"""
Invoices Endpoint Handler

Handles Invoices (ACCREC) synchronization from Xero with amount filtering.
Extracted from main.py for better modularity.
"""

import logging
from typing import List
from datetime import datetime, timezone

from context import SyncContext
from utils.sync_helpers import _create_audit_log_entry, _update_firestore_sync_timestamp, _update_entity_settings
from utils.bills_processor import _check_if_current_version_exists

logger = logging.getLogger(__name__)

__all__ = ["sync_invoices"]


async def sync_invoices(ctx: SyncContext, requested_endpoints: List[str]) -> int:
    """
    Handle Invoices synchronization with amount threshold filtering.
    
    Args:
        ctx: Sync context containing all dependencies
        requested_endpoints: List of requested endpoints for sync
        
    Returns:
        Number of invoices processed
        
    Raises:
        Exception: If invoices sync fails
    """
    if "Invoices" not in requested_endpoints and requested_endpoints:
        return 0
    
    try:
        logger.info(f"Starting Invoices sync for entity_id: {ctx.entity_id}")
        
        full_sync = "Invoices" in ctx.force_full_sync_endpoints
        last_sync_timestamp_utc_str = None
        transaction_sync_start_date = ctx.message_data.get("transactionSyncStartDate")
        
        # Get last sync timestamp for incremental sync
        sync_ts_field = "_system_lastSyncTimestampUtc_Invoices"
        last_sync_timestamp_utc_str = ctx.get_entity_setting(sync_ts_field) if not full_sync else None
        
        # Build where filter for ACCREC invoices (customer invoices)
        where_filter = 'Type=="ACCREC" AND Status!="VOIDED" AND Status!="DELETED"'
        
        # Add amount threshold filter for performance optimization
        scanning_threshold = ctx.get_scanning_threshold()
        previous_threshold = ctx.get_entity_setting("_last_amount_filter_used")
        
        # Check for threshold changes requiring backfill
        threshold_updates = {"_last_amount_filter_used": scanning_threshold}
        
        if previous_threshold is not None and previous_threshold != scanning_threshold:
            logger.warning(f"Scanning threshold changed from {previous_threshold} to {scanning_threshold} for entity {ctx.entity_id}")
            logger.warning(f"Historical backfill may be required for transactions between ${previous_threshold} and ${scanning_threshold}")
            # TODO: Implement automatic backfill trigger in future iteration
            threshold_updates["_threshold_change_detected"] = {
                "previous": previous_threshold,
                "current": scanning_threshold,
                "detected_at": datetime.now().isoformat(),
                "backfill_required": True
            }
            
            # Persist threshold change detection
            await _update_entity_settings(
                ctx.db, ctx.entity_id, threshold_updates, 
                f"Threshold changed from {previous_threshold} to {scanning_threshold}"
            )
        else:
            # Just update the tracking field
            await _update_entity_settings(
                ctx.db, ctx.entity_id, threshold_updates,
                f"Updated amount filter tracking to {scanning_threshold}"
            )
        
        # Add amount filter to where clause
        where_filter += f' AND Total>={scanning_threshold}'
        
        # Handle date-based filtering if provided
        use_date_filter = False
        if transaction_sync_start_date and not full_sync:
            where_filter += f' AND Date>=DateTime({transaction_sync_start_date})'
            use_date_filter = True
            logger.info(f"Using date-based filter: Date>={transaction_sync_start_date}")
        
        logger.info(f"Fetching ACCREC invoices with filter: {where_filter}")
        invoices_data = await ctx.xero_client.get_records(
            "Invoices", 
            where_filter=where_filter,
            if_modified_since=last_sync_timestamp_utc_str if not use_date_filter else None
        )
        
        saved_invoices_count = 0
        skipped_invoices_count = 0
        
        # Initialize financial document adapter
        from utils.financial_doc_adapter import FinancialDocAdapter
        doc_adapter = FinancialDocAdapter(entity_id=ctx.entity_id)
        
        # Process invoices in batches
        batch = ctx.db.batch()
        batch_count = 0
        
        for invoice in invoices_data:
            invoice_id = invoice.get("InvoiceID")
            if not invoice_id:
                logger.warning(f"Invoice data missing InvoiceID for entity {ctx.entity_id}. Skipping: {invoice}")
                continue
            
            # CHECK IF WE ALREADY HAVE THE CURRENT VERSION - SKIP PROCESSING IF SO
            new_updated_at = invoice.get("UpdatedDateUTC")
            if await _check_if_current_version_exists(ctx.db, invoice_id, new_updated_at, ctx.entity_id, "TRANSACTIONS", "invoice"):
                skipped_invoices_count += 1
                continue  # Skip all processing for this invoice - we already have current version
            
            # Transform invoice using adapter
            financial_doc = doc_adapter.to_financial_document(invoice, "invoice")
            
            # Store in Firestore with document ID = InvoiceID for easy lookup
            doc_ref = ctx.db.collection("TRANSACTIONS").document(invoice_id)
            batch.set(doc_ref, financial_doc, merge=True)
            saved_invoices_count += 1
            batch_count += 1
            
            # Commit batch when it reaches size limit
            if batch_count >= ctx.get_entity_setting("batch_size", 50):
                await batch.commit()
                batch = ctx.db.batch()  # Reset batch
                batch_count = 0
        
        # Commit any remaining documents in the batch
        if batch_count > 0:
            await batch.commit()
        
        logger.info(f"Invoices sync completed for entity_id: {ctx.entity_id} - Processed: {saved_invoices_count}, Skipped: {skipped_invoices_count}")
        await _update_firestore_sync_timestamp(
            ctx.db, ctx.entity_id, "Invoices", datetime.now(timezone.utc).isoformat(), "Customer invoices sync successful"
        )
        await _create_audit_log_entry(
            ctx.db, "SYNC", "INVOICES_SYNC_SUCCESS", ctx.client_id, ctx.entity_id, "SUCCESS",
            {"processedCount": saved_invoices_count, "skippedCount": skipped_invoices_count, "syncJobId": ctx.sync_job_id}
        )
        
        return saved_invoices_count
        
    except Exception as e:
        logger.error(f"Invoices sync failed for entity_id: {ctx.entity_id}: {e}", exc_info=True)
        await _create_audit_log_entry(
            ctx.db, "SYNC", "INVOICES_SYNC_FAILURE", ctx.client_id, ctx.entity_id, "FAILURE",
            {"error": str(e), "syncJobId": ctx.sync_job_id}
        )
        raise