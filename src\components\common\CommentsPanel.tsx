import React, { useState, useEffect, useRef } from 'react';
import { 
  CommentsService, 
  ParentType
} from '@/services/comments.service';
import type { 
  CommentOut, 
  CommentCreate, 
  CommentUpdate, 
  CommentFilters 
} from '@/services/comments.service';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Avatar } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { AlertDialog, AlertDialogContent, AlertDialogHeader, AlertDialogTitle, AlertDialogDescription, AlertDialogFooter, AlertDialogCancel, AlertDialogAction } from '@/components/ui/alert-dialog';
import { Command, CommandInput, CommandList, CommandItem } from '@/components/ui/command';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { MessageSquare, Send, Edit2, Trash2, Search, AtSign, X } from 'lucide-react';
import { useAuthStore } from '@/store/auth.store';
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';

interface CommentsPanelProps {
  parentType: ParentType;
  parentId: string;
  onClose?: () => void;
  height?: number | string;
  enableMentions?: boolean;
  className?: string;
}

interface MentionUser {
  uid: string;
  display_name: string;
  email: string;
}

export const CommentsPanel: React.FC<CommentsPanelProps> = ({
  parentType,
  parentId,
  onClose,
  height = 600,
  enableMentions = true,
  className = ''
}) => {
  // State
  const [comments, setComments] = useState<CommentOut[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [newCommentText, setNewCommentText] = useState('');
  const [editingComment, setEditingComment] = useState<string | null>(null);
  const [editText, setEditText] = useState('');
  const [deleteConfirmComment, setDeleteConfirmComment] = useState<string | null>(null);
  const [searchText, setSearchText] = useState('');
  const [mentionSearch, setMentionSearch] = useState('');
  const [mentionUsers, setMentionUsers] = useState<MentionUser[]>([]);
  const [showMentionPopover, setShowMentionPopover] = useState(false);
  const [mentionStartPos, setMentionStartPos] = useState(0);
  const [currentCursorPos, setCurrentCursorPos] = useState(0);
  const [popoverPosition, setPopoverPosition] = useState({ top: 0, left: 0 });
  const [selectedMentionIndex, setSelectedMentionIndex] = useState(0);
  const [selectedMentions, setSelectedMentions] = useState<MentionUser[]>([]);
  // Keep a ref in sync so we can read the latest mentions synchronously (e.g., right after selection)
  const selectedMentionsRef = useRef<MentionUser[]>([]);

  // Whenever selectedMentions state changes, mirror it into the ref
  useEffect(() => {
    selectedMentionsRef.current = selectedMentions;
  }, [selectedMentions]);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  
  // Refs
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const scrollRef = useRef<HTMLDivElement>(null);
  const skipNextDetection = useRef(false);
  const skipTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // Auth
  const { user } = useAuthStore();
  
  // Effects
  useEffect(() => {
    loadComments();
  }, [parentType, parentId, page, searchText]);
  
  useEffect(() => {
    if (mentionSearch.length >= 2) {
      searchMentionUsers();
    } else {
      setMentionUsers([]);
      setShowMentionPopover(false);
    }
  }, [mentionSearch]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (skipTimeoutRef.current) {
        clearTimeout(skipTimeoutRef.current);
      }
    };
  }, []);
  
  // Load comments
  const loadComments = async () => {
    try {
      setLoading(true);
      
      const filters: CommentFilters = {};
      if (searchText) {
        filters.search_text = searchText;
      }
      
      const response = await CommentsService.getCommentsForRecord(
        parentType,
        parentId,
        { page, limit: 25, filters }
      );
      
      setComments(response.comments);
      setTotalPages(response.pages);
      
    } catch (error: any) {
      console.error('Failed to load comments:', error);
      toast.error(error.message || 'Failed to load comments');
    } finally {
      setLoading(false);
    }
  };
  
  // Search mention users
  const searchMentionUsers = async () => {
    try {
      const users = await CommentsService.searchUsersForMention(mentionSearch);
      setMentionUsers(users);
      setShowMentionPopover(users.length > 0);
    } catch (error) {
      console.error('Failed to search mention users:', error);
      setMentionUsers([]);
      setShowMentionPopover(false);
    }
  };
  
  // Handle comment submission
  const handleSubmitComment = async () => {
    if (!newCommentText.trim()) return;
    
    try {
      setSubmitting(true);
      
      const commentData: CommentCreate = {
        parent_type: parentType,
        parent_id: parentId,
        text: newCommentText.trim(),
        // Read from ref to ensure we capture mentions even if state hasn’t flushed yet
        mention_uids: selectedMentionsRef.current.map(m => m.uid)
      };
      
      await CommentsService.createComment(commentData);
      
      setNewCommentText('');
      setSelectedMentions([]);
      await loadComments();
      
      // Scroll to bottom to show new comment
      scrollToEnd();
      
      toast.success('Comment added successfully');
      
    } catch (error: any) {
      console.error('Failed to create comment:', error);
      toast.error(error.message || 'Failed to add comment');
    } finally {
      setSubmitting(false);
    }
  };
  
  // Handle comment edit
  const handleEditComment = async (commentId: string) => {
    if (!editText.trim()) return;
    
    try {
      const updateData: CommentUpdate = {
        text: editText.trim(),
        mention_uids: selectedMentions.map(m => m.uid)
      };
      
      await CommentsService.updateComment(commentId, updateData);
      
      setEditingComment(null);
      setEditText('');
      setSelectedMentions([]);
      await loadComments();
      
      toast.success('Comment updated successfully');
      
    } catch (error: any) {
      console.error('Failed to update comment:', error);
      toast.error(error.message || 'Failed to update comment');
    }
  };
  
  // Handle comment delete
  const handleDeleteComment = async (commentId: string) => {
    try {
      await CommentsService.deleteComment(commentId);
      
      setDeleteConfirmComment(null);
      await loadComments();
      
      toast.success('Comment deleted successfully');
      
    } catch (error: any) {
      console.error('Failed to delete comment:', error);
      toast.error(error.message || 'Failed to delete comment');
    }
  };
  
  // Handle mention selection
  const handleMentionSelect = (user: MentionUser) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    const currentText = editingComment ? editText : newCommentText;

    // Calculate the exact end position of the current @mention text
    const mentionEndPos = mentionStartPos + 1 + mentionSearch.length; // +1 for @, +mentionSearch.length for the typed text

    // Replace the @search text with @username
    const beforeMention = currentText.slice(0, mentionStartPos);
    const afterMention = currentText.slice(mentionEndPos);
    // Add a trailing space so the mention is immediately terminated for detection logic
    const mentionText = `@${user.display_name} `;
    const newText = beforeMention + mentionText + afterMention;

    // Add to selected mentions if not already there
    if (!selectedMentions.find(m => m.uid === user.uid)) {
      setSelectedMentions([...selectedMentions, user]);
    }

    // Close popover and reset search
    setShowMentionPopover(false);
    setMentionSearch('');

    // Set flag to skip mention detection - use a longer delay to ensure cursor positioning completes
    skipNextDetection.current = true;

    // Update the text (this will trigger handleTextareaChange)
    if (editingComment) {
      setEditText(newText);
    } else {
      setNewCommentText(newText);
    }

    // Clear any existing timeout
    if (skipTimeoutRef.current) {
      clearTimeout(skipTimeoutRef.current);
    }

    // Set cursor position after the mention and reset skip flag after positioning
    setTimeout(() => {
      const newCursorPos = mentionStartPos + mentionText.length;
      textarea.setSelectionRange(newCursorPos, newCursorPos);
      textarea.focus();

      // Reset the skip flag after cursor positioning is complete
      // This ensures the flag is only consumed after the DOM is properly updated
      skipTimeoutRef.current = setTimeout(() => {
        skipNextDetection.current = false;
        skipTimeoutRef.current = null;
      }, 50); // Increased timeout to ensure DOM updates complete
    }, 0);
  };
  
  // Remove mention (when user manually deletes @username from text)
  const removeMention = (uid: string) => {
    setSelectedMentions(selectedMentions.filter(m => m.uid !== uid));
  };
  
  // Handle textarea key events
  const handleTextareaKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Handle mention popover navigation
    if (showMentionPopover && mentionUsers.length > 0) {
      if (e.key === 'Escape') {
        e.preventDefault();
        setShowMentionPopover(false);
        setMentionSearch('');
        setSelectedMentionIndex(0);
        return;
      }
      
      if (e.key === 'ArrowDown' || e.key === 'Tab') {
        e.preventDefault();
        setSelectedMentionIndex(prev => 
          prev < mentionUsers.length - 1 ? prev + 1 : 0
        );
        return;
      }
      
      if (e.key === 'ArrowUp') {
        e.preventDefault();
        setSelectedMentionIndex(prev => 
          prev > 0 ? prev - 1 : mentionUsers.length - 1
        );
        return;
      }
      
      if (e.key === 'Enter') {
        e.preventDefault();
        if (mentionUsers[selectedMentionIndex]) {
          handleMentionSelect(mentionUsers[selectedMentionIndex]);
        }
        return;
      }
    }
    
    // Handle submit
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      if (editingComment) {
        handleEditComment(editingComment);
      } else {
        handleSubmitComment();
      }
    }
  };
  
  // Handle textarea change for mention detection
  const handleTextareaChange = (value: string) => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    // Update text state first
    if (editingComment) {
      setEditText(value);
    } else {
      setNewCommentText(value);
    }

    if (!enableMentions) return;

    // Skip mention detection if we just selected a mention
    // Don't reset the flag here - let the timeout in handleMentionSelect manage it
    if (skipNextDetection.current) {
      return;
    }

    const cursorPos = textarea.selectionStart;
    setCurrentCursorPos(cursorPos);

    // Find if cursor is in an active mention
    const textBeforeCursor = value.slice(0, cursorPos);
    const lastAtIndex = textBeforeCursor.lastIndexOf('@');

    if (lastAtIndex !== -1) {
      // Check if there's a space between @ and cursor (which would end the mention)
      const textAfterAt = textBeforeCursor.slice(lastAtIndex + 1);

      // A mention candidate is valid only if it contains NO whitespace or punctuation
      const invalidCharPattern = /[\s\.,;:!\?\(\)\[\]\{\}]/;
      if (
        textAfterAt.length > 0 &&
        textAfterAt.length <= 30 &&
        !invalidCharPattern.test(textAfterAt)
      ) {
        // Active mention detected
        setMentionStartPos(lastAtIndex);
        setMentionSearch(textAfterAt);

        if (textAfterAt.length >= 1) { // Start searching after 1 character
          setShowMentionPopover(true);
          setSelectedMentionIndex(0); // Reset selection
          // Calculate position near cursor
          const position = calculateCursorPosition(textarea, cursorPos);
          setPopoverPosition(position);
        } else {
          setShowMentionPopover(false);
        }
      } else {
        // Not in active mention
        setShowMentionPopover(false);
        setMentionSearch('');
      }
    } else {
      // No @ found before cursor
      setShowMentionPopover(false);
      setMentionSearch('');
    }

    // Update selected mentions based on what's actually in the text
    updateMentionsFromText(value);
  };
  
  // Update mentions list based on @mentions actually present in text
  const updateMentionsFromText = (text: string) => {
    // Match @mentions that end at whitespace, punctuation, or end of line so that
    // we don’t accidentally include trailing words like "test" after the mention.
    const mentionMatches = text.match(/@(\w+(?:\s+\w+)*)(?=\s|$|[.,!?])/g) || [];
    const textMentions = mentionMatches.map(match => match.slice(1)); // Remove @
    
    // Keep only mentions that are still in the text
    setSelectedMentions(prev => 
      prev.filter(mention => 
        textMentions.some(textMention => 
          mention.display_name === textMention
        )
      )
    );
  };
  
  // Format comment text with styled @mentions using mention_mapping
  const formatCommentTextWithMentions = (text: string, mentionMapping: { [uid: string]: string } = {}) => {
    // If no mention mapping, return plain text
    if (!mentionMapping || Object.keys(mentionMapping).length === 0) {
      return text;
    }
    
    const parts = [];
    let lastIndex = 0;
    
    // Get all display names from the mapping
    const displayNames = Object.values(mentionMapping);
    
    // Create regex to match any of the actual display names in the text
    // Sort by length (longest first) to avoid partial matches
    const sortedNames = displayNames.sort((a, b) => b.length - a.length);
    
    // Escape special regex characters in names and create pattern
    const escapedNames = sortedNames.map(name => 
      name.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    );
    
    if (escapedNames.length === 0) {
      return text;
    }
    
    const mentionRegex = new RegExp(`@(${escapedNames.join('|')})(?=\\s|$|[.!?])`, 'g');
    let match;
    
    while ((match = mentionRegex.exec(text)) !== null) {
      // Add text before the mention
      if (match.index > lastIndex) {
        parts.push(text.slice(lastIndex, match.index));
      }
      
      // Add the styled mention
      parts.push(
        <span
          key={`mention-${match.index}`}
          className="inline-flex items-center px-1.5 py-0.5 rounded-md text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200"
        >
          <AtSign className="h-3 w-3 mr-0.5" />
          {match[1]}
        </span>
      );
      
      lastIndex = match.index + match[0].length;
    }
    
    // Add remaining text
    if (lastIndex < text.length) {
      parts.push(text.slice(lastIndex));
    }
    
    return parts.length > 0 ? parts : text;
  };
  
  // Calculate cursor position for popover
  const calculateCursorPosition = (textarea: HTMLTextAreaElement, cursorPos: number) => {
    // Create a mirror div to measure text
    const mirror = document.createElement('div');
    const computedStyle = window.getComputedStyle(textarea);
    
    // Copy all relevant styles
    [
      'fontFamily', 'fontSize', 'fontWeight', 'lineHeight', 'letterSpacing',
      'paddingTop', 'paddingLeft', 'paddingRight', 'paddingBottom',
      'borderTopWidth', 'borderLeftWidth', 'borderRightWidth', 'borderBottomWidth',
      'boxSizing', 'whiteSpace', 'wordWrap', 'wordBreak'
    ].forEach(prop => {
      mirror.style[prop as any] = computedStyle[prop as any];
    });
    
    mirror.style.position = 'absolute';
    mirror.style.visibility = 'hidden';
    mirror.style.width = textarea.offsetWidth + 'px';
    mirror.style.height = 'auto';
    mirror.style.top = '-9999px';
    mirror.style.left = '-9999px';
    
    document.body.appendChild(mirror);
    
    // Get text up to cursor
    const textBeforeCursor = textarea.value.substring(0, cursorPos);
    mirror.textContent = textBeforeCursor;
    
    // Add a span at the cursor position to measure
    const span = document.createElement('span');
    span.textContent = '|';
    mirror.appendChild(span);
    
    // Get position relative to textarea
    const textareaRect = textarea.getBoundingClientRect();
    const spanRect = span.getBoundingClientRect();
    const mirrorRect = mirror.getBoundingClientRect();
    
    // Calculate position
    const top = textareaRect.top + (spanRect.top - mirrorRect.top) + 25; // Add line height
    const left = textareaRect.left + (spanRect.left - mirrorRect.left);
    
    document.body.removeChild(mirror);
    
    return { top, left };
  };
  
  // Scroll to end
  const scrollToEnd = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  };
  
  // Format comment timestamp
  const formatTimestamp = (timestamp: string) => {
    try {
      return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
    } catch {
      return 'Unknown time';
    }
  };
  
  return (
    <Card className={`flex flex-col ${className}`} style={{ height }}>
      <CardHeader className="flex-shrink-0 pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Comments ({comments.length})
          </CardTitle>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
        
        {/* Search */}
        <div className="flex items-center gap-2 mt-3">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search comments..."
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              className="pl-9"
            />
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="flex-1 flex flex-col gap-4 overflow-hidden">
        {/* Comments List */}
        <ScrollArea className="flex-1" ref={scrollRef}>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <LoadingSpinner />
            </div>
          ) : comments.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              {searchText ? 'No comments found matching your search.' : 'No comments yet. Be the first to comment!'}
            </div>
          ) : (
            <div className="space-y-4">
              {comments.map((comment) => (
                <div key={comment.comment_id} className="flex gap-3">
                  <Avatar className="h-8 w-8 flex-shrink-0">
                    <div className="h-full w-full bg-primary/10 flex items-center justify-center text-sm font-medium">
                      {comment.author_display_name?.charAt(0) || '?'}
                    </div>
                  </Avatar>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-sm">
                        {comment.author_display_name || 'Unknown User'}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {formatTimestamp(comment.created_at)}
                      </span>
                      {comment.updated_at && (
                        <Badge variant="secondary" className="text-xs">
                          edited
                        </Badge>
                      )}
                    </div>
                    
                    {editingComment === comment.comment_id ? (
                      <div className="space-y-2">
                        <div className="relative">
                          <Textarea
                            value={editText}
                            onChange={(e) => handleTextareaChange(e.target.value)}
                            onKeyDown={handleTextareaKeyDown}
                            placeholder="Edit your comment..."
                            className="min-h-[80px]"
                          />
                          
                        </div>
                        
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            onClick={() => handleEditComment(comment.comment_id)}
                            disabled={!editText.trim()}
                          >
                            Save
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setEditingComment(null);
                              setEditText('');
                              setSelectedMentions([]);
                            }}
                          >
                            Cancel
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <>
                        <div className="text-sm whitespace-pre-wrap break-words">
                          {formatCommentTextWithMentions(comment.text, comment.mention_mapping)}
                        </div>
                        
                        {/* Mentions */}
                        {comment.mentions.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {comment.mentions.map((mentionUid) => (
                              <Badge key={mentionUid} variant="outline" className="text-xs">
                                <AtSign className="h-3 w-3 mr-1" />
                                mentioned user
                              </Badge>
                            ))}
                          </div>
                        )}
                        
                        {/* Actions */}
                        {comment.created_by === user?.uid && (
                          <div className="flex gap-1 mt-2">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => {
                                      setEditingComment(comment.comment_id);
                                      setEditText(comment.text);
                                      // TODO: Load existing mentions
                                    }}
                                  >
                                    <Edit2 className="h-3 w-3" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>Edit comment</TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => setDeleteConfirmComment(comment.comment_id)}
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>Delete comment</TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
        
        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
            >
              Previous
            </Button>
            <span className="text-sm text-muted-foreground">
              Page {page} of {totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page + 1)}
              disabled={page === totalPages}
            >
              Next
            </Button>
          </div>
        )}
        
        <Separator />
        
        {/* New Comment */}
        <div className="space-y-3">
          <div className="relative">
            <Textarea
              ref={textareaRef}
              value={newCommentText}
              onChange={(e) => handleTextareaChange(e.target.value)}
              onKeyDown={handleTextareaKeyDown}
              placeholder={enableMentions ? "Write a comment... Use @ to mention users" : "Write a comment..."}
              className="min-h-[80px] resize-none"
            />
            
          </div>
          
          <div className="flex justify-between items-center">
            <div className="text-xs text-muted-foreground">
              {enableMentions && "Tip: Press @ to mention users, Ctrl+Enter to submit"}
            </div>
            <Button
              onClick={handleSubmitComment}
              disabled={!newCommentText.trim() || submitting}
              size="sm"
              className="gap-2"
            >
              {submitting ? <LoadingSpinner /> : <Send className="h-4 w-4" />}
              {submitting ? 'Posting...' : 'Post Comment'}
            </Button>
          </div>
        </div>
      </CardContent>
      
      {/* Global Mention Dropdown - positioned at cursor */}
      {showMentionPopover && enableMentions && mentionUsers.length > 0 && (
        <div 
          className="fixed z-50 w-64 bg-white border border-gray-200 rounded-md shadow-lg"
          style={{ 
            top: `${popoverPosition.top}px`, 
            left: `${popoverPosition.left}px` 
          }}
        >
          <Command>
            <CommandList className="max-h-48">
              {mentionUsers.map((user, index) => (
                <CommandItem
                  key={user.uid}
                  onSelect={() => handleMentionSelect(user)}
                  className={`cursor-pointer px-3 py-2 ${
                    index === selectedMentionIndex 
                      ? 'bg-blue-100 text-blue-900' 
                      : 'hover:bg-gray-100'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <Avatar className="h-6 w-6">
                      <div className="h-full w-full bg-primary/10 flex items-center justify-center text-xs">
                        {user.display_name?.charAt(0) || '?'}
                      </div>
                    </Avatar>
                    <div>
                      <div className="font-medium text-sm">{user.display_name}</div>
                      <div className="text-xs text-muted-foreground">{user.email}</div>
                    </div>
                  </div>
                </CommandItem>
              ))}
            </CommandList>
          </Command>
        </div>
      )}
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deleteConfirmComment} onOpenChange={() => setDeleteConfirmComment(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Comment</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this comment? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deleteConfirmComment && handleDeleteComment(deleteConfirmComment)}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
};