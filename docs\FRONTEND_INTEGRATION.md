# Backend-Frontend Integration Reference

**Quick reference for backend developers working with the DRCR frontend**
*Part of the DRCR full-stack documentation system*

## Frontend System Overview

**Location**: `/mnt/d/Projects/drcr_front/`
**Technology**: React 18.3.1 + TypeScript + Vite
**Local URL**: `http://localhost:5173`
**Start Command**: `cd /mnt/d/Projects/drcr_front && npm run dev`

## Critical API Integration Points

### Frontend Expects Backend on Port 8081
```typescript
// Frontend configuration (development)
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081';

// CORS Configuration needed in backend
allow_origins=[
  "http://localhost:5173",  // Vite dev server
  "https://drcr-d660a.web.app",  // Production
]
```

### Authentication Flow
```typescript
// Frontend → Backend auth flow
1. Firebase Auth login → Get ID token
2. Include in requests: Authorization: Bearer ${idToken}
3. Backend validates token → Returns user data
```

## Frontend Data Expectations

### Entity Data Structure
```typescript
// Frontend expects this interface
interface EntitySummary {
  entity_id: string;
  entity_name: string;        // NOT "name"
  type: 'xero' | 'qbo' | 'manual';
  connection_status: 'connected' | 'error' | 'disconnected';  // Transformed!
  last_sync?: string;
  error_message?: string;
}

// Backend should return
{
  "entity_id": "uuid",
  "entity_name": "Company Name",  // Use entity_name, not name
  "type": "xero",
  "status": "active",
  "connection_details": {
    "status": "active",        // Frontend transforms to connection_status
    "xero_tenant_id": "uuid",
    "last_sync": "2025-01-15T10:30:00Z"
  }
}
```

### Schedule Status System
```typescript
// Frontend recognizes these statuses
type ScheduleStatus = 
  | "pending_configuration"  // Shows "Configure" button
  | "pending_confirmation"   // Shows "Confirm" button
  | "proposed"              // Shows "Confirm" button (legacy)
  | "confirmed"             // Shows "Post" button
  | "posted"                // Shows "View" button
  | "cancelled"             // Shows disabled state
  | "error";                // Shows error state

// Backend MUST return exact string matches
```

### Client Summary Data
```typescript
// Frontend expects from GET /clients/summary
interface ClientSummary {
  client_id: string;
  name: string;              // NOT "client_name"
  status: string;
  entities?: EntitySummary[];
  pending_items_count?: number;
  error_count?: number;
  schedule_status_counts?: {
    pending_configuration?: number;
    pending_confirmation?: number;
    confirmed?: number;
    posted?: number;
  };
}
```

## Key Frontend Service Patterns

### Service Layer Architecture
Frontend uses 9 domain services that make API calls:

```typescript
// Service structure
class ServiceName {
  private api: AxiosInstance;
  
  async methodName(params: RequestType): Promise<ResponseType> {
    try {
      const response = await this.api.post('/endpoint', params);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Request failed');
    }
  }
}
```

### Critical Services & Their Endpoints
```typescript
// PrepaymentsService (main feature)
GET /transactions/dashboard     // Dashboard data
POST /schedules/calculate-preview // Amortization preview
POST /schedules/{id}/confirm   // Confirm schedule
POST /schedules/{id}/entries/bulk-post // Post to Xero

// ClientsService  
GET /clients/summary          // Main client list
GET /clients/{id}            // Client details
POST /clients/wizard         // Client creation

// EntitiesService
GET /entities/               // Entity list
GET /entities/{id}/accounts  // Chart of accounts
POST /entities/{id}/sync/trigger // Trigger sync
```

## Frontend Component Patterns

### Loading States
Frontend shows loading indicators for ALL async operations:
```typescript
// Backend should respond quickly or provide progress
// Long operations should use progress tracking endpoints
GET /sync/status/{syncJobId}  // For progress updates
```

### Error Handling
```typescript
// Frontend expects consistent error format
{
  "detail": "User-friendly error message",
  "status_code": 400,
  "timestamp": **********
}

// Frontend displays error.response.data.detail to user
```

### Form Validation
```typescript
// Frontend uses Zod schemas for validation
// Backend Pydantic models should match frontend expectations
// Return 422 for validation errors with field-specific details
```

## Critical API Behaviors

### Pagination
```typescript
// Frontend expects consistent pagination
interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    total: number;
    page: number;
    per_page: number;
    pages: number;
  };
}

// Frontend calls with: ?limit=25&offset=0
```

### Field Naming Convention
```typescript
// Backend should return snake_case
{
  "entity_id": "uuid",
  "entity_name": "Company",
  "created_at": "2025-01-15T10:30:00Z"
}

// Frontend services handle camelCase transformation
{
  entityId: "uuid",
  entityName: "Company", 
  createdAt: "2025-01-15T10:30:00Z"
}
```

### Status Transformations
```typescript
// Backend connection_details.status → Frontend connection_status
"active" → "connected"
"error" → "error"
"inactive" → "disconnected"
"disconnected" → "disconnected"
```

## Frontend Component Requirements

### Dashboard Components
```typescript
// PrepaymentsPage expects:
GET /transactions/dashboard → {
  transactions: Transaction[];
  pagination: PaginationInfo;
  summary: {
    total_count: number;
    status_counts: {
      pending_configuration: number;
      pending_confirmation: number;
      confirmed: number;
      posted: number;
    };
  };
}
```

### Schedule Components  
```typescript
// ScheduleCard expects:
GET /schedules/{id} → {
  schedule_id: string;
  status: ScheduleStatus;
  original_amount: number;
  monthly_entries: MonthlyEntry[];
  // ... other fields
}
```

### Entity Setup Components
```typescript
// EntitySetup expects:
GET /entities/{id}/accounts → Account[]
POST /entities/{id}/settings → EntitySettings
```

## Common Frontend Workflows

### 1. User Dashboard Load
```
Frontend: PrepaymentsPage.tsx loads
   ↓
Service: PrepaymentsService.getPrepaymentsData()
   ↓
API: GET /transactions/dashboard
   ↓
Backend: Returns paginated transactions with status counts
```

### 2. Schedule Confirmation
```
Frontend: ScheduleCard "Confirm" button clicked
   ↓
Service: PrepaymentsService.confirmSchedule(scheduleId)
   ↓
API: POST /schedules/{id}/confirm
   ↓
Backend: Updates schedule status to "confirmed"
   ↓
Frontend: Shows toast notification, updates UI
```

### 3. Xero Connection
```
Frontend: XeroConnectButton clicked
   ↓
Service: EntitiesService.startXeroConnection(clientId)
   ↓
API: GET /xero/connect/initiate/{clientId}
   ↓
Backend: Returns auth_url
   ↓
Frontend: Opens auth_url in popup
   ↓
Backend: Handles OAuth callback
   ↓
Frontend: Refreshes entity connection status
```

## Performance Expectations

### Response Times
- **Fast endpoints** (<100ms): /health, /auth/me
- **Medium endpoints** (<500ms): /clients/summary, /entities/{id}
- **Long operations** (>5s): Sync operations (use progress tracking)

### Caching
```typescript
// Frontend caches GET requests
// Backend should set appropriate cache headers
response.headers["Cache-Control"] = "public, max-age=60"  // 1 minute
```

## Error Scenarios Frontend Handles

### Authentication Errors
```typescript
// 401 Unauthorized → Frontend redirects to login
// 403 Forbidden → Frontend shows "Access Denied"
// Frontend refreshes Firebase tokens automatically
```

### Validation Errors
```typescript
// 422 Unprocessable Entity → Frontend shows field-specific errors
{
  "detail": "Validation failed",
  "errors": {
    "entity_name": ["This field is required"],
    "email": ["Invalid email format"]
  }
}
```

### Network Errors
```typescript
// Network failures → Frontend shows retry option
// Timeout errors → Frontend shows "Request timed out"
```

## Testing Integration

### Frontend Test Requirements
```typescript
// API mocking for frontend tests
// Backend should provide OpenAPI spec for mock generation
// Consistent response formats for reliable mocking
```

### Integration Test Patterns
```typescript
// Frontend runs integration tests against backend
// Backend should have stable test data
// Use test-specific endpoints for setup/teardown
```

## Common Integration Issues

### Field Naming Mismatches
❌ **Backend returns**: `{ "name": "Company" }`
✅ **Frontend expects**: `{ "entity_name": "Company" }`

### Status Value Mismatches
❌ **Backend returns**: `{ "status": "connected" }`
✅ **Frontend expects**: `{ "connection_details": { "status": "active" } }`

### Data Type Mismatches
❌ **Backend returns**: `{ "amount": "100.50" }` (string)
✅ **Frontend expects**: `{ "amount": 100.50 }` (number)

### URL Protocol Issues
❌ **Backend CORS**: Only allows HTTPS
✅ **Frontend dev needs**: HTTP localhost:5173

## Development Coordination

### When Adding New Endpoints
1. **Backend**: Create FastAPI endpoint with Pydantic models
2. **Backend**: Update `docs/BACKEND_REFERENCE.md`
3. **Frontend**: Add TypeScript interfaces
4. **Frontend**: Create service method  
5. **Frontend**: Update `docs/API_REFERENCE.md`
6. **Both**: Test integration end-to-end

### When Changing Data Structures
1. **Backend**: Update Pydantic models
2. **Backend**: Update database schema if needed
3. **Frontend**: Update TypeScript interfaces
4. **Frontend**: Update service transformations
5. **Both**: Test data compatibility

## Quick Development Commands

### Frontend Operations
```bash
# Start frontend
cd /mnt/d/Projects/drcr_front && npm run dev

# Run frontend tests  
cd /mnt/d/Projects/drcr_front && npm test

# Build frontend
cd /mnt/d/Projects/drcr_front && npm run build
```

### Integration Testing
```bash
# Start both systems
cd /mnt/d/Projects/drcr_back/rest_api && python run_server.py &
cd /mnt/d/Projects/drcr_front && npm run dev &

# Test API connectivity
curl -X GET http://localhost:8081/health
```

This reference enables efficient backend development with full frontend integration awareness.