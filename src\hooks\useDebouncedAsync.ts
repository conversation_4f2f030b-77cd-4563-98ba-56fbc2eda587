import { useRef, useCallback, useEffect } from 'react';

interface DebouncedAsyncOptions {
  delay?: number;
  leading?: boolean; // Fire immediately on first call, then debounce subsequent calls
}

/**
 * Hook for debouncing async functions with abort support
 * Reusable across components that need debounced API calls
 */
export function useDebouncedAsync<TArgs extends any[], TReturn>(
  asyncFn: (...args: TArgs) => Promise<TReturn>,
  options: DebouncedAsyncOptions = {}
) {
  const { delay = 300, leading = false } = options;
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const lastCallTimeRef = useRef<number>(0);

  const debouncedFn = useCallback(
    (...args: TArgs): Promise<TReturn> => {
      return new Promise((resolve, reject) => {
        const now = Date.now();
        const timeSinceLastCall = now - lastCallTimeRef.current;
        
        // Leading edge: fire immediately if enabled and enough time has passed
        if (leading && timeSinceLastCall >= delay) {
          lastCallTimeRef.current = now;
          
          // Abort any existing request
          if (abortControllerRef.current) {
            abortControllerRef.current.abort();
          }
          
          // Execute immediately
          (async () => {
            try {
              abortControllerRef.current = new AbortController();
              const result = await asyncFn(...args);
              resolve(result);
            } catch (error) {
              reject(error);
            } finally {
              abortControllerRef.current = null;
            }
          })();
          
          return;
        }

        // Clear existing timeout
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }

        // Abort existing request
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }

        // Set up new timeout
        timeoutRef.current = setTimeout(async () => {
          lastCallTimeRef.current = Date.now();
          
          try {
            // Create new abort controller for this request
            abortControllerRef.current = new AbortController();
            
            const result = await asyncFn(...args);
            resolve(result);
          } catch (error) {
            reject(error);
          } finally {
            abortControllerRef.current = null;
          }
        }, delay);
      });
    },
    [asyncFn, delay, leading]
  );

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    debouncedFn,
    cancel: () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }
    }
  };
}